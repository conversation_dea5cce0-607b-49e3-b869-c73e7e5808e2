import nextJest from 'next/jest'

const createJestConfig = nextJest({
  dir: './',
})

const config = {
  testEnvironment: 'jsdom',
  setupFiles: ['<rootDir>/tests/setup/polyfills.ts'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup/jest.setup.ts'],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  testMatch: ['<rootDir>/tests/**/*.test.(ts|tsx)'],
  transform: {},
  transformIgnorePatterns: [
    '/node_modules/(?!(jose|@supabase|isows)/)'
  ],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
  },
  collectCoverageFrom: [
    'app/api/**/*.(ts|tsx)',
    'components/**/*.(ts|tsx)',
    'services/**/*.(ts|tsx)',
    '!**/*.d.ts',
    '!**/node_modules/**',
  ],
  coverageDirectory: 'coverage',
}

export default createJestConfig(config)