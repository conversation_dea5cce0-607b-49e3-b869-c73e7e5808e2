{"scripts": {"dev": "next dev", "build": "next build", "start": "next start", "test": "jest", "test:watch": "jest --watch", "test:ci": "cross-env CI=true jest --coverage", "test:api": "jest tests/api --runInBand", "test:ui": "jest tests/components --runInBand", "e2e": "playwright test", "e2e:headed": "playwright test --headed", "e2e:report": "playwright show-report"}, "name": "jasz-ai-dashboard", "private": true, "devDependencies": {"@jest/types": "^30.0.5", "jest": "^30.0.5", "next": "^15.4.5", "node-fetch": "^3.3.2", "ts-node": "^10.9.2", "undici": "^7.13.0"}, "dependencies": {"@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.2.5", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.53.0", "axios": "^1.11.0", "date-fns": "^4.1.0", "mammoth": "^1.10.0", "openai": "^5.12.2", "pdf-parse": "^1.1.1", "react": "^19.1.1", "react-day-picker": "^9", "react-dom": "^19.1.1", "tw-animate-css": "^1.3.6"}}