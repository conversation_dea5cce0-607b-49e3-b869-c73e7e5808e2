export interface KnowledgeBase {
  id: string
  user_id: string
  created_at: string
  updated_at: string
  name: string
  description?: string
  status: 'active' | 'inactive'
  document_count?: number
}

export interface CreateKnowledgeBaseRequest {
  name: string
  description?: string
  status?: 'active' | 'inactive'
}

export interface UpdateKnowledgeBaseRequest {
  name?: string
  description?: string
  status?: 'active' | 'inactive'
}

export interface DataSource {
  id: string
  knowledge_base_id: string
  created_at: string
  updated_at: string
  name: string
  original_filename: string
  file_type: 'pdf' | 'docx' | 'txt' | 'md'
  file_size: number
  file_url: string
  status: 'uploading' | 'processing' | 'ready' | 'error'
  error_message?: string
  metadata: {
    title?: string
    author?: string
    created_date?: string
    page_count?: number
    word_count?: number
  }
  chunk_count?: number
}

export interface DocumentChunk {
  id: string
  data_source_id: string
  content: string
  embedding: number[]
  metadata: {
    chunk_index: number
    page?: number
    section?: string
    token_count: number
  }
  created_at: string
}

export interface RAGSearchResult {
  chunk_id: string
  content: string
  similarity_score: number
  metadata: {
    source_name: string
    page?: number
    section?: string
    document_type: string
  }
}

export interface RAGQueryResponse {
  chunks: RAGSearchResult[]
  context: string
  sources: DocumentSource[]
  total_tokens: number
}

export interface DocumentSource {
  id: string
  name: string
  type: string
  url?: string
}

// Agent-KnowledgeBase Verknüpfung
export interface AgentKnowledgeBase {
  id: string
  agent_id: string
  knowledge_base_id: string
  created_at: string
  priority: number
}

// RAG-Konfiguration pro Agent
export interface AgentRAGConfig {
  agent_id: string
  enabled: boolean
  similarity_threshold: number
  max_chunks: number
  chunk_overlap: boolean
  include_metadata: boolean
  source_attribution: boolean
}