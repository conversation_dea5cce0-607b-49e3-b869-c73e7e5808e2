// Call Handling Types
// Epic 6 Story 6.3: Anruf-Handling und -protokollierung

export interface CallLog {
  id: string
  phone_number_id: string
  agent_id: string
  user_id: string
  created_at: string
  updated_at: string
  
  // Provider integration
  provider_call_sid?: string
  
  // Caller information
  caller_number: string
  caller_name?: string
  caller_location?: string
  
  // Call timing
  start_time: string
  end_time?: string
  duration_seconds?: number
  
  // Call status
  status: 'ringing' | 'in_progress' | 'completed' | 'failed' | 'missed' | 'manual_takeover'
  end_reason?: 'caller_hangup' | 'agent_hangup' | 'system_error' | 'timeout' | 'manual_takeover' | 'transferred'
  
  // Quality and ratings
  success_rating?: number // 1-5
  caller_satisfaction?: number // 1-5
  
  // Recording and transcription
  recording_enabled: boolean
  recording_url?: string
  recording_duration_seconds?: number
  
  // Cost tracking
  cost_breakdown: CallCostBreakdown
  total_cost: number
  currency: string
  
  // Quality metrics
  quality_metrics: CallQualityMetrics
  
  // Additional metadata
  metadata: CallMetadata
  
  // Relations
  phone_number?: {
    id: string
    number: string
    friendly_name?: string
  }
  agent?: {
    id: string
    name: string
    description?: string
  }
}

export interface CallTranscriptEntry {
  id: string
  call_log_id: string
  created_at: string
  
  // Timing
  timestamp: string
  sequence_number: number
  
  // Speaker identification
  speaker: 'caller' | 'agent' | 'system'
  speaker_name?: string
  
  // Content
  text: string
  original_text?: string
  
  // Quality metrics
  confidence?: number // 0.00 to 1.00
  processing_time_ms?: number
  
  // AI processing metadata
  tokens_used: number
  model_used?: string
  
  // Event metadata for system entries
  event_type?: 'message' | 'tool_call' | 'system_event' | 'error'
  event_data: Record<string, any>
}

export interface CallSystemEvent {
  id: string
  call_log_id: string
  created_at: string
  
  // Event details
  event_type: 'call_started' | 'agent_joined' | 'recording_started' | 'tool_used' | 'manual_takeover' | 'transferred' | 'error'
  event_description: string
  
  // Event timing
  timestamp: string
  duration_ms?: number
  
  // Associated data
  event_data: Record<string, any>
  
  // Severity for alerts
  severity: 'info' | 'warning' | 'error' | 'critical'
}

export interface CallTakeoverSession {
  id: string
  call_log_id: string
  supervisor_user_id: string
  created_at: string
  updated_at: string
  
  // Session timing
  start_time: string
  end_time?: string
  duration_seconds?: number
  
  // Takeover details
  reason: string
  status: 'active' | 'completed' | 'aborted'
  
  // Notes and outcomes
  supervisor_notes?: string
  outcome?: string
  
  // Metadata
  metadata: Record<string, any>
  
  // Relations
  supervisor?: {
    id: string
    email: string
  }
}

export interface CallCostBreakdown {
  duration_cost: number
  recording_cost?: number
  transcription_cost?: number
  ai_processing_cost?: number
  speech_synthesis_cost?: number
  total_cost: number
  currency: string
  
  // Detailed breakdown
  minutes_billed?: number
  per_minute_rate?: number
  setup_fee?: number
  additional_fees?: Record<string, number>
}

export interface CallQualityMetrics {
  // Response time metrics
  average_response_time_ms: number
  max_response_time_ms?: number
  min_response_time_ms?: number
  
  // Speech recognition quality
  speech_recognition_accuracy?: number // 0.00 to 1.00
  average_confidence_score?: number // 0.00 to 1.00
  
  // Agent performance
  agent_response_quality?: number // 0.00 to 1.00
  successful_tool_calls?: number
  failed_tool_calls?: number
  
  // Call quality
  audio_quality_score?: number // 0.00 to 1.00
  connection_stability?: number // 0.00 to 1.00
  
  // User satisfaction
  caller_satisfaction?: number // 1-5
  resolution_achieved?: boolean
  
  // Technical issues
  technical_issues: string[]
  interruptions_count?: number
  silence_duration_ms?: number
}

export interface CallMetadata {
  // Technical metadata
  user_agent?: string
  call_quality_info?: Record<string, any>
  agent_version?: string
  api_version?: string
  
  // Processing stats
  processing_stats?: ProcessingStats
  
  // Business metadata
  call_category?: string
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  department?: string
  tags?: string[]
  
  // Integration metadata
  crm_contact_id?: string
  ticket_id?: string
  case_id?: string
  
  // Custom fields
  custom_fields?: Record<string, any>
}

export interface ProcessingStats {
  total_llm_tokens: number
  input_tokens?: number
  output_tokens?: number
  total_audio_seconds?: number
  speech_to_text_calls?: number
  text_to_speech_calls?: number
  average_latency_ms: number
  max_latency_ms?: number
  errors_encountered: number
  fallback_activations: number
  model_switches?: number
}

// API Request/Response types
export interface CreateCallLogRequest {
  phone_number_id: string
  agent_id: string
  caller_number: string
  caller_name?: string
  provider_call_sid?: string
  recording_enabled?: boolean
  metadata?: Partial<CallMetadata>
}

export interface UpdateCallLogRequest {
  status?: CallLog['status']
  end_reason?: CallLog['end_reason']
  end_time?: string
  success_rating?: number
  caller_satisfaction?: number
  recording_url?: string
  cost_breakdown?: Partial<CallCostBreakdown>
  quality_metrics?: Partial<CallQualityMetrics>
  metadata?: Partial<CallMetadata>
}

export interface AddTranscriptEntryRequest {
  timestamp: string
  speaker: CallTranscriptEntry['speaker']
  text: string
  confidence?: number
  processing_time_ms?: number
  tokens_used?: number
  model_used?: string
  event_type?: CallTranscriptEntry['event_type']
  event_data?: Record<string, any>
}

export interface AddSystemEventRequest {
  event_type: CallSystemEvent['event_type']
  event_description: string
  timestamp: string
  duration_ms?: number
  event_data?: Record<string, any>
  severity?: CallSystemEvent['severity']
}

export interface InitiateTakeoverRequest {
  reason: string
  supervisor_notes?: string
}

export interface UpdateTakeoverRequest {
  status?: CallTakeoverSession['status']
  supervisor_notes?: string
  outcome?: string
}

// Webhook types for provider integration
export interface TwilioIncomingCallWebhook {
  CallSid: string
  AccountSid: string
  From: string
  To: string
  CallStatus: string
  CallerName?: string
  CallerCity?: string
  CallerState?: string
  CallerCountry?: string
  CallerZip?: string
}

export interface TwilioCallStatusWebhook {
  CallSid: string
  AccountSid: string
  From: string
  To: string
  CallStatus: 'queued' | 'ringing' | 'in-progress' | 'completed' | 'busy' | 'failed' | 'no-answer' | 'canceled'
  CallDuration?: string
  RecordingUrl?: string
  RecordingDuration?: string
}

export interface TwilioTranscriptionWebhook {
  TranscriptionSid: string
  TranscriptionText: string
  TranscriptionStatus: string
  TranscriptionUrl: string
  CallSid: string
  RecordingSid?: string
}

// Search and filtering types
export interface CallLogFilters {
  status?: CallLog['status'][]
  agent_ids?: string[]
  phone_number_ids?: string[]
  date_from?: string
  date_to?: string
  duration_min?: number
  duration_max?: number
  caller_number?: string
  search_query?: string // Full text search in transcripts
  has_recording?: boolean
  success_rating_min?: number
  tags?: string[]
  page?: number
  page_size?: number
  sort_by?: 'start_time' | 'duration_seconds' | 'total_cost' | 'success_rating'
  sort_order?: 'asc' | 'desc'
}

export interface CallLogSearchResult {
  call_logs: CallLog[]
  total_count: number
  has_more: boolean
  next_page?: number
}

export interface CallStatistics {
  total_calls: number
  completed_calls: number
  missed_calls: number
  failed_calls: number
  total_duration_seconds: number
  average_duration_seconds: number
  total_cost: number
  success_rate: number
  
  // Time series data
  daily_stats?: DailyCallStats[]
  hourly_stats?: HourlyCallStats[]
}

export interface DailyCallStats {
  date: string
  call_count: number
  completed_calls: number
  total_duration: number
  total_cost: number
  average_rating?: number
}

export interface HourlyCallStats {
  hour: number
  call_count: number
  completed_calls: number
  average_duration: number
  peak_concurrent?: number
}

// Real-time monitoring types
export interface LiveCallStatus {
  call_log_id: string
  status: CallLog['status']
  start_time: string
  duration_seconds: number
  caller_number: string
  agent_name: string
  current_transcript_entry?: CallTranscriptEntry
  quality_indicators: {
    connection_quality: 'good' | 'fair' | 'poor'
    audio_quality: 'good' | 'fair' | 'poor'
    response_latency: 'low' | 'medium' | 'high'
  }
  supervisor_monitoring?: boolean
}

export interface CallMonitoringEvent {
  type: 'call_started' | 'call_ended' | 'transcript_update' | 'status_change' | 'quality_alert' | 'takeover_initiated'
  call_log_id: string
  timestamp: string
  data: Record<string, any>
}

// Speech processing types
export interface SpeechToTextResult {
  text: string
  confidence: number
  processing_time_ms: number
  language?: string
  alternatives?: Array<{
    text: string
    confidence: number
  }>
}

export interface TextToSpeechRequest {
  text: string
  voice?: string
  language?: string
  speed?: number
  pitch?: number
}

export interface TextToSpeechResult {
  audio_url: string
  duration_ms: number
  file_size_bytes: number
  processing_time_ms: number
}

// Error handling types
export interface CallHandlingError {
  type: 'webhook_error' | 'speech_processing_error' | 'agent_error' | 'provider_error' | 'system_error'
  message: string
  call_log_id?: string
  details?: Record<string, any>
  timestamp: string
  severity: 'low' | 'medium' | 'high' | 'critical'
}

// Configuration types
export interface CallHandlingConfig {
  // Speech processing
  speech_to_text_provider: 'openai' | 'google' | 'azure'
  text_to_speech_provider: 'openai' | 'elevenlabs' | 'azure'
  default_language: string
  supported_languages: string[]
  
  // Recording settings
  recording_enabled_by_default: boolean
  recording_retention_days: number
  recording_format: 'mp3' | 'wav'
  
  // Quality thresholds
  max_response_latency_ms: number
  min_speech_confidence: number
  max_silence_duration_ms: number
  
  // Cost limits
  max_cost_per_call: number
  daily_cost_limit: number
  alert_cost_threshold: number
  
  // Business hours
  business_hours: {
    timezone: string
    days: Record<string, { start: string; end: string } | null>
  }
  
  // Fallback settings
  enable_dtmf_fallback: boolean
  fallback_message: string
  emergency_transfer_number?: string
}