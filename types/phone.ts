export interface PhoneNumber {
  id: string
  user_id: string
  created_at: string
  updated_at: string
  number: string
  friendly_name?: string
  country_code: string
  area_code?: string
  status: 'active' | 'inactive' | 'suspended'
  assigned_agent_id?: string
  monthly_cost: number
  provider_id: string
  provider_phone_number_sid?: string
  capabilities: {
    voice: boolean
    sms: boolean
  }
  forwarding_settings: Record<string, any>
  business_hours: Record<string, any>
  webhook_url?: string
}

export interface CallLog {
  id: string
  phone_number_id: string
  agent_id?: string
  created_at: string
  start_time: string
  end_time?: string
  duration_seconds?: number
  caller_number: string
  status: 'active' | 'completed' | 'failed' | 'missed' | 'no_answer'
  end_reason?: 'caller_hangup' | 'agent_hangup' | 'system_error' | 'timeout' | 'busy'
  transcript_url?: string
  recording_url?: string
  cost?: number
  metadata: Record<string, any>
  provider_call_sid?: string
}

export interface CallMetrics {
  id: string
  phone_number_id: string
  date: string
  total_calls: number
  answered_calls: number
  missed_calls: number
  failed_calls: number
  average_duration: number
  total_duration: number
  total_cost: number
  created_at: string
  updated_at: string
}

export interface BillingUsage {
  id: string
  user_id: string
  phone_number_id: string
  period_start: string
  period_end: string
  usage_type: 'monthly_fee' | 'call_minutes' | 'sms'
  quantity: number
  unit_cost: number
  total_cost: number
  currency: string
  created_at: string
  metadata: Record<string, any>
}

export interface PhoneNumberStats {
  total_calls: number
  answered_calls: number
  missed_calls: number
  average_duration: number
  total_cost: number
  last_call_date?: string
}

export interface AvailablePhoneNumber {
  phone_number: string
  country_code: string
  area_code?: string
  region?: string
  capabilities: {
    voice: boolean
    sms: boolean
    mms?: boolean
  }
  monthly_cost: number
  setup_cost?: number
}

export interface CreatePhoneNumberRequest {
  phone_number: string
  friendly_name?: string
  assigned_agent_id?: string
}

export interface UpdatePhoneNumberRequest {
  friendly_name?: string
  status?: 'active' | 'inactive' | 'suspended'
  assigned_agent_id?: string
  forwarding_settings?: Record<string, any>
  business_hours?: Record<string, any>
  webhook_url?: string
}

export interface PhoneNumberSearchParams {
  country_code?: string
  area_code?: string
  contains?: string
  voice_enabled?: boolean
  sms_enabled?: boolean
  limit?: number
}