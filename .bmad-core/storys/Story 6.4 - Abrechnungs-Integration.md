# Story 6.4: Abrechnungs-Integration

## Story-Information
**Epic**: Epic 6 - Telefonie-Integration  
**Story Points**: 5  
**Priorität**: Mittel  
**Status**: Ready for Development  

## User Story
**Als** registrierter <PERSON>utzer  
**möchte ich** transparente Kostenübersichten und automatische Abrechnung für meine Telefonie-Nutzung  
**sodass** ich meine Ausgaben kontrollieren und optimieren kann.

## Beschreibung
Diese Story implementiert ein umfassendes Abrechnungssystem für alle Telefonie-bezogenen Kosten, einschließlich Telefonnummern-Abonnements, Anrufkosten und AI-Processing-Gebühren.

## Akzeptanzkriterien

### AC1: Kostenübersicht und Dashboard
- [ ] Als Benutzer kann ich eine Übersicht aller Telefonie-Kosten sehen
- [ ] Aufschlüsselung nach Kategorien: Nummern, Anrufe, AI-Processing
- [ ] Monatliche und tägliche Kostenverlauf in Grafiken
- [ ] Vergleich zu Vorperioden (Monat/Jahr)
- [ ] Export-Funktionen für Buchhaltung (CSV, PDF)

### AC2: Detaillierte Kostenaufschlüsselung
- [ ] Kosten pro Telefonnummer (monatliche Grundgebühr)
- [ ] Kosten pro Anruf (Minuten, AI-Processing, Transcription)
- [ ] Separate Auflistung verschiedener Kostenarten
- [ ] Filterung nach Zeitraum, Telefonnummer oder Agent
- [ ] Drill-down zu einzelnen Anrufen möglich

### AC3: Budget-Management und Alerts
- [ ] Ich kann Budgetlimits pro Monat festlegen
- [ ] Automatische Warnungen bei Erreichen von Schwellenwerten (75%, 90%, 100%)
- [ ] E-Mail-Benachrichtigungen bei Budgetüberschreitung
- [ ] Automatische Pausierung von Services bei Hard-Limits
- [ ] Budgets können pro Telefonnummer oder global gesetzt werden

### AC4: Rechnungsstellung und Payment
- [ ] Automatische monatliche Rechnungserstellung
- [ ] Integration mit Payment-Provider für automatische Abbuchung
- [ ] Rechnungen können über die UI eingesehen und heruntergeladen werden
- [ ] Payment-Methoden können verwaltet werden
- [ ] Bei Zahlungsfehlern werden Services pausiert mit Kulanzzeit

### AC5: Usage Analytics und Optimierung
- [ ] Analysen zeigen teurste Telefonnummern und Agenten
- [ ] Empfehlungen zur Kostenoptimierung
- [ ] Ungenutzte Ressourcen werden identifiziert
- [ ] Cost-per-Call-Metriken für verschiedene Agenten
- [ ] ROI-Berechnungen basierend auf Anrufvolumen

## Technische Anforderungen

### API-Endpoints
```typescript
// Kosten-Übersicht
GET    /api/billing/overview                    // Gesamt-Kostenübersicht
GET    /api/billing/costs/breakdown             // Detaillierte Aufschlüsselung
GET    /api/billing/costs/trends                // Kostenverlauf und Trends

// Budget-Management
GET    /api/billing/budgets                     // Aktuelle Budgets
POST   /api/billing/budgets                     // Budget erstellen/aktualisieren
DELETE /api/billing/budgets/[id]                // Budget löschen
GET    /api/billing/budgets/alerts              // Budget-Alerts

// Rechnungen
GET    /api/billing/invoices                    // Alle Rechnungen
GET    /api/billing/invoices/[id]               // Einzelne Rechnung
POST   /api/billing/invoices/[id]/download      // Rechnung herunterladen
GET    /api/billing/invoices/current            // Aktuelle/kommende Rechnung

// Payment-Management
GET    /api/billing/payment-methods             // Payment-Methoden
POST   /api/billing/payment-methods             // Neue Payment-Methode
DELETE /api/billing/payment-methods/[id]        // Payment-Methode löschen
POST   /api/billing/payment-methods/[id]/default // Als Standard setzen

// Usage-Analytics
GET    /api/billing/analytics/costs-by-agent    // Kosten pro Agent
GET    /api/billing/analytics/costs-by-number   // Kosten pro Telefonnummer
GET    /api/billing/analytics/optimization      // Optimierungsempfehlungen
```

### Datenmodell
```typescript
interface BillingPeriod {
  id: string
  user_id: string
  period_start: string
  period_end: string
  status: 'active' | 'closed' | 'pending_payment'
  total_cost: number
  cost_breakdown: CostBreakdown
  invoice_id?: string
  created_at: string
  updated_at: string
}

interface CostBreakdown {
  phone_number_subscriptions: number
  inbound_call_minutes: number
  outbound_call_minutes: number
  ai_processing_tokens: number
  speech_to_text_minutes: number
  text_to_speech_characters: number
  recording_storage: number
  transcription_minutes: number
  additional_fees: number
  taxes: number
  total: number
  currency: string
}

interface Budget {
  id: string
  user_id: string
  name: string
  type: 'global' | 'phone_number' | 'agent'
  target_id?: string // phone_number_id oder agent_id bei spezifischen Budgets
  monthly_limit: number
  currency: string
  alert_thresholds: number[] // [0.75, 0.90, 1.0]
  auto_pause_enabled: boolean
  current_usage: number
  status: 'active' | 'exceeded' | 'paused'
  created_at: string
  updated_at: string
}

interface Invoice {
  id: string
  user_id: string
  billing_period_id: string
  invoice_number: string
  issue_date: string
  due_date: string
  amount: number
  tax_amount: number
  total_amount: number
  currency: string
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
  payment_date?: string
  pdf_url?: string
  line_items: InvoiceLineItem[]
  billing_address: BillingAddress
}

interface InvoiceLineItem {
  description: string
  quantity: number
  unit_price: number
  total_price: number
  period?: string
  metadata?: Record<string, any>
}

interface CostEntry {
  id: string
  user_id: string
  billing_period_id: string
  cost_type: 'phone_subscription' | 'call_minute' | 'ai_processing' | 'transcription' | 'recording'
  entity_type: 'phone_number' | 'call_log' | 'agent'
  entity_id: string
  amount: number
  currency: string
  quantity: number
  unit_price: number
  metadata: Record<string, any>
  created_at: string
}
```

### Datenbankschema
```sql
CREATE TABLE billing_periods (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  status TEXT NOT NULL DEFAULT 'active' CHECK (
    status IN ('active', 'closed', 'pending_payment')
  ),
  total_cost DECIMAL(10,2) DEFAULT 0,
  cost_breakdown JSONB DEFAULT '{}',
  invoice_id UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, period_start)
);

CREATE TABLE budgets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('global', 'phone_number', 'agent')),
  target_id UUID, -- references phone_numbers(id) or agents(id)
  monthly_limit DECIMAL(10,2) NOT NULL CHECK (monthly_limit > 0),
  currency TEXT NOT NULL DEFAULT 'EUR',
  alert_thresholds DECIMAL[] DEFAULT '{0.75, 0.90, 1.0}',
  auto_pause_enabled BOOLEAN DEFAULT false,
  current_usage DECIMAL(10,2) DEFAULT 0,
  status TEXT NOT NULL DEFAULT 'active' CHECK (
    status IN ('active', 'exceeded', 'paused')
  ),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  billing_period_id UUID REFERENCES billing_periods(id) ON DELETE CASCADE,
  invoice_number TEXT NOT NULL UNIQUE,
  issue_date DATE NOT NULL,
  due_date DATE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'EUR',
  status TEXT NOT NULL DEFAULT 'draft' CHECK (
    status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')
  ),
  payment_date DATE,
  pdf_url TEXT,
  line_items JSONB DEFAULT '[]',
  billing_address JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE cost_entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  billing_period_id UUID REFERENCES billing_periods(id) ON DELETE CASCADE,
  cost_type TEXT NOT NULL CHECK (
    cost_type IN ('phone_subscription', 'call_minute', 'ai_processing', 'transcription', 'recording')
  ),
  entity_type TEXT NOT NULL CHECK (entity_type IN ('phone_number', 'call_log', 'agent')),
  entity_id UUID NOT NULL,
  amount DECIMAL(10,4) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'EUR',
  quantity DECIMAL(10,4) DEFAULT 1,
  unit_price DECIMAL(10,4) NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes für Performance
CREATE INDEX idx_billing_periods_user ON billing_periods(user_id);
CREATE INDEX idx_budgets_user ON budgets(user_id);
CREATE INDEX idx_budgets_target ON budgets(target_id) WHERE target_id IS NOT NULL;
CREATE INDEX idx_invoices_user ON invoices(user_id);
CREATE INDEX idx_cost_entries_user ON cost_entries(user_id);
CREATE INDEX idx_cost_entries_period ON cost_entries(billing_period_id);
CREATE INDEX idx_cost_entries_entity ON cost_entries(entity_type, entity_id);
CREATE INDEX idx_cost_entries_created ON cost_entries(created_at);
```

## Services Implementation

### Billing Service
```typescript
export class BillingService {
  constructor(
    private readonly paymentProvider: PaymentProvider,
    private readonly invoiceGenerator: InvoiceGenerator,
    private readonly notificationService: NotificationService
  ) {}

  async trackCost(costEntry: CreateCostEntryRequest): Promise<void> {
    const billingPeriod = await this.getOrCreateCurrentBillingPeriod(costEntry.user_id)
    
    // Kosten-Eintrag erstellen
    await this.db.query(`
      INSERT INTO cost_entries (
        user_id, billing_period_id, cost_type, entity_type, entity_id,
        amount, currency, quantity, unit_price, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
    `, [
      costEntry.user_id,
      billingPeriod.id,
      costEntry.cost_type,
      costEntry.entity_type,
      costEntry.entity_id,
      costEntry.amount,
      costEntry.currency,
      costEntry.quantity,
      costEntry.unit_price,
      JSON.stringify(costEntry.metadata)
    ])

    // Billing-Period aktualisieren
    await this.updateBillingPeriodTotals(billingPeriod.id)

    // Budget-Checks durchführen
    await this.checkBudgets(costEntry.user_id, costEntry.amount)
  }

  async checkBudgets(userId: string, additionalCost: number): Promise<void> {
    const budgets = await this.getActiveBudgets(userId)
    
    for (const budget of budgets) {
      const newUsage = budget.current_usage + additionalCost
      const limitReached = newUsage / budget.monthly_limit

      for (const threshold of budget.alert_thresholds) {
        if (budget.current_usage / budget.monthly_limit < threshold && 
            limitReached >= threshold) {
          await this.sendBudgetAlert(budget, threshold, newUsage)
        }
      }

      // Auto-Pause bei 100% Limit
      if (budget.auto_pause_enabled && limitReached >= 1.0) {
        await this.pauseServices(budget)
      }

      // Budget-Usage aktualisieren
      await this.updateBudgetUsage(budget.id, newUsage)
    }
  }

  async generateMonthlyInvoice(userId: string, billingPeriod: BillingPeriod): Promise<Invoice> {
    // Cost-Entries für Period abrufen
    const costEntries = await this.getCostEntriesForPeriod(billingPeriod.id)
    
    // Line-Items generieren
    const lineItems = this.groupCostEntriesIntoLineItems(costEntries)
    
    // Steuern berechnen
    const taxAmount = await this.calculateTax(billingPeriod.total_cost, userId)
    
    // Invoice erstellen
    const invoice = await this.db.query(`
      INSERT INTO invoices (
        user_id, billing_period_id, invoice_number, issue_date, due_date,
        amount, tax_amount, total_amount, line_items, billing_address
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [
      userId,
      billingPeriod.id,
      await this.generateInvoiceNumber(),
      new Date(),
      new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 Tage
      billingPeriod.total_cost,
      taxAmount,
      billingPeriod.total_cost + taxAmount,
      JSON.stringify(lineItems),
      JSON.stringify(await this.getBillingAddress(userId))
    ])

    // PDF generieren
    const pdfUrl = await this.invoiceGenerator.generatePDF(invoice.rows[0])
    await this.updateInvoicePDF(invoice.rows[0].id, pdfUrl)

    return invoice.rows[0]
  }

  async processAutomaticPayment(invoice: Invoice): Promise<PaymentResult> {
    const user = await this.userService.getUser(invoice.user_id)
    const defaultPaymentMethod = await this.getDefaultPaymentMethod(invoice.user_id)

    if (!defaultPaymentMethod) {
      await this.notificationService.sendPaymentReminderEmail(user.email, invoice)
      return { success: false, reason: 'No payment method' }
    }

    try {
      const paymentResult = await this.paymentProvider.processPayment({
        amount: invoice.total_amount,
        currency: invoice.currency,
        paymentMethodId: defaultPaymentMethod.provider_id,
        description: `Invoice ${invoice.invoice_number}`,
        metadata: {
          invoiceId: invoice.id,
          userId: invoice.user_id
        }
      })

      if (paymentResult.success) {
        await this.markInvoiceAsPaid(invoice.id, paymentResult.payment_id)
        await this.notificationService.sendPaymentConfirmationEmail(user.email, invoice)
      } else {
        await this.handlePaymentFailure(invoice, paymentResult.error)
      }

      return paymentResult
    } catch (error) {
      await this.handlePaymentError(invoice, error)
      return { success: false, reason: error.message }
    }
  }
}
```

### Cost Tracking Integration
```typescript
// Integration in Call Service
export class CallService {
  async finalizeCall(callLogId: string): Promise<void> {
    const callLog = await this.getCallLog(callLogId)
    
    // Anrufkosten berechnen
    const callCosts = await this.calculateCallCosts(callLog)
    
    // Kosten einzeln tracken
    await this.billingService.trackCost({
      user_id: callLog.user_id,
      cost_type: 'call_minute',
      entity_type: 'call_log',
      entity_id: callLog.id,
      amount: callCosts.duration_cost,
      quantity: callLog.duration_seconds / 60,
      unit_price: callCosts.per_minute_rate,
      metadata: {
        phone_number: callLog.phone_number,
        agent_id: callLog.agent_id,
        duration_seconds: callLog.duration_seconds
      }
    })

    if (callCosts.ai_processing_cost > 0) {
      await this.billingService.trackCost({
        user_id: callLog.user_id,
        cost_type: 'ai_processing',
        entity_type: 'call_log',
        entity_id: callLog.id,
        amount: callCosts.ai_processing_cost,
        quantity: callCosts.total_tokens,
        unit_price: callCosts.token_price,
        metadata: {
          model_used: callLog.metadata.model_used,
          total_tokens: callCosts.total_tokens
        }
      })
    }

    // Call-Log mit Kosten aktualisieren
    await this.updateCallLog(callLogId, {
      cost_breakdown: callCosts
    })
  }
}

// Integration in Phone Number Service
export class PhoneNumberService {
  async trackMonthlySubscriptionCosts(): Promise<void> {
    // Cron-Job läuft monatlich
    const activeNumbers = await this.getActivePhoneNumbers()
    
    for (const phoneNumber of activeNumbers) {
      await this.billingService.trackCost({
        user_id: phoneNumber.user_id,
        cost_type: 'phone_subscription',
        entity_type: 'phone_number',
        entity_id: phoneNumber.id,
        amount: phoneNumber.pricing.monthly_fee,
        quantity: 1,
        unit_price: phoneNumber.pricing.monthly_fee,
        metadata: {
          number: phoneNumber.number,
          country: phoneNumber.country_name,
          subscription_period: this.getCurrentMonth()
        }
      })
    }
  }
}
```

## UI/UX Anforderungen

### Billing-Dashboard
- Gesamt-Kostenübersicht mit großen KPI-Cards
- Kostenverlauf-Charts (Balken- und Liniendiagramme)
- Quick-Links zu aktueller Rechnung und Budgets
- Alert-Badges bei Budget-Überschreitungen
- Export-Buttons für verschiedene Formate

### Budget-Management-Interface
- Budget-Cards mit Progress-Bars
- Quick-Edit für Budget-Limits
- Alert-Konfiguration mit Toggle-Switches
- Budget-Creation-Modal mit Wizard
- Usage-Breakdown pro Budget

### Invoice-Liste und -Details
- Tabelle mit Status-Filtering
- Download-Buttons für PDF-Rechnungen
- Payment-Status-Badges
- Detailansicht mit Line-Items
- Payment-History pro Rechnung

### Cost-Analytics-Dashboard
- Kosten-Breakdown nach verschiedenen Dimensionen
- Top-Spender-Listen (Nummern, Agenten, Anrufe)
- Trend-Analysen mit Forecasting
- Optimierungs-Empfehlungen als Cards
- Drill-Down-Möglichkeiten

## Testkriterien

### Unit Tests
- [ ] Cost-Tracking-Logic
- [ ] Budget-Check-Algorithmus
- [ ] Invoice-Generation-Process
- [ ] Payment-Processing-Flow
- [ ] Tax-Calculation-Logic

### Integration Tests
- [ ] End-to-End-Billing-Flow
- [ ] Payment-Provider-Integration
- [ ] Budget-Alert-System
- [ ] Invoice-PDF-Generation
- [ ] Automatic-Payment-Processing

### UI Tests
- [ ] Billing-Dashboard-Components
- [ ] Budget-Management-Interface
- [ ] Invoice-Download-Functionality
- [ ] Cost-Analytics-Charts
- [ ] Payment-Method-Management

## Definition of Done
- [ ] Alle Akzeptanzkriterien erfüllt
- [ ] Comprehensive Cost-Tracking für alle Telephony-Services
- [ ] Budget-Management mit Alerts und Auto-Pause
- [ ] Automatic Invoice-Generation und -Delivery
- [ ] Payment-Processing-Integration
- [ ] Cost-Analytics und Optimization-Recommendations
- [ ] Database-Schema mit Billing-Tables
- [ ] Unit-, Integration- und UI-Tests
- [ ] PDF-Invoice-Generation
- [ ] Email-Notifications für Billing-Events
- [ ] Export-Funktionen für Accounting
- [ ] Security-Review für Payment-Handling
- [ ] Code Review und Documentation

## Abhängigkeiten
- **Story 6.1**: Telefonnummer-Marktplatz (für Initial-Purchase-Tracking)
- **Story 6.2**: Telefonnummer-Verwaltung (für Subscription-Costs)
- **Story 6.3**: Anruf-Handling (für Call-Cost-Tracking)
- **Payment Provider**: Stripe-Integration für Payments
- **Invoice Generation**: PDF-Library für Rechnungen

## Risiken
- **Payment-Processing-Complexity**: Verschiedene Payment-Methoden und Currencies
- **Tax-Calculation**: Unterschiedliche Steuersätze je nach Land
- **Budget-Race-Conditions**: Concurrent-Usage kann Budgets überschreiten
- **Invoice-Compliance**: Legal-Requirements für verschiedene Märkte

## Nächste Schritte nach Completion
- **Story 6.5**: Erweiterte Anruf-Features
- **Financial-Reporting**: Advanced-Analytics für Business-Intelligence