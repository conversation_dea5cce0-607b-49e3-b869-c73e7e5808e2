# Story 2: Echtzeit-Gesprächsmonitoring implementieren

## Story-Information

**Epic**: Epic 3 - Agent-Monitoring und Analytics  
**Story ID**: E3-S2  
**Titel**: Echtzeit-Gesprächsmonitoring implementieren  
**Priorität**: Hoch  
**Aufwand**: 13 Story Points  
**Status**: Bereit zur Implementierung  
**Assignee**: TBD  
**Sprint**: TBD  

## User Story

**Als** Administrator oder Supervisor  
**möchte ich** laufende Gespräche in Echtzeit überwachen können  
**damit ich** bei Problemen eingreifen und die Gesprächsqualität sicherstellen kann.

## Geschäftswert

- **Qualitätssicherung**: 25% Verbesserung der Gesprächsqualität
- **Problemlösung**: 60% schnellere Reaktion auf Probleme
- **Kundenzufriedenheit**: 20% Steigerung durch proaktive Unterstützung
- **Compliance**: Vollständige Überwachung für Audit-Zwecke

## Akzeptanzkriterien

### AC1: Live-Gesprächsanzeige mit Status
**Priorität**: Muss  
**Aufwand**: 4 SP

- [ ] Liste aller aktuell laufenden Gespräche
- [ ] Für jedes Gespräch angezeigt:
  - Agent-Name und Avatar
  - Anrufer-Information (falls verfügbar)
  - Gesprächsdauer (Live-Timer)
  - Aktueller Status (connecting, active, on-hold, ending)
  - Letzte Aktivität/Nachricht (Zeitstempel)
- [ ] Farbkodierung nach Gesprächsstatus:
  - Blau: connecting
  - Grün: active
  - Gelb: on-hold
  - Orange: ending
- [ ] Auto-Refresh alle 2 Sekunden
- [ ] Sortierung nach Gesprächsdauer (längste zuerst)

### AC2: Gesprächsverlauf in Echtzeit
**Priorität**: Muss  
**Aufwand**: 5 SP

- [ ] Live-Transkription des aktuellen Gesprächs
- [ ] Unterscheidung zwischen Agent und Anrufer:
  - Agent: Rechts ausgerichtet, blaue Sprechblase
  - Anrufer: Links ausgerichtet, graue Sprechblase
  - System: Zentriert, gelbe Benachrichtigung
- [ ] Zeitstempel für jede Nachricht (HH:MM:SS)
- [ ] Automatisches Scrollen zu neuesten Nachrichten
- [ ] Anzeige von System-Events:
  - Tool-Nutzung (z.B. "Kalender geöffnet")
  - Transfers ("Gespräch weitergeleitet")
  - Hold-Status ("Gespräch pausiert")
- [ ] Sentiment-Analyse-Indikatoren (falls verfügbar):
  - 😊 Positiv (grün)
  - 😐 Neutral (grau)
  - 😟 Negativ (rot)

### AC3: Möglichkeit zur Gesprächsübernahme
**Priorität**: Muss  
**Aufwand**: 3 SP

- [ ] "Übernehmen"-Button für jedes aktive Gespräch
- [ ] Bestätigungsdialog vor Übernahme:
  - Grund für Übernahme (Dropdown)
  - Optionale Notiz
  - Bestätigen/Abbrechen
- [ ] Nahtloser Transfer ohne Gesprächsunterbrechung
- [ ] Benachrichtigung an ursprünglichen Agent:
  - In-App Notification
  - Grund der Übernahme
- [ ] Logging der Übernahme-Aktion:
  - Zeitstempel
  - Supervisor-ID
  - Grund
  - Gesprächs-ID

### AC4: Erweiterte Monitoring-Features
**Priorität**: Sollte  
**Aufwand**: 1 SP

- [ ] Audio-Level-Anzeige (falls verfügbar):
  - Visuelle Balken für Agent und Anrufer
  - Stumm-Indikator
- [ ] Gesprächsqualitäts-Indikatoren:
  - Verbindungsqualität (1-5 Sterne)
  - Latenz-Anzeige
- [ ] Warnung bei langen Wartezeiten:
  - Alert bei >30 Sekunden Stille
  - Visuelle Hervorhebung
- [ ] Alert bei Gesprächsabbrüchen:
  - Sofortige Benachrichtigung
  - Grund des Abbruchs (falls verfügbar)
- [ ] Filter nach:
  - Agent
  - Gesprächsstatus
  - Gesprächsdauer
  - Priorität

## Technische Spezifikation

### Komponenten-Architektur

```typescript
// Hauptkomponenten
app/monitoring/page.tsx
components/monitoring/LiveMonitoringDashboard.tsx
components/monitoring/ConversationList.tsx
components/monitoring/ConversationDetails.tsx
components/monitoring/LiveTranscript.tsx
components/monitoring/TakeoverModal.tsx
components/monitoring/AudioLevelIndicator.tsx

// Hooks
hooks/useLiveConversations.ts
hooks/useConversationDetails.ts
hooks/useWebSocketConnection.ts
hooks/useTakeover.ts
hooks/useAudioLevels.ts

// Services
services/monitoringService.ts
services/webSocketService.ts
services/takeoverService.ts

// Types
types/monitoring.ts
types/conversation.ts
```

### Datenstrukturen

```typescript
interface LiveConversation {
  id: string
  agentId: string
  agentName: string
  agentAvatar?: string
  callerId?: string
  callerInfo?: {
    name?: string
    phone?: string
    location?: string
  }
  status: 'connecting' | 'active' | 'on-hold' | 'ending'
  startTime: Date
  duration: number // in seconds
  lastActivity: Date
  transcript: TranscriptEntry[]
  qualityMetrics: {
    connectionQuality: number // 1-5
    latency: number // in ms
    audioLevel: {
      agent: number // 0-100
      caller: number // 0-100
    }
  }
  metadata: {
    priority: 'low' | 'medium' | 'high'
    tags: string[]
    department?: string
  }
}

interface TranscriptEntry {
  id: string
  timestamp: Date
  speaker: 'agent' | 'caller' | 'system'
  content: string
  confidence?: number // 0-1
  sentiment?: 'positive' | 'neutral' | 'negative'
  eventType: 'message' | 'tool_use' | 'transfer' | 'hold' | 'system'
  metadata?: {
    toolName?: string
    transferTarget?: string
    systemEvent?: string
  }
}

interface TakeoverRequest {
  conversationId: string
  reason: 'quality_issue' | 'escalation' | 'training' | 'technical_problem' | 'other'
  note?: string
  supervisorId: string
  timestamp: Date
}
```

### WebSocket Events

```typescript
// Eingehende Events
interface WebSocketEvents {
  'conversation:started': LiveConversation
  'conversation:updated': {
    conversationId: string
    updates: Partial<LiveConversation>
  }
  'conversation:ended': {
    conversationId: string
    endTime: Date
    reason: string
  }
  'transcript:new_entry': {
    conversationId: string
    entry: TranscriptEntry
  }
  'audio:level_update': {
    conversationId: string
    levels: { agent: number, caller: number }
  }
  'quality:update': {
    conversationId: string
    metrics: LiveConversation['qualityMetrics']
  }
}

// Ausgehende Events
interface WebSocketCommands {
  'monitoring:subscribe': {
    agentIds?: string[]
    conversationIds?: string[]
  }
  'monitoring:unsubscribe': {
    agentIds?: string[]
    conversationIds?: string[]
  }
  'conversation:takeover': TakeoverRequest
  'conversation:get_details': {
    conversationId: string
  }
}
```

## Implementierungsplan

### Phase 1: WebSocket-Integration (3 Tage)
- [ ] WebSocket-Service erweitern
- [ ] Event-Handling für Monitoring-Events
- [ ] Connection-Management optimieren
- [ ] Error Handling und Reconnection-Logic

### Phase 2: Basis-UI (3 Tage)
- [ ] LiveMonitoringDashboard Layout erstellen
- [ ] ConversationList Komponente entwickeln
- [ ] Split-Screen Layout (Liste links, Details rechts)
- [ ] Responsive Design für verschiedene Bildschirmgrößen

### Phase 3: Live-Transkription (3 Tage)
- [ ] LiveTranscript Komponente implementieren
- [ ] Echtzeit-Updates für Nachrichten
- [ ] Auto-Scroll und Performance-Optimierung
- [ ] Sentiment-Anzeige und System-Events

### Phase 4: Übernahme-Funktionalität (2 Tage)
- [ ] TakeoverModal implementieren
- [ ] API-Integration für Übernahme-Prozess
- [ ] Benutzer-Feedback und Notifications
- [ ] Logging und Audit-Trail

### Phase 5: Erweiterte Features (2 Tage)
- [ ] Audio-Level-Anzeige implementieren
- [ ] Qualitäts-Indikatoren
- [ ] Filter- und Such-Funktionen
- [ ] Performance-Optimierungen

## Testplan

### Unit Tests (Ziel: >85% Coverage)
- [ ] WebSocket-Service
  - Event-Handling
  - Connection-Management
  - Error-Scenarios
- [ ] Monitoring-Hooks
  - useLiveConversations
  - useConversationDetails
  - useTakeover
- [ ] Komponenten-Logik
  - LiveTranscript
  - TakeoverModal
  - ConversationList

### Integration Tests
- [ ] WebSocket-Verbindung
  - Live-Update-Flow
  - Reconnection-Logic
  - Event-Synchronisation
- [ ] Übernahme-Prozess
  - End-to-End Takeover
  - Notification-Flow
  - Error-Handling

### E2E Tests
- [ ] Vollständiger Monitoring-Workflow
  - Dashboard öffnen
  - Gespräch auswählen
  - Live-Updates verfolgen
- [ ] Gesprächsübernahme
  - Übernahme-Dialog
  - Erfolgreiche Übernahme
  - Benachrichtigungen
- [ ] Multi-User-Szenarien
  - Mehrere Supervisoren
  - Gleichzeitige Übernahmen

## Performance-Anforderungen

### Echtzeit-Performance
- [ ] WebSocket-Updates: < 500ms Latenz
- [ ] Transkript-Updates: < 1 Sekunde
- [ ] UI-Updates: < 200ms
- [ ] Übernahme-Prozess: < 3 Sekunden

### Skalierbarkeit
- [ ] Unterstützt bis zu 50 gleichzeitige Gespräche
- [ ] Memory-Management für lange Sessions
- [ ] Optimierte Re-Rendering (React.memo, useMemo)
- [ ] Lazy Loading für Gesprächshistorie

## Definition of Done

### Funktional
- [ ] Alle Akzeptanzkriterien erfüllt
- [ ] Live-Monitoring funktioniert zuverlässig
- [ ] Gesprächsübernahme arbeitet nahtlos
- [ ] Echtzeit-Updates ohne Verzögerung
- [ ] Filter und Suche funktionieren korrekt

### Technisch
- [ ] WebSocket-Integration stabil
- [ ] Code Review abgeschlossen
- [ ] Unit Tests mit >85% Coverage
- [ ] Integration Tests erfolgreich
- [ ] Performance-Tests bestanden

### Qualität
- [ ] Security-Review für WebSocket-Verbindungen
- [ ] Deutsche Lokalisierung vollständig
- [ ] Accessibility-Tests erfolgreich
- [ ] Error Handling für alle Szenarien

### Deployment
- [ ] Dokumentation für Administratoren
- [ ] Deployment in Staging erfolgreich
- [ ] Benutzer-Akzeptanztests erfolgreich
- [ ] Stakeholder-Abnahme erhalten

## Risiken und Mitigation

### Technische Risiken
| Risiko | Wahrscheinlichkeit | Impact | Mitigation |
|--------|-------------------|---------|------------|
| WebSocket-Stabilität bei hoher Last | Mittel | Hoch | Robuste Reconnection-Logik, Fallback-Mechanismen |
| Hoher Memory-Verbrauch | Mittel | Mittel | Lazy Loading, Memory-Management |
| Audio-Integration Komplexität | Hoch | Niedrig | Optionales Feature, schrittweise Implementierung |

### Geschäftsrisiken
| Risiko | Wahrscheinlichkeit | Impact | Mitigation |
|--------|-------------------|---------|------------|
| Datenschutz-Bedenken | Niedrig | Sehr Hoch | Rollenbasierte Zugriffskontrolle, Audit-Logging |
| Benutzerakzeptanz | Niedrig | Mittel | UX-Tests, Schulungen, iterative Verbesserungen |

## Abhängigkeiten

### Interne Abhängigkeiten
- **Story 1**: Dashboard für Navigation zu Monitoring
- **WebSocket-Infrastruktur**: Erweiterte Backend-Unterstützung
- **Audio-Pipeline**: Integration für Audio-Level-Anzeige
- **Notification-Service**: Für Alerts und Benachrichtigungen

### Externe Abhängigkeiten
- **Supabase Real-time**: Für erweiterte WebSocket-Features
- **Audio-Processing-Library**: Für Audio-Level-Anzeige

---

**Erstellt**: 2025-01-29  
**Letzte Aktualisierung**: 2025-01-29  
**Version**: 1.0  
**Nächste Review**: Bei Sprint-Planning
