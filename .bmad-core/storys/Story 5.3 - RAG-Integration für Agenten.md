# Story 5.3: RAG-Integration für Agenten

## Story-Information
**Epic**: Epic 5 - Wissensbasis-Integration (RAG)  
**Story Points**: 8  
**Priorität**: Hoch  
**Status**: Ready for Development  

## User Story
**Als** registrierter <PERSON>utzer  
**möchte ich** meine Agenten mit Wissensdatenbanken verknüpfen können  
**sodass** meine Agenten kontextuelle Antworten basierend auf meinen Dokumenten geben können.

## Beschreibung
Diese Story implementiert die Kernfunktionalität des RAG-Systems: die Verknüpfung von Agenten mit Wissensdatenbanken und die semantische Suche in den Dokumenten für kontextuelle Antworten.

## Akzeptanzkriterien

### AC1: Agent-Wissensdatenbank-Verknüpfung
- [ ] Als Benutzer kann ich bei der Agent-Erstellung eine Wissensdatenbank auswählen
- [ ] Ich kann bei der Agent-Bearbeitung die Wissensdatenbank ändern oder entfernen
- [ ] Ein Agent kann mit mehreren Wissensdatenbanken verknüpft werden
- [ ] Ich sehe in der Agent-Übersicht, welche Wissensdatenbanken verknüpft sind

### AC2: Semantische Suche implementieren
- [ ] Das System kann relevante Dokument-Chunks basierend auf Benutzeranfragen finden
- [ ] Vector-Similarity-Search funktioniert mit konfigurierbaren Schwellenwerten
- [ ] Die Suche berücksichtigt nur Dokumente aus verknüpften Wissensdatenbanken
- [ ] Suchergebnisse werden nach Relevanz sortiert

### AC3: Kontext-Integration für Agenten
- [ ] Relevante Dokument-Chunks werden automatisch zum Agent-Context hinzugefügt
- [ ] Der Agent erhält sowohl die Chunks als auch deren Quellen-Metadaten
- [ ] Die Anzahl der verwendeten Chunks ist konfigurierbar (Standard: 3-5)
- [ ] Token-Limits werden respektiert

### AC4: RAG-Konfiguration pro Agent
- [ ] Ich kann die RAG-Einstellungen pro Agent konfigurieren
- [ ] Relevanz-Schwellenwert ist einstellbar (0.0 - 1.0)
- [ ] Maximale Anzahl Chunks ist konfigurierbar
- [ ] Ich kann RAG für einen Agent temporär deaktivieren

### AC5: Quellenangaben und Transparenz
- [ ] Agent-Antworten enthalten Quellenangaben der verwendeten Dokumente
- [ ] Benutzer können die ursprünglichen Dokument-Chunks einsehen
- [ ] Relevanz-Scores werden für Debugging angezeigt
- [ ] Logs zeigen, welche Chunks für Antworten verwendet wurden

## Technische Anforderungen

### API-Endpoints
```typescript
// Agent-Wissensdatenbank-Verknüpfung
GET    /api/agents/[id]/knowledge-bases           // Verknüpfte Wissensdatenbanken
POST   /api/agents/[id]/knowledge-bases           // Wissensdatenbank verknüpfen
DELETE /api/agents/[id]/knowledge-bases/[kbId]    // Verknüpfung entfernen

// RAG-Suche
POST   /api/knowledge-bases/search                // Vector-Suche
POST   /api/agents/[id]/rag-query                // RAG-Query für Agent

// RAG-Konfiguration
GET    /api/agents/[id]/rag-config                // RAG-Einstellungen abrufen
PUT    /api/agents/[id]/rag-config                // RAG-Einstellungen aktualisieren
```

### Datenmodell-Erweiterungen
```typescript
// Agent-KnowledgeBase Verknüpfung
interface AgentKnowledgeBase {
  id: string
  agent_id: string
  knowledge_base_id: string
  created_at: string
  priority: number  // Für Reihenfolge bei mehreren KBs
}

// RAG-Konfiguration pro Agent
interface AgentRAGConfig {
  agent_id: string
  enabled: boolean
  similarity_threshold: number      // 0.0 - 1.0
  max_chunks: number               // Standard: 5
  chunk_overlap: boolean           // Überlappende Chunks erlauben
  include_metadata: boolean        // Metadaten in Context einbeziehen
  source_attribution: boolean     // Quellenangaben aktivieren
}

// RAG-Suchergebnis
interface RAGSearchResult {
  chunk_id: string
  content: string
  similarity_score: number
  metadata: {
    source_name: string
    page?: number
    section?: string
    document_type: string
  }
}

// RAG-Query-Response
interface RAGQueryResponse {
  chunks: RAGSearchResult[]
  context: string
  sources: DocumentSource[]
  total_tokens: number
}
```

### Datenbankschema-Erweiterungen
```sql
-- Agent-KnowledgeBase Verknüpfungen
CREATE TABLE agent_knowledge_bases (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
  knowledge_base_id UUID REFERENCES knowledge_bases(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  priority INTEGER DEFAULT 1,
  UNIQUE(agent_id, knowledge_base_id)
);

-- RAG-Konfiguration pro Agent
CREATE TABLE agent_rag_configs (
  agent_id UUID PRIMARY KEY REFERENCES agents(id) ON DELETE CASCADE,
  enabled BOOLEAN DEFAULT true,
  similarity_threshold DECIMAL(3,2) DEFAULT 0.75 CHECK (similarity_threshold >= 0 AND similarity_threshold <= 1),
  max_chunks INTEGER DEFAULT 5 CHECK (max_chunks > 0 AND max_chunks <= 20),
  chunk_overlap BOOLEAN DEFAULT true,
  include_metadata BOOLEAN DEFAULT true,
  source_attribution BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Erweiterte Agent-Tabelle
ALTER TABLE agents ADD COLUMN rag_enabled BOOLEAN DEFAULT false;

-- Indexes für Performance
CREATE INDEX idx_agent_knowledge_bases_agent ON agent_knowledge_bases(agent_id);
CREATE INDEX idx_agent_knowledge_bases_kb ON agent_knowledge_bases(knowledge_base_id);
```

## RAG-Service Implementation

### Vector-Suche
```typescript
interface RAGService {
  async searchRelevantChunks(
    query: string,
    knowledgeBaseIds: string[],
    config: RAGSearchConfig
  ): Promise<RAGSearchResult[]>

  async generateContext(
    chunks: RAGSearchResult[],
    includeMetadata: boolean
  ): Promise<string>

  async performRAGQuery(
    agentId: string,
    query: string
  ): Promise<RAGQueryResponse>
}

class RAGServiceImpl implements RAGService {
  async searchRelevantChunks(query, kbIds, config) {
    // 1. Query-Embedding generieren
    const queryEmbedding = await this.embeddingService.generateEmbedding(query)
    
    // 2. Vector-Similarity-Search in Database
    const chunks = await this.db.query(`
      SELECT 
        dc.id,
        dc.content,
        dc.metadata,
        ds.name as source_name,
        ds.file_type,
        1 - (dc.embedding <=> $1) as similarity_score
      FROM document_chunks dc
      JOIN data_sources ds ON dc.data_source_id = ds.id
      WHERE ds.knowledge_base_id = ANY($2)
        AND 1 - (dc.embedding <=> $1) > $3
      ORDER BY similarity_score DESC
      LIMIT $4
    `, [queryEmbedding, kbIds, config.similarity_threshold, config.max_chunks])
    
    return chunks
  }

  async generateContext(chunks, includeMetadata) {
    return chunks.map(chunk => {
      let context = chunk.content
      if (includeMetadata && chunk.metadata.source_name) {
        context += `\n[Quelle: ${chunk.metadata.source_name}]`
      }
      return context
    }).join('\n\n')
  }
}
```

### Agent-Integration
```typescript
// Erweiterte Agent-Service für RAG
export class AgentService {
  async processWithRAG(agentId: string, userMessage: string) {
    const agent = await this.getAgent(agentId)
    const ragConfig = await this.getRAGConfig(agentId)
    
    if (!ragConfig.enabled) {
      return this.processWithoutRAG(agent, userMessage)
    }
    
    // RAG-Suche durchführen
    const ragResult = await this.ragService.performRAGQuery(agentId, userMessage)
    
    // Erweiterten Context erstellen
    const enhancedContext = `
${agent.system_prompt}

VERFÜGBARE DOKUMENTE:
${ragResult.context}

AKTUELLE ANFRAGE: ${userMessage}

Beantworte die Anfrage basierend auf den verfügbaren Dokumenten. 
Gib am Ende deiner Antwort die verwendeten Quellen an.
`

    return {
      response: await this.llmService.generateResponse(enhancedContext),
      sources: ragResult.sources,
      chunks_used: ragResult.chunks.length
    }
  }
}
```

## UI/UX Anforderungen

### Agent-Form-Erweiterungen
- Neuer Tab "Wissensdatenbank" in Agent-Konfiguration
- Multi-Select für Wissensdatenbank-Auswahl
- RAG-Einstellungen mit Schiebereglern und Checkboxen
- Vorschau der verknüpften Dokumente

### RAG-Status-Anzeigen
- RAG-Status-Badge in Agent-Liste
- Indikator für Anzahl verknüpfter Wissensdatenbanken
- Quellenangaben in Test-Umgebung
- Debug-Panel mit Chunk-Details

### Testing-Integration
- RAG-Testing in der Test-Umgebung
- Anzeige verwendeter Chunks bei Tests
- Relevanz-Scores für Debugging
- Quellen-Links zu ursprünglichen Dokumenten

## Testkriterien

### Unit Tests
- [ ] Vector-Similarity-Search-Funktionen
- [ ] Context-Generation-Logic
- [ ] RAG-Konfiguration-Validierung
- [ ] Agent-KnowledgeBase-Verknüpfungen

### Integration Tests
- [ ] End-to-End RAG-Flow
- [ ] Agent-Testing mit RAG
- [ ] Performance bei großen Wissensdatenbanken
- [ ] Fallback bei deaktiviertem RAG

### Performance Tests
- [ ] Vector-Suche-Performance bei vielen Chunks
- [ ] Context-Generation-Geschwindigkeit
- [ ] Memory-Usage bei großen Kontexten
- [ ] API-Response-Zeiten

## Error-Handling

### RAG-Fehler
- Keine relevanten Dokumente gefunden → Fallback ohne RAG
- Embedding-API-Fehler → Temporärer Fallback-Modus
- Context zu lang → Automatisches Chunking/Kürzung
- Wissensdatenbank nicht verfügbar → Clear Error Message

### Konfigurationsfehler
- Ungültige Schwellenwerte → Validation mit Default-Fallback
- Zu viele Chunks → Automatische Begrenzung
- Zirkuläre Abhängigkeiten → Prevention-Logic

## Definition of Done
- [ ] Alle Akzeptanzkriterien erfüllt
- [ ] Agent-Wissensdatenbank-Verknüpfung funktioniert
- [ ] Vector-Similarity-Search implementiert
- [ ] RAG-Konfiguration pro Agent verfügbar
- [ ] Quellenangaben in Agent-Antworten
- [ ] Testing-Integration mit RAG-Support
- [ ] Database-Schema mit RLS-Policies
- [ ] Performance-optimierte Vector-Suche
- [ ] Error-Handling und Fallback-Mechanismen
- [ ] Unit-, Integration- und Performance-Tests
- [ ] UI/UX für RAG-Konfiguration
- [ ] Code Review und Documentation

## Abhängigkeiten
- **Story 5.1**: Wissensdatenbank-Verwaltung (abgeschlossen)
- **Story 5.2**: Dokumenten-Upload und -verarbeitung (abgeschlossen)
- **Vector Database**: pgvector mit Similarity-Search
- **Embedding Service**: Für Query-Embeddings

## Risiken
- **Vector-Search-Performance**: Langsame Suche bei vielen Dokumenten
- **Context-Length-Limits**: Token-Limits bei großen Kontexten
- **Relevanz-Qualität**: Suboptimale Suchergebnisse
- **API-Costs**: Hohe Kosten für Query-Embeddings

## Nächste Schritte nach Completion
- **Story 5.4**: Erweiterte Dokumentenverwaltung
- **Testing**: Umfassende RAG-Tests mit echten Dokumenten
- **Optimization**: Performance-Tuning für Production