# Story 6.1: Telefonnummer-Marktplatz

## Story-Information
**Epic**: Epic 6 - Telefonie-Integration  
**Story Points**: 13  
**Priorität**: Hoch  
**Status**: Ready for Development  

## User Story
**Als** registrierter Benutzer  
**möchte ich** verfügbare Telefonnummern durchsuchen und kaufen können  
**sodass** ich meinen Agenten Telefonnummern zuweisen kann.

## Beschreibung
Diese Story implementiert einen Marktplatz für Telefonnummern, auf dem Benutzer verfügbare Nummern nach verschiedenen Kriterien durchsuchen und direkt kaufen können.

## Akzeptanzkriterien

### AC1: Verfügbare Nummern durchsuchen
- [ ] Als Benutzer kann ich verfügbare Telefonnummern nach Land durchsuchen
- [ ] Ich kann nach Ortsvorwahl (Area Code) filtern
- [ ] Ich kann nach Nummertyp filtern (lokal, national, toll-free)
- [ ] Ich sehe Preise für monatliche Kosten und Setup-Gebühren
- [ ] Ich kann nach Nummernmustern suchen (z.B. bestimmte Ziffernfolgen)

### AC2: Nummerdetails und -vorschau
- [ ] Ich sehe eine Vorschau jeder verfügbaren Nummer
- [ ] Kostenaufschlüsselung wird transparent angezeigt
- [ ] Verfügbarkeitsstatus wird in Echtzeit aktualisiert
- [ ] Länder- und Regionsinformationen werden angezeigt
- [ ] Features der Nummer werden aufgelistet (SMS, Voice, etc.)

### AC3: Kaufprozess
- [ ] Ich kann eine Nummer mit einem Klick in den Warenkorb legen
- [ ] Ich kann mehrere Nummern gleichzeitig kaufen
- [ ] Ein Bestätigungsdialog zeigt alle Kosten vor dem Kauf
- [ ] Der Kaufprozess ist sicher und GDPR-konform
- [ ] Ich erhalte eine Bestätigung nach erfolgreichem Kauf

### AC4: Länder- und Regionsfilter
- [ ] Ich kann nach Ländern filtern (Deutschland, USA, UK, etc.)
- [ ] Innerhalb eines Landes kann ich nach Städten/Regionen filtern
- [ ] Beliebte Regionen werden priorisiert angezeigt
- [ ] Ich kann Favoriten-Regionen speichern
- [ ] Geografische Karte zeigt verfügbare Regionen

### AC5: Preistransparenz und Billing
- [ ] Alle Kosten (Setup, monatlich, per Minute) werden klar angezeigt
- [ ] Währungsumrechnung für internationale Nummern
- [ ] Bulk-Rabatte werden automatisch angewendet
- [ ] Kostenvoranschlag für geschätzte Nutzung
- [ ] Payment-Integration für direkten Kauf

## Technische Anforderungen

### API-Endpoints
```typescript
// Telefonnummer-Marktplatz
GET    /api/phone-numbers/available              // Verfügbare Nummern
GET    /api/phone-numbers/countries              // Unterstützte Länder
GET    /api/phone-numbers/area-codes/[country]   // Ortsvorwahlen pro Land
POST   /api/phone-numbers/search                 // Erweiterte Suche
POST   /api/phone-numbers/purchase               // Nummer(n) kaufen
GET    /api/phone-numbers/pricing                // Preisliste

// Shopping Cart für Bulk-Käufe
POST   /api/phone-numbers/cart/add               // Zur Merkliste hinzufügen
GET    /api/phone-numbers/cart                   // Merkliste anzeigen
DELETE /api/phone-numbers/cart/[numberId]        // Aus Merkliste entfernen
POST   /api/phone-numbers/cart/purchase          // Alle kaufen
```

### Datenmodell
```typescript
interface AvailablePhoneNumber {
  id: string
  number: string
  formatted_number: string
  country_code: string
  country_name: string
  area_code: string
  city?: string
  region?: string
  number_type: 'local' | 'national' | 'toll_free' | 'mobile'
  capabilities: PhoneNumberCapabilities
  pricing: PhoneNumberPricing
  provider_id: string
  available_until?: string
}

interface PhoneNumberCapabilities {
  voice: boolean
  sms: boolean
  mms: boolean
  fax: boolean
}

interface PhoneNumberPricing {
  setup_fee: number
  monthly_fee: number
  per_minute_inbound: number
  per_minute_outbound: number
  per_sms: number
  currency: string
}

interface PhoneNumberSearchRequest {
  country_code?: string
  area_code?: string
  city?: string
  number_type?: string[]
  pattern?: string
  max_monthly_cost?: number
  capabilities?: string[]
  limit?: number
  offset?: number
}

interface PurchaseRequest {
  number_ids: string[]
  billing_address: BillingAddress
  payment_method_id: string
  auto_assign_to_agent?: string
}
```

### Provider-Integration
```typescript
interface TelephonyProvider {
  searchAvailableNumbers(
    criteria: NumberSearchCriteria
  ): Promise<AvailablePhoneNumber[]>

  purchaseNumber(
    numberId: string,
    customerInfo: CustomerInfo
  ): Promise<PurchasedNumber>

  getCountries(): Promise<Country[]>
  getAreaCodes(countryCode: string): Promise<AreaCode[]>
  getPricing(countryCode: string): Promise<PricingInfo>
}

// Twilio-Implementation
class TwilioProvider implements TelephonyProvider {
  async searchAvailableNumbers(criteria) {
    const response = await this.twilioClient.availablePhoneNumbers(criteria.countryCode)
      .local.list({
        areaCode: criteria.areaCode,
        contains: criteria.pattern,
        limit: criteria.limit
      })
    
    return response.map(this.mapTwilioNumber)
  }
}
```

## UI/UX Anforderungen

### Marktplatz-Interface
- Suchleiste mit Auto-Complete für Länder/Städte
- Filter-Sidebar mit Checkboxes und Range-Sliders
- Grid-Layout für Telefonnummern mit Cards
- Sortierung nach Preis, Verfügbarkeit, Region
- Pagination für große Ergebnismengen

### Telefonnummer-Cards
- Große, gut lesbare Nummernanzeige
- Länder-Flagge und Regionsinformation
- Preis-Badge mit monatlichen Kosten
- Feature-Icons für Capabilities
- "In den Warenkorb" und "Sofort kaufen" Buttons

### Shopping-Cart-System
- Floating Cart-Button mit Counter
- Slide-out Cart-Panel
- Bulk-Actions für mehrere Nummern
- Gesamtkostenübersicht
- One-Click-Checkout-Flow

### Filter-System
- Länderauswahl mit Flaggen
- Area-Code-Dropdown mit Städtenamen
- Preisbereich-Slider
- Feature-Checkboxes (SMS, Voice, etc.)
- "Filter zurücksetzen" Option

## Shopping Cart Implementation

### Frontend State Management
```typescript
interface CartState {
  items: AvailablePhoneNumber[]
  total_setup_fee: number
  total_monthly_fee: number
  currency: string
}

const useShoppingCart = () => {
  const [cart, setCart] = useState<CartState>({
    items: [],
    total_setup_fee: 0,
    total_monthly_fee: 0,
    currency: 'EUR'
  })

  const addToCart = (number: AvailablePhoneNumber) => {
    setCart(prev => ({
      ...prev,
      items: [...prev.items, number],
      total_setup_fee: prev.total_setup_fee + number.pricing.setup_fee,
      total_monthly_fee: prev.total_monthly_fee + number.pricing.monthly_fee
    }))
  }

  const removeFromCart = (numberId: string) => {
    setCart(prev => {
      const item = prev.items.find(i => i.id === numberId)
      if (!item) return prev
      
      return {
        ...prev,
        items: prev.items.filter(i => i.id !== numberId),
        total_setup_fee: prev.total_setup_fee - item.pricing.setup_fee,
        total_monthly_fee: prev.total_monthly_fee - item.pricing.monthly_fee
      }
    })
  }

  const purchaseAll = async () => {
    const response = await fetch('/api/phone-numbers/cart/purchase', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        number_ids: cart.items.map(i => i.id),
        billing_address: billingInfo,
        payment_method_id: paymentMethodId
      })
    })
    
    if (response.ok) {
      setCart({ items: [], total_setup_fee: 0, total_monthly_fee: 0, currency: 'EUR' })
      // Redirect to success page
    }
  }

  return { cart, addToCart, removeFromCart, purchaseAll }
}
```

### Backend Service
```typescript
export class PhoneNumberMarketplaceService {
  constructor(
    private readonly provider: TelephonyProvider,
    private readonly billingService: BillingService
  ) {}

  async searchAvailableNumbers(
    searchRequest: PhoneNumberSearchRequest
  ): Promise<AvailablePhoneNumber[]> {
    // Cache popular searches
    const cacheKey = `available_numbers_${JSON.stringify(searchRequest)}`
    const cached = await this.cache.get(cacheKey)
    if (cached) return cached

    const numbers = await this.provider.searchAvailableNumbers(searchRequest)
    await this.cache.set(cacheKey, numbers, 300) // 5 min cache
    
    return numbers
  }

  async purchaseNumbers(
    userId: string,
    purchaseRequest: PurchaseRequest
  ): Promise<PurchaseResult> {
    // Validate user billing info
    await this.validateBillingInfo(userId, purchaseRequest.billing_address)
    
    // Process payment
    const paymentResult = await this.billingService.processPayment(
      purchaseRequest.payment_method_id,
      this.calculateTotalCost(purchaseRequest.number_ids)
    )

    if (!paymentResult.success) {
      throw new Error('Payment failed')
    }

    // Purchase numbers from provider
    const results = await Promise.allSettled(
      purchaseRequest.number_ids.map(id => 
        this.provider.purchaseNumber(id, {
          userId,
          billingAddress: purchaseRequest.billing_address
        })
      )
    )

    // Save to database
    const successfulPurchases = results
      .filter(r => r.status === 'fulfilled')
      .map(r => (r as PromiseFulfilledResult<PurchasedNumber>).value)

    await this.savePhoneNumbers(userId, successfulPurchases)

    return {
      successful: successfulPurchases.length,
      failed: results.length - successfulPurchases.length,
      phone_numbers: successfulPurchases
    }
  }
}
```

## Testkriterien

### Unit Tests
- [ ] Provider-Integration-Adapter
- [ ] Search-Query-Builder
- [ ] Pricing-Calculation-Logic
- [ ] Purchase-Flow-Validation

### Integration Tests
- [ ] End-to-End-Purchase-Flow
- [ ] Payment-Processing-Integration
- [ ] Provider-API-Integration
- [ ] Cart-State-Management

### UI Tests
- [ ] Marktplatz-Navigation und -Filter
- [ ] Shopping-Cart-Funktionalität
- [ ] Purchase-Checkout-Flow
- [ ] Error-Handling und Loading-States

### Performance Tests
- [ ] Search-Performance bei vielen Nummern
- [ ] Cart-Update-Performance
- [ ] Concurrent-Purchase-Handling
- [ ] Provider-API-Response-Times

## Sicherheitsüberlegungen

### Payment Security
- PCI-DSS-konforme Payment-Verarbeitung
- Secure Token-basierte Payment-Methods
- Encryption aller sensitiven Daten
- Fraud-Detection für ungewöhnliche Käufe

### Data Protection
- GDPR-konforme Speicherung von Kundendaten
- Right-to-be-forgotten Implementation
- Minimal Data Collection
- Audit-Logging für alle Transaktionen

## Definition of Done
- [ ] Alle Akzeptanzkriterien erfüllt
- [ ] Telefonnummer-Marktplatz mit Such- und Filterfunktionen
- [ ] Shopping-Cart-System für Bulk-Käufe
- [ ] Sichere Payment-Integration
- [ ] Provider-API-Integration (Twilio)
- [ ] Database-Schema für gekaufte Nummern
- [ ] Unit-, Integration- und Performance-Tests
- [ ] UI/UX für Marktplatz und Checkout
- [ ] Security-Review für Payment-Flow
- [ ] Code Review und Documentation

## Abhängigkeiten
- **Payment Provider**: Stripe oder PayPal-Integration
- **Telephony Provider**: Twilio-Account und API-Keys
- **Billing System**: Für Kostenverfolgung
- **Legal Review**: AGB für Telefonnummer-Käufe

## Risiken
- **Provider-Verfügbarkeit**: Telefonnummern können schnell ausverkauft sein
- **Payment-Komplexität**: Internationale Zahlungen und Steuern
- **Regulatory-Compliance**: Verschiedene Länder haben unterschiedliche Regeln
- **API-Rate-Limits**: Provider-APIs haben Beschränkungen

## Nächste Schritte nach Completion
- **Story 6.2**: Telefonnummer-Verwaltung
- **Story 6.3**: Anruf-Handling und -protokollierung