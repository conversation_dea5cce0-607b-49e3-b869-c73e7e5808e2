# Story 5.1: Wissensdatenbank-Verwaltung

## Story-Information
**Epic**: Epic 5 - Wissensbasis-Integration (RAG)  
**Story Points**: 8  
**Priorität**: Hoch  
**Status**: Ready for Development  

## User Story
**Als** registrierter <PERSON>  
**möchte ich** Wissensdatenbanken erstellen, anzeigen, bearbeiten und löschen können  
**sodass** ich meine Dokumente und Informationen organisiert verwalten kann.

## Beschreibung
Diese Story implementiert die grundlegende CRUD-Funktionalität für Wissensdatenbanken. Benutzer können mehrere Wissensdatenbanken erstellen, um verschiedene Themenbereiche oder Projekte zu organisieren.

## Akzeptanzkriterien

### AC1: Wissensdatenbank-Übersicht anzeigen
- [ ] Als <PERSON>utzer kann ich alle meine Wissensdatenbanken in einer Übersicht sehen
- [ ] Die Liste zeigt Name, Beschreibung, Anzahl Dokumente und Status an
- [ ] Ich kann zwischen aktiven und inaktiven Wissensdatenbanken filtern
- [ ] Die Übersicht ist responsive und benutzerfreundlich

### AC2: Neue Wissensdatenbank erstellen
- [ ] Als Benutzer kann ich eine neue Wissensdatenbank erstellen
- [ ] Ich muss einen Namen eingeben (Pflichtfeld)
- [ ] Ich kann optional eine Beschreibung hinzufügen
- [ ] Der Status wird standardmäßig auf "aktiv" gesetzt
- [ ] Nach dem Erstellen werde ich zur Detail-Ansicht weitergeleitet

### AC3: Wissensdatenbank-Details anzeigen
- [ ] Als Benutzer kann ich Details einer Wissensdatenbank einsehen
- [ ] Ich sehe alle Metadaten (Name, Beschreibung, Erstelldatum, Status)
- [ ] Ich sehe eine Übersicht der enthaltenen Dokumente
- [ ] Ich kann direkt Aktionen wie Bearbeiten oder Löschen ausführen

### AC4: Wissensdatenbank bearbeiten
- [ ] Als Benutzer kann ich Name und Beschreibung einer Wissensdatenbank ändern
- [ ] Ich kann den Status zwischen aktiv und inaktiv wechseln
- [ ] Änderungen werden sofort gespeichert
- [ ] Ich erhalte Feedback über erfolgreiche Änderungen

### AC5: Wissensdatenbank löschen
- [ ] Als Benutzer kann ich eine Wissensdatenbank löschen
- [ ] Ich muss die Löschung in einem Dialog bestätigen
- [ ] Beim Löschen werden alle verknüpften Dokumente und Daten entfernt
- [ ] Ich werde über die Konsequenzen der Löschung informiert

## Technische Anforderungen

### API-Endpoints
```typescript
GET    /api/knowledge-bases          // Liste aller Wissensdatenbanken
POST   /api/knowledge-bases          // Neue Wissensdatenbank erstellen
GET    /api/knowledge-bases/[id]     // Einzelne Wissensdatenbank
PUT    /api/knowledge-bases/[id]     // Wissensdatenbank aktualisieren
DELETE /api/knowledge-bases/[id]     // Wissensdatenbank löschen
```

### Datenmodell
```typescript
interface KnowledgeBase {
  id: string
  user_id: string
  created_at: string
  updated_at: string
  name: string
  description?: string
  status: 'active' | 'inactive'
  document_count?: number
}

interface CreateKnowledgeBaseRequest {
  name: string
  description?: string
  status?: 'active' | 'inactive'
}

interface UpdateKnowledgeBaseRequest {
  name?: string
  description?: string
  status?: 'active' | 'inactive'
}
```

### Datenbankschema
```sql
CREATE TABLE knowledge_bases (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  name TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive'))
);

-- Row Level Security
ALTER TABLE knowledge_bases ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own knowledge bases" ON knowledge_bases
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own knowledge bases" ON knowledge_bases
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own knowledge bases" ON knowledge_bases
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own knowledge bases" ON knowledge_bases
  FOR DELETE USING (auth.uid() = user_id);
```

## UI/UX Anforderungen

### Seiten-Struktur
- `/rag` - Übersicht aller Wissensdatenbanken
- `/rag/new` - Neue Wissensdatenbank erstellen
- `/rag/[id]` - Details einer Wissensdatenbank
- `/rag/[id]/edit` - Wissensdatenbank bearbeiten

### Komponenten
- `KnowledgeBaseList` - Liste mit Filterfunktion
- `KnowledgeBaseCard` - Einzelne KB in der Übersicht
- `KnowledgeBaseForm` - Erstellen/Bearbeiten Formular
- `KnowledgeBaseDetails` - Detail-Ansicht
- `DeleteKnowledgeBaseDialog` - Lösch-Bestätigung

### Design-Anforderungen
- Konsistent mit vorhandener App-UI (Shadcn/UI)
- Responsive Design für alle Bildschirmgrößen
- Loading-States für alle Aktionen
- Error-Handling mit benutzerfreundlichen Meldungen
- Success-Toasts für Aktionen

## Testkriterien

### Unit Tests
- [ ] API-Route-Handler für alle CRUD-Operationen
- [ ] Validierung von Eingabedaten
- [ ] Error-Handling für verschiedene Szenarien
- [ ] Database-Operationen mit Mocking

### Integration Tests
- [ ] End-to-End CRUD-Flow
- [ ] Authentication und Authorization
- [ ] Database-Constraints und RLS-Policies

### UI Tests
- [ ] Formular-Validierung
- [ ] Loading-States und Error-Handling
- [ ] Navigation zwischen Seiten
- [ ] Responsive Design auf verschiedenen Geräten

## Definition of Done
- [ ] Alle Akzeptanzkriterien erfüllt
- [ ] API-Endpoints implementiert und getestet
- [ ] Frontend-Komponenten erstellt
- [ ] Database-Schema deployed
- [ ] Unit- und Integration-Tests geschrieben
- [ ] UI/UX Review abgeschlossen
- [ ] Code Review durchgeführt
- [ ] Performance-Tests bestanden
- [ ] Documentation aktualisiert

## Abhängigkeiten
- **Epic 2**: Agent-Management (für spätere Verknüpfung)
- **Supabase Setup**: Database und Authentication
- **UI-Komponenten**: Basis-Komponenten verfügbar

## Risiken
- **Komplexität**: Erste RAG-Story, Architektur muss stimmen
- **Performance**: Skalierung bei vielen Wissensdatenbanken
- **Security**: RLS-Policies müssen korrekt implementiert sein

## Nächste Schritte nach Completion
Nach Abschluss dieser Story können die folgenden Stories begonnen werden:
- **Story 5.2**: Dokumenten-Upload und -verarbeitung
- **Story 5.3**: RAG-Integration für Agenten