# Story 6.3: <PERSON><PERSON><PERSON>-Handling und -protokollierung

## Story-Information
**Epic**: Epic 6 - Telefonie-Integration  
**Story Points**: 13  
**Priorität**: Hoch  
**Status**: Ready for Development  

## User Story
**Als** registrierter <PERSON>er  
**möchte ich** dass eingehende Anrufe automatisch von meinen Agenten bearbeitet und vollständig protokolliert werden  
**sodass** ich eine lückenlose Dokumentation aller Gespräche habe.

## Beschreibung
Diese Story implementiert das Herzstück der Telefonie-Integration: die automatische Verarbeitung eingehender Anrufe durch KI-Agenten und die vollständige Protokollierung aller Gesprächsdaten für Analyse und Compliance.

## Akzeptanzkriterien

### AC1: Eingehende Anrufe verarbeiten
- [ ] Eingehende Anrufe werden automatisch an den zugewiesenen Agenten weitergeleitet
- [ ] Der Agent antwortet automatisch mit der konfigurierten Begrüßung
- [ ] Gespräche werden in Echtzeit zwischen Anrufer und Agent geführt
- [ ] Bei technischen Problemen gibt es ein Fallback-System
- [ ] Anrufe außerhalb der Geschäftszeiten werden entsprechend behandelt

### AC2: Echtzeit-Gesprächsverarbeitung
- [ ] Sprache wird in Echtzeit in Text umgewandelt (Speech-to-Text)
- [ ] Agent-Antworten werden in Echtzeit generiert
- [ ] Text wird zurück in Sprache umgewandelt (Text-to-Speech)
- [ ] Latenz wird minimiert für natürliche Gespräche
- [ ] Gespräche können bei Bedarf manuell übernommen werden

### AC3: Vollständige Anrufprotokollierung
- [ ] Alle Anrufdaten werden automatisch gespeichert (Start, Ende, Dauer)
- [ ] Anrufer-Informationen werden erfasst (Nummer, ggf. Name)
- [ ] Vollständige Transkripte werden erstellt und gespeichert
- [ ] Agent-Aktionen und verwendete Tools werden protokolliert
- [ ] Kosten pro Anruf werden berechnet und gespeichert

### AC4: Anrufaufzeichnungen verwalten
- [ ] Audio-Aufzeichnungen werden sicher gespeichert (opt-in)
- [ ] Aufzeichnungen können über die UI abgespielt werden
- [ ] Download von Aufzeichnungen ist möglich
- [ ] Datenschutz-konforme Speicherung und Löschung
- [ ] Aufzeichnungen sind mit Anrufprotokollen verknüpft

### AC5: Live-Monitoring und Intervention
- [ ] Aktive Anrufe werden in der Monitoring-Ansicht angezeigt
- [ ] Supervisoren können Gespräche live mithören
- [ ] Manuelle Übernahme von Gesprächen ist möglich
- [ ] Notfall-Eskalation bei kritischen Situationen
- [ ] Echtzeit-Alerts bei Problemen

## Technische Anforderungen

### Webhook-Endpoints für Provider
```typescript
// Twilio-Webhooks
POST   /api/webhooks/twilio/incoming-call      // Eingehender Anruf
POST   /api/webhooks/twilio/call-status        // Anruf-Status-Updates
POST   /api/webhooks/twilio/recording-status   // Aufzeichnungs-Status
POST   /api/webhooks/twilio/transcription      // Transkript-Updates

// Agent-Processing
POST   /api/calls/[id]/process-speech          // Speech-to-Text verarbeiten
POST   /api/calls/[id]/generate-response       // Agent-Antwort generieren
POST   /api/calls/[id]/synthesize-speech       // Text-to-Speech
POST   /api/calls/[id]/manual-takeover         // Manuelle Übernahme
```

### Datenmodell für Anrufprotokolle
```typescript
interface CallLog {
  id: string
  phone_number_id: string
  agent_id: string
  created_at: string
  updated_at: string
  
  // Anruf-Metadaten
  caller_number: string
  caller_name?: string
  start_time: string
  end_time?: string
  duration_seconds?: number
  
  // Status und Ergebnis
  status: 'ringing' | 'in_progress' | 'completed' | 'failed' | 'missed'
  end_reason?: 'caller_hangup' | 'agent_hangup' | 'system_error' | 'timeout' | 'manual_takeover'
  success_rating?: number // 1-5 Bewertung des Gesprächsverlaufs
  
  // Content und Protokollierung
  transcript: TranscriptEntry[]
  full_transcript_url?: string
  recording_url?: string
  recording_enabled: boolean
  
  // Kosten und Metriken
  cost_breakdown: CallCostBreakdown
  quality_metrics: CallQualityMetrics
  
  // Metadaten
  metadata: {
    user_agent?: string
    call_quality?: string
    agent_version?: string
    processing_stats?: ProcessingStats
  }
}

interface TranscriptEntry {
  timestamp: string
  speaker: 'caller' | 'agent'
  text: string
  confidence?: number
  processing_time_ms?: number
}

interface CallCostBreakdown {
  duration_cost: number
  recording_cost?: number
  transcription_cost?: number
  ai_processing_cost?: number
  total_cost: number
  currency: string
}

interface CallQualityMetrics {
  average_response_time_ms: number
  speech_recognition_accuracy?: number
  agent_response_quality?: number
  caller_satisfaction?: number
  technical_issues: string[]
}

interface ProcessingStats {
  total_llm_tokens: number
  average_latency_ms: number
  errors_encountered: number
  fallback_activations: number
}
```

### Datenbankschema
```sql
CREATE TABLE call_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number_id UUID REFERENCES phone_numbers(id) ON DELETE CASCADE,
  agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Anruf-Metadaten
  caller_number TEXT NOT NULL,
  caller_name TEXT,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  duration_seconds INTEGER,
  
  -- Status
  status TEXT NOT NULL DEFAULT 'ringing' CHECK (
    status IN ('ringing', 'in_progress', 'completed', 'failed', 'missed')
  ),
  end_reason TEXT CHECK (
    end_reason IN ('caller_hangup', 'agent_hangup', 'system_error', 'timeout', 'manual_takeover')
  ),
  success_rating INTEGER CHECK (success_rating >= 1 AND success_rating <= 5),
  
  -- Content
  transcript JSONB DEFAULT '[]',
  full_transcript_url TEXT,
  recording_url TEXT,
  recording_enabled BOOLEAN DEFAULT false,
  
  -- Kosten und Metriken
  cost_breakdown JSONB DEFAULT '{}',
  quality_metrics JSONB DEFAULT '{}',
  metadata JSONB DEFAULT '{}'
);

-- Transcript-Entries für bessere Suche
CREATE TABLE call_transcript_entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  call_log_id UUID REFERENCES call_logs(id) ON DELETE CASCADE,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  speaker TEXT NOT NULL CHECK (speaker IN ('caller', 'agent')),
  text TEXT NOT NULL,
  confidence DECIMAL,
  processing_time_ms INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes für Performance
CREATE INDEX idx_call_logs_phone_number ON call_logs(phone_number_id);
CREATE INDEX idx_call_logs_agent ON call_logs(agent_id);
CREATE INDEX idx_call_logs_status ON call_logs(status);
CREATE INDEX idx_call_logs_start_time ON call_logs(start_time);
CREATE INDEX idx_call_logs_caller_number ON call_logs(caller_number);

CREATE INDEX idx_transcript_entries_call ON call_transcript_entries(call_log_id);
CREATE INDEX idx_transcript_entries_timestamp ON call_transcript_entries(timestamp);

-- Full-Text-Search für Transkripte
CREATE INDEX idx_transcript_entries_text ON call_transcript_entries USING gin(to_tsvector('german', text));
```

## Call Processing Pipeline

### 1. Incoming Call Webhook Handler
```typescript
export class IncomingCallHandler {
  async handleIncomingCall(req: IncomingCallRequest): Promise<TwiMLResponse> {
    const { CallSid, From, To, CallerName } = req.body

    // Telefonnummer und Agent ermitteln
    const phoneNumber = await this.phoneService.getByNumber(To)
    if (!phoneNumber || !phoneNumber.assigned_agent_id) {
      return this.createErrorResponse('Number not configured')
    }

    const agent = await this.agentService.getAgent(phoneNumber.assigned_agent_id)
    
    // Call-Log erstellen
    const callLog = await this.callService.createCallLog({
      phoneNumberId: phoneNumber.id,
      agentId: agent.id,
      callerNumber: From,
      callerName: CallerName,
      providerCallId: CallSid,
      status: 'ringing'
    })

    // Geschäftszeiten prüfen
    if (!this.isWithinBusinessHours(phoneNumber.configuration.business_hours)) {
      return this.handleAfterHoursCall(phoneNumber, callLog)
    }

    // TwiML für Agent-Call generieren
    return this.createAgentCallTwiML(agent, callLog.id)
  }

  private createAgentCallTwiML(agent: Agent, callLogId: string): TwiMLResponse {
    const twiml = new VoiceResponse()
    
    // Begrüßung abspielen
    twiml.say({
      voice: agent.voice,
      language: agent.language
    }, agent.system_prompt.split('\n')[0]) // Erste Zeile als Begrüßung

    // WebSocket-Stream für Echtzeit-Verarbeitung starten
    const stream = twiml.start()
    stream.stream({
      name: 'agent-stream',
      url: `wss://${process.env.BASE_URL}/ws/calls/${callLogId}/stream`,
      track: 'both_tracks'
    })

    // Anruf aufzeichnen (falls konfiguriert)
    if (agent.recording_enabled) {
      twiml.record({
        action: `/api/webhooks/twilio/recording-complete/${callLogId}`,
        transcribe: true,
        transcribeCallback: `/api/webhooks/twilio/transcription/${callLogId}`
      })
    }

    return twiml.toString()
  }
}
```

### 2. Real-time Speech Processing
```typescript
export class RealTimeSpeechProcessor {
  constructor(
    private readonly speechToText: SpeechToTextService,
    private readonly textToSpeech: TextToSpeechService,
    private readonly agentService: AgentService
  ) {}

  async processCallStream(
    callLogId: string,
    audioStream: AudioStream
  ): Promise<void> {
    const callLog = await this.callService.getCallLog(callLogId)
    const agent = await this.agentService.getAgent(callLog.agent_id)

    // Audio-Stream in Chunks verarbeiten
    audioStream.on('audio', async (audioChunk) => {
      try {
        // Speech-to-Text
        const transcription = await this.speechToText.transcribe(audioChunk, {
          language: agent.language,
          speaker: 'caller'
        })

        if (transcription.text) {
          // Transcript speichern
          await this.callService.addTranscriptEntry(callLogId, {
            timestamp: new Date().toISOString(),
            speaker: 'caller',
            text: transcription.text,
            confidence: transcription.confidence
          })

          // Agent-Antwort generieren
          const agentResponse = await this.agentService.processMessage(
            agent.id,
            transcription.text,
            this.buildCallContext(callLog)
          )

          // Text-to-Speech
          const audioResponse = await this.textToSpeech.synthesize(
            agentResponse.text,
            {
              voice: agent.voice,
              language: agent.language
            }
          )

          // Audio zurück an Anrufer senden
          audioStream.write(audioResponse.audioBuffer)

          // Agent-Antwort im Transcript speichern
          await this.callService.addTranscriptEntry(callLogId, {
            timestamp: new Date().toISOString(),
            speaker: 'agent',
            text: agentResponse.text,
            processing_time_ms: agentResponse.processing_time
          })

          // Kosten aktualisieren
          await this.updateCallCosts(callLogId, agentResponse.tokens_used)
        }
      } catch (error) {
        console.error('Error processing speech:', error)
        await this.handleProcessingError(callLogId, error)
      }
    })

    audioStream.on('end', async () => {
      await this.finalizeCall(callLogId)
    })
  }

  private buildCallContext(callLog: CallLog): AgentContext {
    return {
      type: 'phone_call',
      caller_number: callLog.caller_number,
      call_duration: this.calculateDuration(callLog.start_time),
      previous_transcript: callLog.transcript.slice(-10), // Letzte 10 Einträge
      call_metadata: callLog.metadata
    }
  }
}
```

### 3. Manual Takeover System
```typescript
export class ManualTakeoverService {
  async initiateTakeover(
    callLogId: string,
    supervisorId: string,
    reason: string
  ): Promise<TakeoverSession> {
    const callLog = await this.callService.getCallLog(callLogId)
    
    if (callLog.status !== 'in_progress') {
      throw new Error('Call is not active')
    }

    // Takeover-Session erstellen
    const session = await this.createTakeoverSession({
      callLogId,
      supervisorId,
      reason,
      startTime: new Date()
    })

    // Agent pausieren
    await this.pauseAgentProcessing(callLogId)

    // WebRTC-Verbindung für Supervisor einrichten
    await this.setupSupervisorConnection(session.id, callLog.provider_call_id)

    // Call-Log aktualisieren
    await this.callService.updateCallLog(callLogId, {
      status: 'manual_takeover',
      metadata: {
        ...callLog.metadata,
        takeover_session_id: session.id,
        takeover_reason: reason
      }
    })

    // Real-time Update an Monitoring-Dashboard
    await this.notificationService.notifyTakeover(callLogId, supervisorId)

    return session
  }

  async endTakeover(sessionId: string): Promise<void> {
    const session = await this.getTakeoverSession(sessionId)
    const callLog = await this.callService.getCallLog(session.call_log_id)

    // Agent-Processing wieder aktivieren
    await this.resumeAgentProcessing(session.call_log_id)

    // Session beenden
    await this.updateTakeoverSession(sessionId, {
      endTime: new Date(),
      status: 'completed'
    })

    // Call-Log zurücksetzen
    await this.callService.updateCallLog(session.call_log_id, {
      status: 'in_progress'
    })
  }
}
```

## Live-Monitoring Integration

### WebSocket für Real-time Updates
```typescript
export class CallMonitoringWebSocket {
  constructor(private readonly wsServer: WebSocketServer) {
    this.setupEventHandlers()
  }

  private setupEventHandlers(): void {
    // Call-Status-Updates
    this.eventBus.on('call.status_changed', (event) => {
      this.broadcast('call_status_update', {
        callId: event.callId,
        status: event.newStatus,
        timestamp: event.timestamp
      })
    })

    // Neue Transcript-Einträge
    this.eventBus.on('call.transcript_entry', (event) => {
      this.broadcast('transcript_update', {
        callId: event.callId,
        entry: event.transcriptEntry
      })
    })

    // Takeover-Events
    this.eventBus.on('call.takeover_initiated', (event) => {
      this.broadcast('takeover_alert', {
        callId: event.callId,
        supervisorId: event.supervisorId,
        reason: event.reason
      })
    })
  }

  private broadcast(eventType: string, data: any): void {
    this.wsServer.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify({
          type: eventType,
          data,
          timestamp: new Date().toISOString()
        }))
      }
    })
  }
}
```

## Testkriterien

### Unit Tests
- [ ] Webhook-Handler für alle Provider-Events
- [ ] Speech-Processing-Pipeline
- [ ] Call-Log-CRUD-Operationen
- [ ] Cost-Calculation-Logic
- [ ] Manual-Takeover-System

### Integration Tests
- [ ] End-to-End-Call-Flow
- [ ] Provider-Webhook-Integration
- [ ] Real-time-Speech-Processing
- [ ] Recording und Transcription
- [ ] Live-Monitoring-Updates

### Performance Tests
- [ ] Concurrent-Call-Handling
- [ ] Speech-Processing-Latency
- [ ] Database-Performance bei vielen Calls
- [ ] WebSocket-Scaling

### Telephony Tests
- [ ] Actual Phone Calls mit Test-Nummern
- [ ] Speech-to-Text-Accuracy
- [ ] Text-to-Speech-Quality
- [ ] Call-Quality-Metriken

## Error-Handling und Fallbacks

### Provider-Fehler
- Connection-Lost → Automatic Retry mit Exponential Backoff
- Speech-Recognition-Error → Fallback auf DTMF-Navigation
- TTS-Service-Down → Pre-recorded Fallback-Messages
- Webhook-Timeout → Asynchronous Processing

### Processing-Fehler
- LLM-API-Error → Standard-Responses mit Escalation
- High-Latency → Quality-Degradation mit Warnings
- Context-Too-Large → Automatic Summarization
- Memory-Issues → Circuit-Breaker-Pattern

## Definition of Done
- [ ] Alle Akzeptanzkriterien erfüllt
- [ ] Webhook-Handler für alle Call-Events
- [ ] Real-time Speech-Processing-Pipeline
- [ ] Vollständige Call-Protokollierung
- [ ] Audio-Recording-Management
- [ ] Manual-Takeover-System
- [ ] Live-Monitoring-Integration
- [ ] Cost-Tracking für alle Call-Components
- [ ] Database-Schema mit optimierten Queries
- [ ] Error-Handling und Fallback-Mechanismen
- [ ] Unit-, Integration- und Performance-Tests
- [ ] Actual-Telephony-Tests
- [ ] Security-Review für Webhook-Endpoints
- [ ] GDPR-konforme Data-Handling
- [ ] Code Review und Documentation

## Abhängigkeiten
- **Story 6.2**: Telefonnummer-Verwaltung (für Agent-Assignment)
- **Epic 3**: Live-Monitoring (für Real-time Updates)
- **Speech Services**: Speech-to-Text und Text-to-Speech APIs
- **Provider Integration**: Twilio-Webhook-System
- **WebSocket Infrastructure**: Für Real-time Features

## Risiken
- **Latency-Issues**: Speech-Processing kann zu langsam sein
- **Speech-Quality**: Schlechte Anrufqualität beeinträchtigt Recognition
- **Scalability**: Viele gleichzeitige Anrufe sind ressourcenintensiv
- **Cost-Management**: AI-Processing kann teuer werden

## Nächste Schritte nach Completion
- **Story 6.4**: Abrechnungs-Integration
- **Story 6.5**: Erweiterte Anruf-Features
- **Performance-Optimization**: Latency-Tuning für Production