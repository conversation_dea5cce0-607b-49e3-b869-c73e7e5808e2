# Story 1: Agent-Performance Dashboard erstellen

## Story-Information

**Epic**: Epic 3 - Agent-Monitoring und Analytics  
**Story ID**: E3-S1  
**Titel**: Agent-Performance Dashboard erstellen  
**Priorität**: Hoch  
**Aufwand**: 8 Story Points  
**Status**: Bereit zur Implementierung  
**Assignee**: TBD  
**Sprint**: TBD  

## User Story

**Als** Administrator der JASZ-AI WebApp  
**möchte ich** ein zentrales Dashboard zur Überwachung der Agent-Performance  
**damit ich** einen schnellen Überblick über den Status und die Leistung aller Agenten erhalte.

## Geschäftswert

- **Effizienzsteigerung**: 30% schnellere Problemerkennung
- **Kosteneinsparung**: Reduzierung manueller Überwachung um 50%
- **Qualitätsverbesserung**: Proaktive Performance-Optimierung
- **Entscheidungsunterstützung**: Datenbasierte Agent-Verwaltung

## Akzeptanzkriterien

### AC1: Übersichtskarten für aktive Agenten
**Priorität**: Muss  
**Aufwand**: 3 SP

- [ ] Dashboard zeigt <PERSON>rten für alle aktiven Agenten
- [ ] Jede Karte enthält:
  - Agent-Name und Avatar
  - Aktueller Status (aktiv/idle/fehler/inaktiv)
  - Anzahl aktuelle Gespräche
  - Letzte Aktivität (Zeitstempel)
- [ ] Farbkodierung: Grün (aktiv), Gelb (idle), Rot (Fehler), Grau (inaktiv)
- [ ] Klick auf Karte führt zu Agent-Details
- [ ] Responsive Grid-Layout (1-4 Spalten je nach Bildschirmgröße)

### AC2: Echtzeit-Status-Anzeige
**Priorität**: Muss  
**Aufwand**: 2 SP

- [ ] Status-Updates erfolgen in Echtzeit (max. 5 Sekunden Verzögerung)
- [ ] WebSocket-Verbindung für Live-Updates
- [ ] Automatische Reconnection bei Verbindungsabbruch
- [ ] Status-Indikator für WebSocket-Verbindung (grün/rot)
- [ ] Fallback auf Polling bei WebSocket-Problemen

### AC3: Performance-Metriken anzeigen
**Priorität**: Muss  
**Aufwand**: 2 SP

- [ ] Anzeige der heutigen Metriken pro Agent:
  - Anzahl Anrufe (gesamt)
  - Erfolgreiche Anrufe (mit Prozentsatz)
  - Durchschnittliche Gesprächsdauer
  - Aktuelle Auslastung (%)
- [ ] Vergleich zu gestern (Trend-Pfeile: ↑↓→)
- [ ] Aggregierte Metriken für alle Agenten im Header
- [ ] Tooltips mit detaillierten Erklärungen

### AC4: Dashboard-Navigation und Filter
**Priorität**: Sollte  
**Aufwand**: 1 SP

- [ ] Suchfunktion für Agenten (Name, ID)
- [ ] Filter-Optionen:
  - Alle Agenten
  - Nur aktive
  - Nur inaktive
  - Nur mit Fehlern
- [ ] Sortierung nach: Name, Status, Aktivität, Performance
- [ ] Sortierreihenfolge: Aufsteigend/Absteigend

## Technische Spezifikation

### Komponenten-Architektur

```typescript
// Hauptkomponente
app/dashboard/page.tsx
components/dashboard/AgentPerformanceDashboard.tsx

// Unterkomponenten
components/dashboard/AgentCard.tsx
components/dashboard/MetricsOverview.tsx
components/dashboard/StatusIndicator.tsx
components/dashboard/DashboardFilters.tsx
components/dashboard/ConnectionStatus.tsx

// Hooks
hooks/useAgentMetrics.ts
hooks/useRealtimeStatus.ts
hooks/useWebSocketConnection.ts

// Services
services/dashboardService.ts
services/webSocketService.ts

// Types
types/dashboard.ts
types/metrics.ts
```

### Datenstrukturen

```typescript
interface AgentDashboardData {
  id: string
  name: string
  avatar?: string
  status: 'active' | 'idle' | 'error' | 'inactive'
  currentCalls: number
  lastActivity: Date
  todayMetrics: {
    totalCalls: number
    successfulCalls: number
    successRate: number
    averageDuration: number // in seconds
    utilization: number // percentage
  }
  yesterdayComparison: {
    callsChange: number // percentage
    successRateChange: number // percentage
    durationChange: number // percentage
  }
}

interface DashboardSummary {
  totalAgents: number
  activeAgents: number
  totalCallsToday: number
  averageSuccessRate: number
  totalUtilization: number
}

interface DashboardFilters {
  search: string
  status: 'all' | 'active' | 'inactive' | 'error'
  sortBy: 'name' | 'status' | 'activity' | 'performance'
  sortOrder: 'asc' | 'desc'
}
```

### API-Endpoints

```typescript
// REST APIs
GET /api/dashboard/agents
// Response: AgentDashboardData[]

GET /api/dashboard/summary
// Response: DashboardSummary

// WebSocket Events
interface WebSocketEvents {
  'agent:status_changed': {
    agentId: string
    status: AgentStatus
    timestamp: Date
  }
  'agent:metrics_updated': {
    agentId: string
    metrics: Partial<AgentDashboardData['todayMetrics']>
  }
  'dashboard:summary_updated': DashboardSummary
}
```

## Implementierungsplan

### Phase 1: Grundstruktur (2 Tage)
- [ ] Dashboard-Layout und Routing erstellen
- [ ] AgentCard-Komponente entwickeln
- [ ] Basis-Styling mit Tailwind CSS
- [ ] Responsive Grid-System

### Phase 2: Datenintegration (2 Tage)
- [ ] API-Endpoints implementieren
- [ ] TypeScript-Interfaces definieren
- [ ] Dashboard-Service erstellen
- [ ] Daten-Hooks entwickeln

### Phase 3: Echtzeit-Features (2 Tage)
- [ ] WebSocket-Service implementieren
- [ ] Live-Updates für Agent-Status
- [ ] Connection-Status-Anzeige
- [ ] Error Handling und Reconnection

### Phase 4: Erweiterte Features (2 Tage)
- [ ] Such- und Filterfunktionen
- [ ] Sortierung implementieren
- [ ] Performance-Optimierungen
- [ ] Loading States und Error Handling

## Testplan

### Unit Tests (Ziel: >90% Coverage)
- [ ] AgentCard-Komponente
  - Rendering verschiedener Status
  - Klick-Events
  - Metriken-Anzeige
- [ ] Dashboard-Hooks
  - useAgentMetrics
  - useRealtimeStatus
  - useWebSocketConnection
- [ ] Utility-Funktionen
  - Sortierung und Filterung
  - Datenformatierung

### Integration Tests
- [ ] API-Integration
  - Dashboard-Daten laden
  - Error-Handling
- [ ] WebSocket-Verbindung
  - Live-Updates
  - Reconnection-Logic
- [ ] Datenfluss Ende-zu-Ende

### E2E Tests
- [ ] Dashboard-Navigation
  - Laden der Seite
  - Agent-Karten anzeigen
- [ ] Filter- und Suchfunktionen
  - Suche nach Agent-Namen
  - Status-Filter anwenden
- [ ] Responsive Verhalten
  - Mobile Ansicht
  - Tablet Ansicht

## Performance-Anforderungen

### Ladezeiten
- [ ] Initiales Dashboard-Laden: < 2 Sekunden
- [ ] Agent-Karten-Rendering: < 500ms
- [ ] WebSocket-Verbindung: < 1 Sekunde
- [ ] Filter-/Sortier-Operationen: < 200ms

### Skalierbarkeit
- [ ] Unterstützt bis zu 100 Agenten ohne Performance-Einbußen
- [ ] Lazy Loading für große Agent-Listen (>50)
- [ ] Optimierte Re-Rendering (React.memo, useMemo)
- [ ] Memory-Management für WebSocket-Daten

## Accessibility-Anforderungen

- [ ] WCAG 2.1 AA konform
- [ ] Keyboard-Navigation für alle Funktionen
- [ ] Screen Reader Unterstützung
- [ ] Ausreichende Farbkontraste (min. 4.5:1)
- [ ] Focus-Indikatoren sichtbar
- [ ] Alt-Texte für alle visuellen Elemente

## Definition of Done

### Funktional
- [ ] Alle Akzeptanzkriterien erfüllt
- [ ] Dashboard lädt und zeigt Agent-Daten korrekt an
- [ ] Echtzeit-Updates funktionieren zuverlässig
- [ ] Such- und Filterfunktionen arbeiten korrekt
- [ ] Responsive Design auf allen Geräten

### Technisch
- [ ] Code Review abgeschlossen
- [ ] Unit Tests mit >90% Coverage
- [ ] Integration Tests erfolgreich
- [ ] Performance-Tests bestanden
- [ ] Accessibility-Tests erfolgreich

### Qualität
- [ ] Deutsche Lokalisierung vollständig
- [ ] Error Handling implementiert
- [ ] Loading States für alle Operationen
- [ ] Dokumentation aktualisiert

### Deployment
- [ ] Deployment in Staging erfolgreich
- [ ] Smoke Tests in Staging bestanden
- [ ] Stakeholder-Abnahme erhalten

## Risiken und Mitigation

### Technische Risiken
| Risiko | Wahrscheinlichkeit | Impact | Mitigation |
|--------|-------------------|---------|------------|
| WebSocket-Performance bei vielen Agenten | Mittel | Hoch | Connection Pooling, Rate Limiting |
| Hoher Memory-Verbrauch | Niedrig | Mittel | Lazy Loading, Memory-Management |
| API-Latenz | Niedrig | Mittel | Caching, Optimierte Queries |

### Geschäftsrisiken
| Risiko | Wahrscheinlichkeit | Impact | Mitigation |
|--------|-------------------|---------|------------|
| Benutzerakzeptanz | Niedrig | Mittel | UX-Tests, Iterative Verbesserungen |
| Scope Creep | Mittel | Niedrig | Klare Abgrenzung der Anforderungen |

## Abhängigkeiten

### Interne Abhängigkeiten
- **WebSocket-Infrastruktur**: Backend-Support für Live-Updates
- **Agent-Datenmodell**: Erweiterte Metriken aus Epic 2
- **Authentifizierung**: Rollenbasierte Zugriffskontrolle

### Externe Abhängigkeiten
- **Supabase Real-time**: Für WebSocket-Verbindungen
- **Recharts**: Für kleine Metriken-Visualisierungen

## Notizen

- Dashboard sollte als Einstiegspunkt für alle Monitoring-Features dienen
- Berücksichtigung zukünftiger Erweiterungen (Alerts, detaillierte Analytics)
- Performance ist kritisch für die Benutzerakzeptanz
- Mobile-First Ansatz für responsive Design

---

**Erstellt**: 2025-01-29  
**Letzte Aktualisierung**: 2025-01-29  
**Version**: 1.0  
**Nächste Review**: Bei Sprint-Planning
