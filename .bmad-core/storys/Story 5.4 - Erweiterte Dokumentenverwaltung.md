# Story 5.4: Erweiterte Dokumentenverwaltung

## Story-Information
**Epic**: Epic 5 - Wissensbasis-Integration (RAG)  
**Story Points**: 5  
**Priorität**: Mittel  
**Status**: Ready for Development  

## User Story
**Als** registrierter <PERSON>  
**möchte ich** erweiterte Funktionen zur Verwaltung meiner Dokumente haben  
**sodass** ich meine Wissensdatenbanken effizient organisieren und optimieren kann.

## Beschreibung
Diese Story erweitert die Dokumentenverwaltung um praktische Features wie Vorschau, Suche, Bulk-Operationen und Metadaten-Management, die die tägliche Arbeit mit Wissensdatenbanken erleichtern.

## Akzeptanzkriterien

### AC1: Dokumentvorschau und -anzeige
- [ ] Als Benutzer kann ich eine Vorschau von Dokumentinhalten sehen
- [ ] Ich kann den extrahierten Text eines Dokuments vollständig anzeigen
- [ ] PDF-Dokumente werden mit einer einfachen Viewer-Komponente angezeigt
- [ ] Markdown-Dokumente werden gerendert dargestellt
- [ ] Ich kann zwischen Roh-Text und formatierter Ansicht wechseln

### AC2: Erweiterte Suche und Filter
- [ ] Ich kann nach Dokumentnamen und Inhalten suchen
- [ ] Ich kann nach Dateityp, Upload-Datum und Status filtern
- [ ] Ich kann nach Schlagwörtern in Metadaten suchen
- [ ] Suchergebnisse werden mit Highlighting angezeigt
- [ ] Filter können kombiniert werden

### AC3: Metadaten-Management
- [ ] Ich kann Dokumenten Tags und Kategorien zuweisen
- [ ] Ich kann benutzerdefinierte Metadaten hinzufügen und bearbeiten
- [ ] Automatisch extrahierte Metadaten werden angezeigt
- [ ] Ich kann Metadaten für mehrere Dokumente gleichzeitig bearbeiten
- [ ] Metadaten werden in der Suche berücksichtigt

### AC4: Bulk-Operationen
- [ ] Ich kann mehrere Dokumente gleichzeitig auswählen
- [ ] Ich kann ausgewählte Dokumente löschen
- [ ] Ich kann Tags für mehrere Dokumente gleichzeitig setzen
- [ ] Ich kann Dokumente zwischen Wissensdatenbanken verschieben
- [ ] Bulk-Operationen zeigen Progress und Ergebnisse

### AC5: Dokumentstatistiken und Analytics
- [ ] Ich sehe Statistiken zu jeder Wissensdatenbank (Anzahl Dokumente, Gesamtgröße, etc.)
- [ ] Ich kann die Nutzung von Dokumenten in Agent-Antworten sehen
- [ ] Ich sehe, welche Dokumente am häufigsten verwendet werden
- [ ] Unused/Orphaned-Dokumente werden identifiziert
- [ ] Export-Funktionen für Statistiken

## Technische Anforderungen

### API-Endpoints
```typescript
// Dokumentvorschau
GET    /api/documents/[id]/preview              // Dokumentvorschau
GET    /api/documents/[id]/content              // Vollständiger Inhalt
GET    /api/documents/[id]/chunks               // Generierte Chunks

// Suche und Filter
GET    /api/knowledge-bases/[id]/search         // Dokumentsuche
GET    /api/documents/search                    // Globale Dokumentsuche

// Metadaten
PUT    /api/documents/[id]/metadata             // Metadaten aktualisieren
POST   /api/documents/bulk-metadata             // Bulk-Metadaten-Update

// Bulk-Operationen
POST   /api/documents/bulk-delete               // Mehrere Dokumente löschen
POST   /api/documents/bulk-move                 // Dokumente verschieben
POST   /api/documents/bulk-tag                  // Bulk-Tagging

// Analytics
GET    /api/knowledge-bases/[id]/analytics      // KB-Statistiken
GET    /api/documents/[id]/usage                // Dokument-Nutzung
GET    /api/documents/unused                    // Ungenutzte Dokumente
```

### Datenmodell-Erweiterungen
```typescript
// Erweiterte Metadaten
interface DocumentMetadata {
  title?: string
  author?: string
  created_date?: string
  modified_date?: string
  page_count?: number
  word_count?: number
  language?: string
  tags: string[]
  category?: string
  custom_fields: Record<string, any>
  description?: string
}

// Dokument-Usage-Tracking
interface DocumentUsage {
  document_id: string
  agent_id: string
  usage_count: number
  last_used: string
  average_relevance_score: number
}

// Such-Request
interface DocumentSearchRequest {
  query?: string
  file_types?: string[]
  date_range?: {
    from: string
    to: string
  }
  tags?: string[]
  status?: string[]
  sort_by?: 'name' | 'date' | 'size' | 'usage'
  sort_order?: 'asc' | 'desc'
  limit?: number
  offset?: number
}

// Bulk-Operation-Request
interface BulkOperationRequest {
  document_ids: string[]
  operation: 'delete' | 'move' | 'tag' | 'update_metadata'
  parameters: Record<string, any>
}
```

### Datenbankschema-Erweiterungen
```sql
-- Erweiterte Metadaten-Spalten
ALTER TABLE data_sources ADD COLUMN tags TEXT[] DEFAULT '{}';
ALTER TABLE data_sources ADD COLUMN category TEXT;
ALTER TABLE data_sources ADD COLUMN custom_metadata JSONB DEFAULT '{}';
ALTER TABLE data_sources ADD COLUMN description TEXT;

-- Dokument-Usage-Tracking
CREATE TABLE document_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID REFERENCES data_sources(id) ON DELETE CASCADE,
  agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
  usage_count INTEGER DEFAULT 0,
  last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  total_relevance_score DECIMAL DEFAULT 0,
  average_relevance_score DECIMAL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(document_id, agent_id)
);

-- Full-Text-Search für Dokumente
ALTER TABLE data_sources ADD COLUMN search_vector tsvector;

CREATE INDEX idx_data_sources_search ON data_sources USING gin(search_vector);
CREATE INDEX idx_data_sources_tags ON data_sources USING gin(tags);
CREATE INDEX idx_document_usage_agent ON document_usage(agent_id);
CREATE INDEX idx_document_usage_document ON document_usage(document_id);

-- Update-Trigger für Search-Vector
CREATE OR REPLACE FUNCTION update_data_source_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector := 
    setweight(to_tsvector('german', COALESCE(NEW.name, '')), 'A') ||
    setweight(to_tsvector('german', COALESCE(NEW.description, '')), 'B') ||
    setweight(to_tsvector('german', array_to_string(NEW.tags, ' ')), 'C');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_data_source_search_vector_trigger
  BEFORE INSERT OR UPDATE ON data_sources
  FOR EACH ROW EXECUTE FUNCTION update_data_source_search_vector();
```

## UI/UX Anforderungen

### Dokumentvorschau-Modal
- Zweispaltige Ansicht: Metadaten + Inhalt
- Tab-Navigation: Vorschau / Roh-Text / Chunks / Metadaten
- Syntax-Highlighting für Code-Dokumente
- Zoom-Funktion für PDF-Vorschau
- Print/Download-Optionen

### Erweiterte Such-UI
- Omnisearch-Bar mit Auto-Complete
- Filter-Panel mit Checkboxes und Schiebereglern
- Saved-Searches für häufige Abfragen
- Search-Results mit Snippet-Vorschau
- Tag-Cloud für populäre Tags

### Bulk-Operations-Interface
- Checkbox-Selection in Dokumentenliste
- Floating Action Bar bei Selektion
- Progress-Modal für lange Operationen
- Undo-Funktionalität für kritische Operationen
- Confirm-Dialoge mit Impact-Preview

### Analytics-Dashboard
- Donut-Charts für Dateityp-Verteilung
- Zeitreihen für Upload-Aktivität
- Heatmap für Dokument-Nutzung
- Top-Liste für meist-genutzte Dokumente
- Export-Buttons für CSV/PDF-Reports

## Services Implementation

### Document Service Erweiterungen
```typescript
export class DocumentService {
  async searchDocuments(
    knowledgeBaseId: string,
    searchRequest: DocumentSearchRequest
  ): Promise<SearchResult<DataSource>> {
    const query = this.buildSearchQuery(searchRequest)
    const results = await this.db.query(query)
    return {
      items: results.rows,
      total: results.count,
      page: Math.floor((searchRequest.offset || 0) / (searchRequest.limit || 20)) + 1
    }
  }

  async getDocumentPreview(documentId: string): Promise<DocumentPreview> {
    const document = await this.getDocument(documentId)
    const content = await this.extractPreviewContent(document)
    const chunks = await this.getDocumentChunks(documentId, 5) // First 5 chunks
    
    return {
      document,
      content: content.substring(0, 2000), // First 2000 chars
      chunks,
      metadata: document.metadata
    }
  }

  async bulkOperation(
    operation: BulkOperationRequest
  ): Promise<BulkOperationResult> {
    const results = await Promise.allSettled(
      operation.document_ids.map(id => 
        this.performSingleOperation(id, operation.operation, operation.parameters)
      )
    )

    return {
      successful: results.filter(r => r.status === 'fulfilled').length,
      failed: results.filter(r => r.status === 'rejected').length,
      errors: results
        .filter(r => r.status === 'rejected')
        .map(r => (r as PromiseRejectedResult).reason)
    }
  }
}
```

### Analytics Service
```typescript
export class DocumentAnalyticsService {
  async getKnowledgeBaseStats(kbId: string): Promise<KBAnalytics> {
    const stats = await this.db.query(`
      SELECT 
        COUNT(*) as total_documents,
        SUM(file_size) as total_size,
        AVG(file_size) as average_size,
        COUNT(DISTINCT file_type) as file_types,
        SUM(chunk_count) as total_chunks
      FROM data_sources 
      WHERE knowledge_base_id = $1 AND status = 'ready'
    `, [kbId])

    const usageStats = await this.getUsageStatistics(kbId)
    
    return {
      ...stats.rows[0],
      usage: usageStats,
      top_documents: await this.getTopDocuments(kbId),
      unused_documents: await this.getUnusedDocuments(kbId)
    }
  }

  async trackDocumentUsage(
    documentId: string,
    agentId: string,
    relevanceScore: number
  ): Promise<void> {
    await this.db.query(`
      INSERT INTO document_usage (document_id, agent_id, usage_count, total_relevance_score, average_relevance_score)
      VALUES ($1, $2, 1, $3, $3)
      ON CONFLICT (document_id, agent_id) 
      DO UPDATE SET 
        usage_count = document_usage.usage_count + 1,
        total_relevance_score = document_usage.total_relevance_score + $3,
        average_relevance_score = (document_usage.total_relevance_score + $3) / (document_usage.usage_count + 1),
        last_used = NOW(),
        updated_at = NOW()
    `, [documentId, agentId, relevanceScore])
  }
}
```

## Testkriterien

### Unit Tests
- [ ] Suche-Algorithmus mit verschiedenen Filtern
- [ ] Metadaten-Update-Logic
- [ ] Bulk-Operations-Verarbeitung
- [ ] Analytics-Berechnungen

### Integration Tests
- [ ] Full-Text-Search mit Highlighting
- [ ] Bulk-Operations mit großen Datenmengen
- [ ] Document-Preview für alle Formate
- [ ] Usage-Tracking-Integration

### UI Tests
- [ ] Dokumentvorschau-Modal
- [ ] Such- und Filter-Interface
- [ ] Bulk-Selection und -Operations
- [ ] Analytics-Dashboard-Komponenten

### Performance Tests
- [ ] Suche bei großen Dokumentenmengen
- [ ] Bulk-Operations-Performance
- [ ] Preview-Loading-Zeiten
- [ ] Analytics-Query-Performance

## Definition of Done
- [ ] Alle Akzeptanzkriterien erfüllt
- [ ] Dokumentvorschau für alle unterstützten Formate
- [ ] Erweiterte Suche mit Multi-Filter
- [ ] Metadaten-Management-Interface
- [ ] Bulk-Operations mit Progress-Tracking
- [ ] Analytics-Dashboard mit Statistiken
- [ ] Usage-Tracking für RAG-Integration
- [ ] Database-Schema mit optimierten Indexes
- [ ] Unit-, Integration- und Performance-Tests
- [ ] UI/UX für alle neuen Features
- [ ] Code Review und Documentation

## Abhängigkeiten
- **Story 5.1**: Wissensdatenbank-Verwaltung (abgeschlossen)
- **Story 5.2**: Dokumenten-Upload und -verarbeitung (abgeschlossen)
- **Story 5.3**: RAG-Integration für Usage-Tracking
- **Full-Text-Search**: PostgreSQL Text-Search-Features

## Risiken
- **Performance**: Suche bei sehr großen Dokumentenmengen
- **UI-Komplexität**: Viele Features in übersichtlicher UI
- **Data-Consistency**: Bei Bulk-Operations
- **Storage-Space**: Analytics-Daten können groß werden

## Nächste Schritte nach Completion
Nach Abschluss von Story 5.4 ist Epic 5 (RAG-Integration) vollständig implementiert. Als nächstes kann mit Epic 6 (Telefonie-Integration) begonnen werden für die vollständige Plattform-Funktionalität.