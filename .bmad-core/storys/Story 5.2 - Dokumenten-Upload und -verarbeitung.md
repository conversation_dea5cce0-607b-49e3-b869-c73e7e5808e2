# Story 5.2: Dokumenten-Upload und -verarbeitung

## Story-Information
**Epic**: Epic 5 - Wissensbasis-Integration (RAG)  
**Story Points**: 13  
**Priorität**: Hoch  
**Status**: Ready for Development  

## User Story
**Als** registrierter <PERSON>  
**möchte ich** verschiedene Dokumentformate in meine Wissensdatenbank hochladen können  
**sodass** meine KI-Agenten auf diese Informationen zugreifen können.

## Beschreibung
Diese Story implementiert das Hochladen, Verarbeiten und Indizieren von Dokumenten in verschiedenen Formaten. Die Dokumente werden automatisch in suchbare Chunks aufgeteilt und mit Vector-Embeddings versehen.

## Akzeptanzkriterien

### AC1: Multi-Format-Upload
- [ ] Als Benutzer kann ich PDF, DOCX, TXT und MD Dateien hochladen
- [ ] Ich kann mehrere Dateien gleichzeitig auswählen
- [ ] Ich sehe eine Vorschau der ausgewählten Dateien vor dem Upload
- [ ] Dateien bis 10MB werden unterstützt
- [ ] Ungültige Dateiformate werden abgelehnt mit klarer Fehlermeldung

### AC2: Upload-Progress und Status
- [ ] Ich sehe den Upload-Progress für jede Datei in Echtzeit
- [ ] Der Verarbeitungsstatus wird live aktualisiert (uploading → processing → ready)
- [ ] Bei Fehlern wird eine detaillierte Fehlermeldung angezeigt
- [ ] Ich kann den Upload-Prozess abbrechen

### AC3: Automatische Textextraktion
- [ ] PDF-Inhalte werden korrekt extrahiert (Text und Struktur)
- [ ] DOCX-Dokumente werden vollständig verarbeitet
- [ ] Markdown-Formatting wird beibehalten
- [ ] Metadaten (Titel, Autor, Erstelldatum) werden extrahiert

### AC4: Intelligentes Chunking
- [ ] Dokumente werden in sinnvolle Chunks aufgeteilt (max. 1000 Tokens)
- [ ] Chunks überlappen sich leicht für besseren Kontext
- [ ] Absätze und Abschnitte werden respektiert
- [ ] Code-Blöcke und Listen bleiben zusammen

### AC5: Vector-Embedding-Generation
- [ ] Für jeden Chunk werden Vector-Embeddings generiert
- [ ] Embeddings werden in der Datenbank gespeichert
- [ ] Fehler bei der Embedding-Generation werden behandelt
- [ ] Retry-Mechanismus für fehlgeschlagene Embeddings

### AC6: Dokumentenverwaltung
- [ ] Ich kann alle Dokumente einer Wissensdatenbank einsehen
- [ ] Ich sehe den Verarbeitungsstatus jedes Dokuments
- [ ] Ich kann Dokumente löschen (mit Bestätigung)
- [ ] Ich kann Dokument-Metadaten bearbeiten

## Technische Anforderungen

### API-Endpoints
```typescript
POST   /api/knowledge-bases/[id]/documents/upload   // Dokument hochladen
GET    /api/knowledge-bases/[id]/documents          // Dokumente auflisten
GET    /api/documents/[id]                          // Dokument-Details
DELETE /api/documents/[id]                          // Dokument löschen
GET    /api/documents/[id]/status                   // Verarbeitungsstatus
POST   /api/documents/[id]/reprocess                // Neu verarbeiten
```

### Datenmodell
```typescript
interface DataSource {
  id: string
  knowledge_base_id: string
  created_at: string
  updated_at: string
  name: string
  original_filename: string
  file_type: 'pdf' | 'docx' | 'txt' | 'md'
  file_size: number
  file_url: string
  status: 'uploading' | 'processing' | 'ready' | 'error'
  error_message?: string
  metadata: {
    title?: string
    author?: string
    created_date?: string
    page_count?: number
    word_count?: number
  }
  chunk_count?: number
}

interface DocumentChunk {
  id: string
  data_source_id: string
  content: string
  embedding: number[]
  metadata: {
    chunk_index: number
    page?: number
    section?: string
    token_count: number
  }
  created_at: string
}
```

### Datenbankschema
```sql
CREATE TABLE data_sources (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  knowledge_base_id UUID REFERENCES knowledge_bases(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  name TEXT NOT NULL,
  original_filename TEXT NOT NULL,
  file_type TEXT NOT NULL CHECK (file_type IN ('pdf', 'docx', 'txt', 'md')),
  file_size INTEGER NOT NULL,
  file_url TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'uploading' CHECK (status IN ('uploading', 'processing', 'ready', 'error')),
  error_message TEXT,
  metadata JSONB DEFAULT '{}',
  chunk_count INTEGER DEFAULT 0
);

CREATE TABLE document_chunks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  data_source_id UUID REFERENCES data_sources(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  embedding vector(1536),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes für bessere Performance
CREATE INDEX idx_data_sources_knowledge_base ON data_sources(knowledge_base_id);
CREATE INDEX idx_document_chunks_data_source ON document_chunks(data_source_id);
CREATE INDEX idx_document_chunks_embedding ON document_chunks USING ivfflat (embedding vector_cosine_ops);
```

### File Processing Services
```typescript
interface DocumentProcessor {
  extractText(file: File): Promise<ExtractedContent>
  generateChunks(content: string): Promise<DocumentChunk[]>
  generateEmbeddings(chunks: string[]): Promise<number[][]>
}

interface ExtractedContent {
  text: string
  metadata: DocumentMetadata
  structure?: DocumentStructure
}
```

## UI/UX Anforderungen

### Upload-Interface
- Drag-and-Drop-Bereich für Dateien
- Datei-Browser-Auswahl als Alternative
- Batch-Upload für mehrere Dateien
- Progress-Bars für jeden Upload
- Cancel-Buttons für laufende Uploads

### Dokumentenliste
- Tabelle mit Dateiname, Typ, Größe, Status, Datum
- Filteroptionen nach Status und Dateityp
- Sortierung nach verschiedenen Kriterien
- Bulk-Aktionen für mehrere Dokumente

### Status-Anzeigen
- Loading-Spinner für Verarbeitung
- Progress-Indicators für verschiedene Phasen
- Success/Error-Icons mit Tooltips
- Retry-Buttons bei Fehlern

## File Processing Pipeline

### 1. Upload-Phase
```typescript
const uploadPipeline = async (file: File, knowledgeBaseId: string) => {
  // 1. Validierung
  validateFileType(file)
  validateFileSize(file)
  
  // 2. Upload zu Supabase Storage
  const fileUrl = await uploadToStorage(file)
  
  // 3. Database-Eintrag erstellen
  const dataSource = await createDataSource({
    knowledgeBaseId,
    filename: file.name,
    fileType: getFileType(file),
    fileSize: file.size,
    fileUrl,
    status: 'uploading'
  })
  
  return dataSource
}
```

### 2. Processing-Phase
```typescript
const processDocument = async (dataSourceId: string) => {
  // 1. Status auf 'processing' setzen
  await updateDataSourceStatus(dataSourceId, 'processing')
  
  // 2. Text extrahieren
  const extractedContent = await extractTextFromFile(fileUrl, fileType)
  
  // 3. Chunks generieren
  const chunks = await generateChunks(extractedContent.text)
  
  // 4. Embeddings erstellen
  const embeddings = await generateEmbeddings(chunks.map(c => c.content))
  
  // 5. Chunks in DB speichern
  await saveChunksToDatabase(dataSourceId, chunks, embeddings)
  
  // 6. Status auf 'ready' setzen
  await updateDataSourceStatus(dataSourceId, 'ready')
}
```

## Testkriterien

### Unit Tests
- [ ] File-Validation-Logic
- [ ] Text-Extraction für alle Formate
- [ ] Chunking-Algorithmus
- [ ] Embedding-Generation
- [ ] Error-Handling für verschiedene Szenarien

### Integration Tests
- [ ] End-to-End Upload-Flow
- [ ] File Storage Integration
- [ ] Database-Operationen
- [ ] Processing-Pipeline

### UI Tests
- [ ] Drag-and-Drop-Funktionalität
- [ ] Progress-Anzeigen
- [ ] Error-Handling in UI
- [ ] File-List-Management

### Performance Tests
- [ ] Upload großer Dateien (bis 10MB)
- [ ] Batch-Upload mehrerer Dateien
- [ ] Processing-Zeit verschiedener Formate
- [ ] Memory-Usage bei großen Dokumenten

## Error-Handling

### Upload-Fehler
- Datei zu groß → Klare Größenbegrenzung kommunizieren
- Ungültiges Format → Unterstützte Formate auflisten
- Network-Fehler → Retry-Option anbieten
- Storage-Fehler → IT-Support kontaktieren

### Processing-Fehler
- Text-Extraction fehlgeschlagen → Datei möglicherweise beschädigt
- Embedding-API-Fehler → Automatischer Retry mit Backoff
- Out-of-Memory → Dokument ist zu groß für Verarbeitung

## Definition of Done
- [ ] Alle Akzeptanzkriterien erfüllt
- [ ] Upload für PDF, DOCX, TXT, MD funktioniert
- [ ] Processing-Pipeline vollständig implementiert
- [ ] Vector-Embeddings werden korrekt generiert
- [ ] Error-Handling und Retry-Mechanismen
- [ ] Progress-Tracking und Status-Updates
- [ ] Database-Schema mit RLS-Policies
- [ ] Unit-, Integration- und Performance-Tests
- [ ] UI/UX für Upload und Dokumentenverwaltung
- [ ] Code Review und Documentation

## Abhängigkeiten
- **Story 5.1**: Wissensdatenbank-Verwaltung (abgeschlossen)
- **Vector Database**: pgvector Extension in Supabase
- **File Storage**: Supabase Storage konfiguriert
- **Embedding API**: OpenAI oder alternative API

## Risiken
- **Embedding-Kosten**: Hohe API-Kosten bei vielen Dokumenten
- **Processing-Zeit**: Große Dokumente dauern lange
- **Memory-Usage**: Speicherverbrauch bei großen Dateien
- **API-Limits**: Rate-Limiting bei Embedding-APIs

## Nächste Schritte nach Completion
- **Story 5.3**: RAG-Integration für Agenten
- **Story 5.4**: Erweiterte Dokumentenverwaltung