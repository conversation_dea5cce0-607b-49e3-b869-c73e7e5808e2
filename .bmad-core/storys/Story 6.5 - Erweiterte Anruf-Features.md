# Story 6.5: Erweiterte Anruf-Features

## Story-Information
**Epic**: Epic 6 - Telefonie-Integration  
**Story Points**: 3  
**Priorität**: Niedrig  
**Status**: Ready for Development  

## User Story
**Als** registrierter <PERSON>utzer  
**möchte ich** erweiterte Anruf-Features wie Weiterleitung, Voicemail und Geschäftszeiten nutzen können  
**sodass** ich professionelle Telefonie-Lösungen für verschiedene Geschäftsszenarien anbieten kann.

## Beschreibung
Diese Story implementiert erweiterte Telefonie-Features, die über die Basic-Agent-Gespräche hinausgehen und professionelle Business-Funktionen bereitstellen.

## Akzeptanzkriterien

### AC1: Geschäftszeiten-Management
- [ ] Als Benutzer kann ich Geschäftszeiten pro Telefonnummer konfigurieren
- [ ] Unterschiedliche Zeiten für verschiedene Wochentage sind möglich
- [ ] Feiertage können als Ausnahmen definiert werden
- [ ] Timezone-Support für internationale Nummern
- [ ] Anrufe außerhalb der Geschäftszeiten werden entsprechend behandelt

### AC2: Intelligente Anrufweiterleitung
- [ ] Anrufe können an andere Nummern weitergeleitet werden
- [ ] Bedingte Weiterleitung basierend auf Status (besetzt, keine Antwort, Fehler)
- [ ] Round-Robin-Weiterleitung zwischen mehreren Nummern
- [ ] Eskalationsketten bei nicht beantworteten Anrufen
- [ ] Weiterleitung kann zeitbasiert konfiguriert werden

### AC3: Voicemail-System
- [ ] Voicemail kann pro Telefonnummer aktiviert/deaktiviert werden
- [ ] Individuelle Voicemail-Begrüßungen können hochgeladen werden
- [ ] Voicemail-Nachrichten werden automatisch transkribiert
- [ ] E-Mail-Benachrichtigungen bei neuen Voicemails
- [ ] Voicemail-Verwaltung über die UI (anhören, löschen, weiterleiten)

### AC4: Anruffilterung und Blacklisting
- [ ] Bestimmte Nummern können blockiert werden (Blacklist)
- [ ] Whitelist für nur erlaubte Nummern
- [ ] Spam-Erkennung und automatische Filterung
- [ ] Anrufer-ID-Lookup für zusätzliche Informationen
- [ ] Custom-Greeting für verschiedene Anrufer-Kategorien

### AC5: Multi-Agent-Routing
- [ ] Anrufe können zwischen mehreren Agenten verteilt werden
- [ ] Load-Balancing basierend auf Agent-Verfügbarkeit
- [ ] Skill-basiertes Routing (Sprache, Fachgebiet)
- [ ] Fallback-Agenten bei nicht verfügbaren Primary-Agenten
- [ ] Queue-System für hohe Anrufvolumen

## Technische Anforderungen

### API-Endpoints
```typescript
// Geschäftszeiten
PUT    /api/phone-numbers/[id]/business-hours     // Geschäftszeiten konfigurieren
GET    /api/phone-numbers/[id]/business-hours     // Aktuelle Konfiguration
POST   /api/phone-numbers/[id]/holidays           // Feiertage hinzufügen
DELETE /api/phone-numbers/[id]/holidays/[date]    // Feiertag entfernen

// Anrufweiterleitung
PUT    /api/phone-numbers/[id]/forwarding         // Weiterleitungsregeln
GET    /api/phone-numbers/[id]/forwarding         // Aktuelle Regeln
POST   /api/phone-numbers/[id]/forwarding/test    // Weiterleitung testen

// Voicemail
PUT    /api/phone-numbers/[id]/voicemail          // Voicemail-Konfiguration
POST   /api/phone-numbers/[id]/voicemail/greeting // Begrüßung hochladen
GET    /api/phone-numbers/[id]/voicemails         // Voicemail-Liste
GET    /api/voicemails/[id]                       // Einzelne Voicemail
DELETE /api/voicemails/[id]                       // Voicemail löschen

// Anruffilterung
GET    /api/phone-numbers/[id]/blacklist          // Blacklist verwalten
POST   /api/phone-numbers/[id]/blacklist          // Nummer blockieren
DELETE /api/phone-numbers/[id]/blacklist/[number] // Blockierung aufheben
GET    /api/phone-numbers/[id]/whitelist          // Whitelist verwalten

// Multi-Agent-Routing
PUT    /api/phone-numbers/[id]/routing            // Routing-Konfiguration
GET    /api/phone-numbers/[id]/routing/status     // Agent-Verfügbarkeit
POST   /api/phone-numbers/[id]/routing/test       // Routing testen
```

### Datenmodell-Erweiterungen
```typescript
interface BusinessHoursConfig {
  timezone: string
  schedule: WeeklySchedule
  holidays: Holiday[]
  after_hours_action: 'voicemail' | 'forward' | 'reject' | 'custom_message'
  after_hours_message?: string
  holiday_action: 'voicemail' | 'forward' | 'reject' | 'custom_message'
  holiday_message?: string
}

interface WeeklySchedule {
  monday?: DaySchedule
  tuesday?: DaySchedule
  wednesday?: DaySchedule
  thursday?: DaySchedule
  friday?: DaySchedule
  saturday?: DaySchedule
  sunday?: DaySchedule
}

interface DaySchedule {
  enabled: boolean
  periods: TimePeriod[]
  break_periods?: TimePeriod[]
}

interface TimePeriod {
  start: string  // HH:mm
  end: string    // HH:mm
}

interface Holiday {
  date: string
  name: string
  recurring: boolean
}

interface ForwardingRule {
  id: string
  condition: 'no_answer' | 'busy' | 'failed' | 'after_hours' | 'all'
  action: 'forward' | 'voicemail' | 'hangup'
  target_type: 'phone_number' | 'agent' | 'external'
  target_value: string
  timeout_seconds?: number
  priority: number
  enabled: boolean
}

interface VoicemailConfig {
  enabled: boolean
  greeting_type: 'default' | 'custom' | 'text_to_speech'
  greeting_text?: string
  greeting_audio_url?: string
  transcription_enabled: boolean
  email_notifications: boolean
  max_message_duration: number
  auto_delete_after_days?: number
}

interface VoicemailMessage {
  id: string
  phone_number_id: string
  caller_number: string
  caller_name?: string
  created_at: string
  duration_seconds: number
  audio_url: string
  transcription?: string
  transcription_confidence?: number
  status: 'new' | 'played' | 'saved' | 'deleted'
  metadata: Record<string, any>
}

interface CallFilter {
  id: string
  phone_number_id: string
  type: 'blacklist' | 'whitelist'
  pattern: string
  pattern_type: 'exact' | 'prefix' | 'regex'
  action: 'reject' | 'voicemail' | 'custom_message'
  reason?: string
  enabled: boolean
  created_at: string
}

interface MultiAgentRouting {
  phone_number_id: string
  enabled: boolean
  strategy: 'round_robin' | 'least_busy' | 'skill_based' | 'priority'
  agents: RoutingAgent[]
  fallback_action: 'voicemail' | 'forward' | 'queue'
  max_queue_time?: number
  queue_music_url?: string
}

interface RoutingAgent {
  agent_id: string
  priority: number
  skills: string[]
  availability_schedule?: WeeklySchedule
  max_concurrent_calls: number
  enabled: boolean
}
```

### Datenbankschema-Erweiterungen
```sql
-- Erweiterte Phone Number Configuration
ALTER TABLE phone_numbers ADD COLUMN business_hours_config JSONB DEFAULT '{}';
ALTER TABLE phone_numbers ADD COLUMN forwarding_rules JSONB DEFAULT '[]';
ALTER TABLE phone_numbers ADD COLUMN voicemail_config JSONB DEFAULT '{}';
ALTER TABLE phone_numbers ADD COLUMN call_filters JSONB DEFAULT '[]';
ALTER TABLE phone_numbers ADD COLUMN routing_config JSONB DEFAULT '{}';

-- Voicemail Messages
CREATE TABLE voicemail_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number_id UUID REFERENCES phone_numbers(id) ON DELETE CASCADE,
  caller_number TEXT NOT NULL,
  caller_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  duration_seconds INTEGER NOT NULL,
  audio_url TEXT NOT NULL,
  transcription TEXT,
  transcription_confidence DECIMAL,
  status TEXT NOT NULL DEFAULT 'new' CHECK (
    status IN ('new', 'played', 'saved', 'deleted')
  ),
  metadata JSONB DEFAULT '{}',
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Call Routing Logs
CREATE TABLE call_routing_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  call_log_id UUID REFERENCES call_logs(id) ON DELETE CASCADE,
  phone_number_id UUID REFERENCES phone_numbers(id) ON DELETE CASCADE,
  routing_decision TEXT NOT NULL,
  selected_agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
  routing_duration_ms INTEGER,
  fallback_used BOOLEAN DEFAULT false,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_voicemail_messages_phone_number ON voicemail_messages(phone_number_id);
CREATE INDEX idx_voicemail_messages_status ON voicemail_messages(status);
CREATE INDEX idx_voicemail_messages_created ON voicemail_messages(created_at);
CREATE INDEX idx_call_routing_logs_call ON call_routing_logs(call_log_id);
CREATE INDEX idx_call_routing_logs_phone_number ON call_routing_logs(phone_number_id);
```

## Feature Implementation

### Business Hours Handler
```typescript
export class BusinessHoursService {
  isWithinBusinessHours(
    config: BusinessHoursConfig,
    timestamp: Date = new Date()
  ): boolean {
    const timezone = config.timezone || 'UTC'
    const localTime = this.convertToTimezone(timestamp, timezone)
    
    // Check for holidays
    if (this.isHoliday(localTime, config.holidays)) {
      return false
    }

    const dayOfWeek = this.getDayOfWeek(localTime)
    const daySchedule = config.schedule[dayOfWeek]
    
    if (!daySchedule || !daySchedule.enabled) {
      return false
    }

    const currentTime = this.getTimeString(localTime)
    
    // Check if within any business period
    for (const period of daySchedule.periods) {
      if (this.isTimeWithinPeriod(currentTime, period)) {
        // Check if not in break period
        const inBreak = daySchedule.break_periods?.some(breakPeriod =>
          this.isTimeWithinPeriod(currentTime, breakPeriod)
        )
        
        if (!inBreak) {
          return true
        }
      }
    }

    return false
  }

  handleAfterHoursCall(
    phoneNumber: PhoneNumber,
    callLog: CallLog
  ): TwiMLResponse {
    const config = phoneNumber.business_hours_config
    const twiml = new VoiceResponse()

    switch (config.after_hours_action) {
      case 'voicemail':
        return this.redirectToVoicemail(phoneNumber, twiml)
      
      case 'forward':
        const forwardingRule = this.getAfterHoursForwardingRule(phoneNumber)
        return this.handleForwarding(forwardingRule, twiml)
      
      case 'custom_message':
        twiml.say(config.after_hours_message || 'We are currently closed.')
        twiml.hangup()
        return twiml
      
      case 'reject':
      default:
        twiml.reject()
        return twiml
    }
  }
}
```

### Call Forwarding System
```typescript
export class CallForwardingService {
  async processForwardingRules(
    phoneNumber: PhoneNumber,
    callContext: CallContext
  ): Promise<ForwardingDecision> {
    const rules = phoneNumber.forwarding_rules
      .filter(rule => rule.enabled)
      .sort((a, b) => a.priority - b.priority)

    for (const rule of rules) {
      if (this.evaluateForwardingCondition(rule, callContext)) {
        return this.executeForwardingAction(rule, callContext)
      }
    }

    // No matching rule, proceed normally
    return { action: 'proceed', target: null }
  }

  private evaluateForwardingCondition(
    rule: ForwardingRule,
    context: CallContext
  ): boolean {
    switch (rule.condition) {
      case 'after_hours':
        return !this.businessHoursService.isWithinBusinessHours(
          context.phoneNumber.business_hours_config
        )
      
      case 'busy':
        return this.isAgentBusy(context.assignedAgent)
      
      case 'no_answer':
        return context.ringDuration > (rule.timeout_seconds || 30) * 1000
      
      case 'failed':
        return context.hasSystemError
      
      case 'all':
        return true
      
      default:
        return false
    }
  }

  private async executeForwardingAction(
    rule: ForwardingRule,
    context: CallContext
  ): Promise<ForwardingDecision> {
    switch (rule.action) {
      case 'forward':
        return this.setupCallForwarding(rule, context)
      
      case 'voicemail':
        return { action: 'voicemail', target: null }
      
      case 'hangup':
        return { action: 'hangup', target: null }
      
      default:
        return { action: 'proceed', target: null }
    }
  }
}
```

### Voicemail System
```typescript
export class VoicemailService {
  createVoicemailTwiML(
    phoneNumber: PhoneNumber,
    callLog: CallLog
  ): TwiMLResponse {
    const config = phoneNumber.voicemail_config
    const twiml = new VoiceResponse()

    // Play greeting
    if (config.greeting_type === 'custom' && config.greeting_audio_url) {
      twiml.play(config.greeting_audio_url)
    } else if (config.greeting_type === 'text_to_speech' && config.greeting_text) {
      twiml.say({
        voice: 'alice',
        language: phoneNumber.language || 'en-US'
      }, config.greeting_text)
    } else {
      twiml.say('Please leave a message after the beep.')
    }

    // Record message
    twiml.record({
      action: `/api/webhooks/twilio/voicemail-complete/${callLog.id}`,
      method: 'POST',
      maxLength: config.max_message_duration || 300,
      transcribe: config.transcription_enabled,
      transcribeCallback: `/api/webhooks/twilio/voicemail-transcription/${callLog.id}`,
      playBeep: true
    })

    twiml.say('Thank you for your message.')
    twiml.hangup()

    return twiml
  }

  async handleVoicemailComplete(
    callLogId: string,
    recordingData: VoicemailRecordingData
  ): Promise<void> {
    const callLog = await this.callService.getCallLog(callLogId)
    
    // Create voicemail message
    const voicemail = await this.db.query(`
      INSERT INTO voicemail_messages (
        phone_number_id, caller_number, caller_name, duration_seconds,
        audio_url, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [
      callLog.phone_number_id,
      callLog.caller_number,
      callLog.caller_name,
      recordingData.RecordingDuration,
      recordingData.RecordingUrl,
      JSON.stringify({
        call_log_id: callLogId,
        recording_sid: recordingData.RecordingSid
      })
    ])

    // Send email notification if enabled
    const phoneNumber = await this.phoneService.getPhoneNumber(callLog.phone_number_id)
    if (phoneNumber.voicemail_config.email_notifications) {
      await this.sendVoicemailNotification(phoneNumber.user_id, voicemail.rows[0])
    }

    // Update call log
    await this.callService.updateCallLog(callLogId, {
      end_reason: 'voicemail',
      status: 'completed'
    })
  }
}
```

### Multi-Agent Routing
```typescript
export class MultiAgentRoutingService {
  async selectAgent(
    phoneNumber: PhoneNumber,
    callContext: CallContext
  ): Promise<Agent | null> {
    const routingConfig = phoneNumber.routing_config
    
    if (!routingConfig.enabled || !routingConfig.agents.length) {
      return null
    }

    const availableAgents = await this.getAvailableAgents(
      routingConfig.agents,
      callContext.timestamp
    )

    if (!availableAgents.length) {
      return this.handleNoAvailableAgents(routingConfig, callContext)
    }

    switch (routingConfig.strategy) {
      case 'round_robin':
        return this.selectRoundRobin(availableAgents)
      
      case 'least_busy':
        return this.selectLeastBusy(availableAgents)
      
      case 'skill_based':
        return this.selectBySkills(availableAgents, callContext)
      
      case 'priority':
        return this.selectByPriority(availableAgents)
      
      default:
        return availableAgents[0]
    }
  }

  private async getAvailableAgents(
    routingAgents: RoutingAgent[],
    timestamp: Date
  ): Promise<Agent[]> {
    const available = []

    for (const routingAgent of routingAgents.filter(a => a.enabled)) {
      // Check availability schedule
      if (routingAgent.availability_schedule) {
        const isAvailable = this.businessHoursService.isWithinBusinessHours(
          { schedule: routingAgent.availability_schedule, timezone: 'UTC', holidays: [] },
          timestamp
        )
        if (!isAvailable) continue
      }

      // Check concurrent call limit
      const currentCalls = await this.getActiveCallsCount(routingAgent.agent_id)
      if (currentCalls >= routingAgent.max_concurrent_calls) {
        continue
      }

      const agent = await this.agentService.getAgent(routingAgent.agent_id)
      if (agent && agent.status === 'active') {
        available.push(agent)
      }
    }

    return available
  }
}
```

## UI/UX Anforderungen

### Business Hours Configuration
- Visual Weekly Calendar mit Time-Picker
- Timezone-Selector mit Auto-Detection
- Holiday-Management mit Date-Picker
- Preview der aktuellen Konfiguration
- Import/Export für Standard-Templates

### Forwarding Rules Builder
- Drag-and-Drop Rule-Builder
- Condition-Testing mit Mock-Scenarios
- Visual Flow-Chart der Routing-Logic
- Priority-Management mit Up/Down-Buttons
- Rule-Templates für häufige Szenarien

### Voicemail Management
- Audio-Player für Greeting-Preview
- Voicemail-Inbox mit Filtering
- Transcription-Viewer mit Edit-Capability
- Bulk-Actions für Message-Management
- Email-Template-Configuration

### Call Filtering Interface
- Number-Input mit Validation
- Pattern-Builder für Complex-Rules
- Import/Export für Large-Lists
- Test-Function für Rule-Validation
- Statistics zu blockierten Anrufen

## Testkriterien

### Unit Tests
- [ ] Business-Hours-Calculation-Logic
- [ ] Forwarding-Rule-Evaluation
- [ ] Agent-Selection-Algorithms
- [ ] Voicemail-Processing-Flow
- [ ] Call-Filter-Pattern-Matching

### Integration Tests
- [ ] End-to-End-Forwarding-Flow
- [ ] Voicemail-Recording-and-Transcription
- [ ] Multi-Agent-Routing-Scenarios
- [ ] Business-Hours-Based-Routing
- [ ] Call-Filter-Blocking

### Telephony Tests
- [ ] Actual-Call-Tests für alle Features
- [ ] Forwarding-Chain-Tests
- [ ] Voicemail-Quality-Tests
- [ ] Agent-Routing-Validation
- [ ] After-Hours-Call-Handling

## Definition of Done
- [ ] Alle Akzeptanzkriterien erfüllt
- [ ] Business-Hours-Management mit Timezone-Support
- [ ] Intelligent Call-Forwarding-System
- [ ] Complete Voicemail-System mit Transcription
- [ ] Call-Filtering und Blacklist-Management
- [ ] Multi-Agent-Routing mit verschiedenen Strategien
- [ ] Database-Schema-Extensions
- [ ] Provider-Integration für alle Features
- [ ] Unit-, Integration- und Telephony-Tests
- [ ] UI/UX für alle Configuration-Interfaces
- [ ] Performance-Optimization für Real-time-Decisions
- [ ] Documentation für alle Features

## Abhängigkeiten
- **Story 6.2**: Telefonnummer-Verwaltung (für Configuration-Base)
- **Story 6.3**: Anruf-Handling (für Integration-Points)
- **Epic 2**: Agent-Management (für Multi-Agent-Routing)
- **Audio-Processing**: Für Voicemail-Features
- **Email-Service**: Für Voicemail-Notifications

## Risiken
- **Complexity**: Viele Features erhöhen System-Complexity
- **Performance**: Real-time-Decisions müssen schnell sein
- **Testing-Effort**: Telephony-Features sind schwer zu testen
- **User-Experience**: Viele Konfigurationsoptionen können überfordern

## Nächste Schritte nach Completion
Mit Story 6.5 ist Epic 6 (Telefonie-Integration) vollständig implementiert. Die KI-Sprachassistenten-Plattform bietet nun alle geplanten Features:

- ✅ Epic 1: Foundation & User Onboarding
- ✅ Epic 2: Agent Management  
- ✅ Epic 3: Monitoring & Analytics
- ✅ Epic 4: Interactive Testing
- ✅ Epic 5: Knowledge Base (RAG)
- ✅ Epic 6: Telephony Integration

Die Plattform ist bereit für Production-Deployment und Benutzer-Onboarding.