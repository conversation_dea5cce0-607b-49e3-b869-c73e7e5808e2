# Story 3: Gesprächshistorie und -logs anzeigen

## Story-Information

**Epic**: Epic 3 - Agent-Monitoring und Analytics  
**Story ID**: E3-S3  
**Titel**: Gesprächshistorie und -logs anzeigen  
**Priorität**: Mittel  
**Aufwand**: 8 Story Points  
**Status**: Bereit zur Implementierung  
**Assignee**: TBD  
**Sprint**: TBD  

## User Story

**Als** Administrator oder Manager  
**möchte ich** eine vollständige Historie aller Gespräche mit Such- und Filterfunktionen  
**damit ich** vergangene Gespräche analysieren und Probleme nachvollziehen kann.

## Geschäftswert

- **Compliance**: Vollständige Audit-Fähigkeiten für Regulierungsanforderungen
- **Qualitätsanalyse**: Identifikation von Verbesserungspotenzialen
- **Problemlösung**: Nachvollziehbarkeit von Kundeninteraktionen
- **Training**: Verwendung als Schulungsmaterial für neue Agenten

## Akzeptanzkriterien

### AC1: Filterable Gesprächsliste
**Priorität**: Muss  
**Aufwand**: 3 SP

- [ ] Tabellarische Liste aller Gespräche mit Pagination (50 pro Seite)
- [ ] Spalten:
  - Datum/Zeit (sortierbar)
  - Agent-Name (klickbar)
  - Anrufer-Info (falls verfügbar)
  - Gesprächsdauer
  - Status (erfolgreich/abgebrochen/fehler)
  - Bewertung (falls verfügbar)
- [ ] Filter-Optionen:
  - Zeitraum (heute, letzte Woche, letzter Monat, benutzerdefiniert)
  - Agent (Dropdown mit allen Agenten)
  - Status (alle, erfolgreich, abgebrochen, fehler)
  - Gesprächsdauer (< 1min, 1-5min, 5-15min, > 15min)
- [ ] Suchfunktion:
  - Nach Anrufer-Name/Telefonnummer
  - Nach Gesprächsinhalt (Volltext-Suche)
  - Nach Agent-Name
- [ ] Export-Funktion (CSV, PDF)

### AC2: Detailansicht einzelner Gespräche
**Priorität**: Muss  
**Aufwand**: 3 SP

- [ ] Modal oder separate Seite für Gesprächsdetails
- [ ] Gesprächs-Metadaten:
  - Start-/Endzeit
  - Gesamtdauer
  - Agent-Informationen
  - Anrufer-Informationen
  - Verwendete Tools
  - Gesprächsbewertung
- [ ] Vollständiges Transkript mit Zeitstempel
- [ ] System-Events chronologisch angezeigt
- [ ] Audio-Aufzeichnung (falls verfügbar):
  - Integrierter Audio-Player
  - Download-Option
- [ ] Notizen und Tags:
  - Agent-Notizen anzeigen
  - System-generierte Tags
  - Manuell hinzugefügte Tags

### AC3: Transkript-Anzeige mit Zeitstempel
**Priorität**: Muss  
**Aufwand**: 2 SP

- [ ] Chronologische Darstellung aller Nachrichten
- [ ] Unterscheidung zwischen Sprechern:
  - Agent: Rechts ausgerichtet, blaue Sprechblase
  - Anrufer: Links ausgerichtet, graue Sprechblase
  - System: Zentriert, gelbe Benachrichtigung
- [ ] Zeitstempel für jede Nachricht (HH:MM:SS)
- [ ] Suchfunktion innerhalb des Transkripts
- [ ] Hervorhebung von Suchbegriff
- [ ] Navigation zu bestimmten Zeitpunkten
- [ ] Sentiment-Indikatoren (falls verfügbar)
- [ ] Confidence-Score für Transkription (falls verfügbar)

## Technische Spezifikation

### Komponenten-Architektur

```typescript
// Hauptkomponenten
app/history/page.tsx
components/history/ConversationHistory.tsx
components/history/ConversationTable.tsx
components/history/ConversationFilters.tsx
components/history/ConversationDetails.tsx
components/history/TranscriptViewer.tsx
components/history/AudioPlayer.tsx

// Hooks
hooks/useConversationHistory.ts
hooks/useConversationDetails.ts
hooks/useConversationFilters.ts
hooks/useAudioPlayer.ts

// Services
services/historyService.ts
services/exportService.ts

// Types
types/history.ts
types/conversation.ts
```

### Datenstrukturen

```typescript
interface ConversationHistoryItem {
  id: string
  startTime: Date
  endTime: Date
  duration: number // in seconds
  agentId: string
  agentName: string
  callerId?: string
  callerInfo?: {
    name?: string
    phone?: string
    email?: string
    location?: string
  }
  status: 'completed' | 'abandoned' | 'failed' | 'transferred'
  rating?: number // 1-5
  summary?: string
  tags: string[]
  toolsUsed: string[]
  metadata: {
    department?: string
    priority: 'low' | 'medium' | 'high'
    category?: string
  }
}

interface ConversationDetails extends ConversationHistoryItem {
  transcript: TranscriptEntry[]
  systemEvents: SystemEvent[]
  audioRecording?: {
    url: string
    duration: number
    format: string
    size: number
  }
  agentNotes?: string
  qualityMetrics?: {
    averageResponseTime: number
    sentimentScore: number
    resolutionStatus: 'resolved' | 'unresolved' | 'escalated'
  }
}

interface ConversationFilters {
  dateRange: {
    start: Date
    end: Date
  }
  agentIds: string[]
  status: ('completed' | 'abandoned' | 'failed' | 'transferred')[]
  durationRange: {
    min: number // in seconds
    max: number // in seconds
  }
  search: string
  tags: string[]
  sortBy: 'startTime' | 'duration' | 'agentName' | 'rating'
  sortOrder: 'asc' | 'desc'
  page: number
  pageSize: number
}

interface SystemEvent {
  id: string
  timestamp: Date
  type: 'tool_used' | 'transfer' | 'hold' | 'resume' | 'note_added' | 'tag_added'
  description: string
  metadata?: {
    toolName?: string
    transferTarget?: string
    note?: string
    tag?: string
  }
}
```

### API-Endpoints

```typescript
// REST APIs
GET /api/history/conversations
// Query params: filters, pagination
// Response: { conversations: ConversationHistoryItem[], total: number }

GET /api/history/conversations/:id
// Response: ConversationDetails

GET /api/history/conversations/:id/transcript
// Response: TranscriptEntry[]

GET /api/history/conversations/:id/audio
// Response: Audio file stream

POST /api/history/export
// Body: { filters, format: 'csv' | 'pdf' }
// Response: File download

GET /api/history/agents
// Response: { id: string, name: string }[]

GET /api/history/tags
// Response: string[]
```

## Implementierungsplan

### Phase 1: Basis-Struktur (2 Tage)
- [ ] ConversationHistory Layout erstellen
- [ ] ConversationTable Komponente entwickeln
- [ ] Pagination implementieren
- [ ] Basis-Styling und Responsive Design

### Phase 2: Filter und Suche (2 Tage)
- [ ] ConversationFilters Komponente
- [ ] Filter-Logic implementieren
- [ ] Suchfunktionalität
- [ ] URL-State-Management für Filter

### Phase 3: Detailansicht (2 Tage)
- [ ] ConversationDetails Modal/Seite
- [ ] TranscriptViewer Komponente
- [ ] Metadaten-Anzeige
- [ ] Navigation zwischen Gesprächen

### Phase 4: Erweiterte Features (2 Tage)
- [ ] AudioPlayer Integration
- [ ] Export-Funktionalität
- [ ] Performance-Optimierungen
- [ ] Error Handling und Loading States

## Testplan

### Unit Tests (Ziel: >90% Coverage)
- [ ] ConversationTable
  - Rendering verschiedener Gesprächstypen
  - Sortierung und Pagination
  - Filter-Anwendung
- [ ] ConversationFilters
  - Filter-Logik
  - URL-State-Synchronisation
- [ ] TranscriptViewer
  - Transkript-Rendering
  - Zeitstempel-Navigation
  - Suchfunktion
- [ ] History-Hooks
  - useConversationHistory
  - useConversationFilters

### Integration Tests
- [ ] API-Integration
  - Gespräche laden mit Filtern
  - Gesprächsdetails abrufen
  - Export-Funktionalität
- [ ] Filter-Pipeline
  - Kombinierte Filter
  - Performance bei großen Datenmengen
- [ ] Audio-Integration
  - Audio-Player
  - Download-Funktionalität

### E2E Tests
- [ ] Vollständiger History-Workflow
  - Seite laden
  - Filter anwenden
  - Gespräch öffnen
- [ ] Such- und Filterfunktionen
  - Textsuche
  - Datumsfilter
  - Agent-Filter
- [ ] Export-Funktionalität
  - CSV-Export
  - PDF-Export

## Performance-Anforderungen

### Ladezeiten
- [ ] Initiales Laden der Historie: < 3 Sekunden
- [ ] Filter-Anwendung: < 1 Sekunde
- [ ] Gesprächsdetails laden: < 2 Sekunden
- [ ] Transkript-Rendering: < 1 Sekunde

### Skalierbarkeit
- [ ] Unterstützt bis zu 10.000 Gespräche pro Monat
- [ ] Effiziente Pagination (Virtual Scrolling bei Bedarf)
- [ ] Optimierte Datenbankabfragen
- [ ] Caching für häufig abgerufene Daten

## Definition of Done

### Funktional
- [ ] Alle Akzeptanzkriterien erfüllt
- [ ] Gesprächsliste lädt und filtert korrekt
- [ ] Detailansicht zeigt alle Informationen
- [ ] Such- und Filterfunktionen arbeiten zuverlässig
- [ ] Export-Funktionen generieren korrekte Dateien

### Technisch
- [ ] Code Review abgeschlossen
- [ ] Unit Tests mit >90% Coverage
- [ ] Integration Tests erfolgreich
- [ ] Performance-Tests bestanden
- [ ] Security-Review für Datenzugriff

### Qualität
- [ ] Deutsche Lokalisierung vollständig
- [ ] Accessibility-Tests erfolgreich
- [ ] Error Handling für alle Szenarien
- [ ] Loading States für alle Operationen

### Deployment
- [ ] Dokumentation aktualisiert
- [ ] Deployment in Staging erfolgreich
- [ ] Benutzer-Akzeptanztests erfolgreich
- [ ] Stakeholder-Abnahme erhalten

## Risiken und Mitigation

### Technische Risiken
| Risiko | Wahrscheinlichkeit | Impact | Mitigation |
|--------|-------------------|---------|------------|
| Performance bei großen Datenmengen | Hoch | Mittel | Pagination, Indexierung, Caching |
| Volltext-Suche Komplexität | Mittel | Niedrig | Einfache String-Suche als Fallback |
| Audio-Integration | Niedrig | Niedrig | Optionales Feature |

### Geschäftsrisiken
| Risiko | Wahrscheinlichkeit | Impact | Mitigation |
|--------|-------------------|---------|------------|
| Datenschutz-Compliance | Niedrig | Sehr Hoch | Rollenbasierte Zugriffskontrolle |
| Speicherplatz für Audio | Mittel | Mittel | Konfigurierbare Aufbewahrungsrichtlinien |

## Abhängigkeiten

### Interne Abhängigkeiten
- **Datenbank-Schema**: Erweiterte Tabellen für Gesprächshistorie
- **Audio-Storage**: File Storage für Aufzeichnungen
- **Export-Service**: Backend-Service für Datenexport

### Externe Abhängigkeiten
- **Supabase Storage**: Für Audio-Dateien
- **PDF-Generation**: Library für PDF-Export

---

**Erstellt**: 2025-01-29  
**Letzte Aktualisierung**: 2025-01-29  
**Version**: 1.0  
**Nächste Review**: Bei Sprint-Planning
