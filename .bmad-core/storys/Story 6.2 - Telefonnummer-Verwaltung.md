# Story 6.2: Telefonnummer-Verwaltung

## Story-Information
**Epic**: Epic 6 - Telefonie-Integration  
**Story Points**: 8  
**Priorität**: Hoch  
**Status**: Ready for Development  

## User Story
**Als** registrierter Benutzer  
**möchte ich** meine gekauften Telefonnummern verwalten und Agenten zuweisen können  
**sodass** ich meine Telefonie-Ressourcen effizient nutzen kann.

## Beschreibung
Diese Story implementiert die vollständige Verwaltung von gekauften Telefonnummern, einschließlich der Zuweisung zu Agenten, Konfiguration von Einstellungen und Überwachung der Nutzung.

## Akzeptanzkriterien

### AC1: Telefonnummer-Übersicht
- [ ] Als Benutzer kann ich alle meine Telefonnummern in einer Übersicht sehen
- [ ] Die Liste zeigt Nummer, Status, zugewiesenen Agent und monatliche Kosten
- [ ] Ich kann nach Status (aktiv, inaktiv, zugewiesen) filtern
- [ ] Sortierung nach Nummer, <PERSON><PERSON>, Kosten oder Agent ist möglich
- [ ] Search-Funktion für schnelles Auffinden bestimmter Nummern

### AC2: Telefonnummer-Details anzeigen
- [ ] Ich kann Details einer Telefonnummer in einer Detailansicht sehen
- [ ] Alle Metadaten (Land, Region, Capabilities, Kosten) werden angezeigt
- [ ] Aktuelle Agent-Zuweisung und Konfiguration ist ersichtlich
- [ ] Usage-Statistiken (Anrufe, Minuten, Kosten) werden dargestellt
- [ ] Konfigurationsoptionen sind zugänglich

### AC3: Agent-Zuweisung verwalten
- [ ] Ich kann eine Telefonnummer einem meiner Agenten zuweisen
- [ ] Ich kann die Zuweisung ändern oder entfernen
- [ ] Nur ein Agent kann pro Telefonnummer zugewiesen werden
- [ ] Ich erhalte Warnungen bei Änderungen bestehender Zuweisungen
- [ ] Unzugewiesene Nummern werden klar gekennzeichnet

### AC4: Telefonnummer-Konfiguration
- [ ] Ich kann einen benutzerdefinierten Namen (Friendly Name) vergeben
- [ ] Ich kann Weiterleitungsregeln konfigurieren
- [ ] Geschäftszeiten können pro Nummer eingestellt werden
- [ ] Voicemail-Einstellungen sind konfigurierbar
- [ ] Webhook-URLs für Events können gesetzt werden

### AC5: Nummer deaktivieren und kündigen
- [ ] Ich kann Telefonnummern temporär deaktivieren
- [ ] Ich kann Nummern dauerhaft kündigen (mit Bestätigung)
- [ ] Bei Kündigung werden alle verknüpften Daten aufgeräumt
- [ ] Kündigungsfristen und -bedingungen werden angezeigt
- [ ] Automatische Abrechnung stoppt nach Kündigung

## Technische Anforderungen

### API-Endpoints
```typescript
// Telefonnummer-Verwaltung
GET    /api/phone-numbers                    // Alle Telefonnummern des Benutzers
GET    /api/phone-numbers/[id]               // Einzelne Telefonnummer
PUT    /api/phone-numbers/[id]               // Telefonnummer aktualisieren
DELETE /api/phone-numbers/[id]               // Telefonnummer kündigen

// Agent-Zuweisung
POST   /api/phone-numbers/[id]/assign        // Agent zuweisen
DELETE /api/phone-numbers/[id]/assign        // Zuweisung entfernen

// Konfiguration
PUT    /api/phone-numbers/[id]/config        // Konfiguration aktualisieren
GET    /api/phone-numbers/[id]/config        // Konfiguration abrufen

// Status-Management
POST   /api/phone-numbers/[id]/activate      // Nummer aktivieren
POST   /api/phone-numbers/[id]/deactivate    // Nummer deaktivieren
```

### Datenmodell
```typescript
interface PhoneNumber {
  id: string
  user_id: string
  created_at: string
  updated_at: string
  number: string
  formatted_number: string
  friendly_name?: string
  country_code: string
  country_name: string
  area_code: string
  city?: string
  region?: string
  number_type: 'local' | 'national' | 'toll_free' | 'mobile'
  status: 'active' | 'inactive' | 'suspended' | 'cancelled'
  assigned_agent_id?: string
  provider_id: string
  provider_sid: string
  capabilities: PhoneNumberCapabilities
  pricing: PhoneNumberPricing
  configuration: PhoneNumberConfiguration
  usage_stats?: PhoneNumberUsageStats
}

interface PhoneNumberConfiguration {
  business_hours?: BusinessHours
  forwarding_rules?: ForwardingRule[]
  voicemail_enabled: boolean
  voicemail_greeting?: string
  webhook_url?: string
  webhook_events: string[]
  call_recording_enabled: boolean
  caller_id_lookup_enabled: boolean
}

interface BusinessHours {
  timezone: string
  monday?: TimeRange
  tuesday?: TimeRange
  wednesday?: TimeRange
  thursday?: TimeRange
  friday?: TimeRange
  saturday?: TimeRange
  sunday?: TimeRange
  holidays?: Holiday[]
}

interface TimeRange {
  start: string  // HH:mm format
  end: string    // HH:mm format
}

interface ForwardingRule {
  condition: 'after_hours' | 'busy' | 'no_answer' | 'all'
  target_type: 'phone' | 'voicemail' | 'agent'
  target_value: string
  timeout_seconds?: number
}

interface PhoneNumberUsageStats {
  current_month: {
    total_calls: number
    total_minutes: number
    total_cost: number
    inbound_calls: number
    outbound_calls: number
    average_call_duration: number
  }
  last_30_days: MonthlyUsage[]
}
```

### Datenbankschema
```sql
CREATE TABLE phone_numbers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  number TEXT NOT NULL UNIQUE,
  formatted_number TEXT NOT NULL,
  friendly_name TEXT,
  country_code TEXT NOT NULL,
  country_name TEXT NOT NULL,
  area_code TEXT,
  city TEXT,
  region TEXT,
  number_type TEXT NOT NULL CHECK (number_type IN ('local', 'national', 'toll_free', 'mobile')),
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'cancelled')),
  assigned_agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
  provider_id TEXT NOT NULL,
  provider_sid TEXT NOT NULL,
  capabilities JSONB NOT NULL DEFAULT '{}',
  pricing JSONB NOT NULL DEFAULT '{}',
  configuration JSONB NOT NULL DEFAULT '{}',
  purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  cancelled_at TIMESTAMP WITH TIME ZONE
);

-- Row Level Security
ALTER TABLE phone_numbers ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own phone numbers" ON phone_numbers
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own phone numbers" ON phone_numbers
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own phone numbers" ON phone_numbers
  FOR DELETE USING (auth.uid() = user_id);

-- Indexes für Performance
CREATE INDEX idx_phone_numbers_user ON phone_numbers(user_id);
CREATE INDEX idx_phone_numbers_agent ON phone_numbers(assigned_agent_id);
CREATE INDEX idx_phone_numbers_status ON phone_numbers(status);
CREATE INDEX idx_phone_numbers_number ON phone_numbers(number);

-- Constraint: Ein Agent kann nur eine Nummer haben
CREATE UNIQUE INDEX idx_phone_numbers_unique_agent 
ON phone_numbers(assigned_agent_id) 
WHERE assigned_agent_id IS NOT NULL AND status IN ('active', 'inactive');
```

## UI/UX Anforderungen

### Telefonnummer-Liste
- Tabelle mit Sorting und Filtering
- Status-Badges (aktiv/inaktiv/zugewiesen)
- Agent-Namen in separater Spalte
- Quick-Actions für häufige Operationen
- Bulk-Selection für Massenoperationen

### Telefonnummer-Details
- Two-Column-Layout: Info + Konfiguration
- Tab-Navigation: Übersicht / Konfiguration / Statistiken / Logs
- Inline-Editing für einfache Felder
- Modal-Dialoge für komplexe Konfigurationen
- Real-time Usage-Graphs

### Agent-Zuweisung-Interface
- Dropdown mit verfügbaren Agenten
- Preview der aktuellen Agent-Einstellungen
- Warnung bei Überschreibung bestehender Zuweisungen
- Confirmation-Dialog mit Impact-Summary
- Quick-Assign-Button für neue Agenten

### Konfiguration-Panels
- Business-Hours-Widget mit grafischer Zeitauswahl
- Forwarding-Rules-Builder mit Drag-and-Drop
- Voicemail-Configuration mit Audio-Upload
- Webhook-Configuration mit Test-Button
- Settings-Validation mit Real-time-Feedback

## Services Implementation

### Phone Number Service
```typescript
export class PhoneNumberService {
  constructor(
    private readonly provider: TelephonyProvider,
    private readonly usageService: UsageTrackingService
  ) {}

  async getUserPhoneNumbers(userId: string): Promise<PhoneNumber[]> {
    const numbers = await this.db.query(`
      SELECT 
        pn.*,
        a.name as agent_name,
        COALESCE(usage.total_calls, 0) as total_calls,
        COALESCE(usage.total_cost, 0) as total_cost
      FROM phone_numbers pn
      LEFT JOIN agents a ON pn.assigned_agent_id = a.id
      LEFT JOIN phone_number_usage_stats usage ON pn.id = usage.phone_number_id
      WHERE pn.user_id = $1 AND pn.status != 'cancelled'
      ORDER BY pn.created_at DESC
    `, [userId])

    return numbers.rows
  }

  async assignAgentToPhoneNumber(
    phoneNumberId: string,
    agentId: string,
    userId: string
  ): Promise<void> {
    // Validate ownership
    await this.validatePhoneNumberOwnership(phoneNumberId, userId)
    await this.validateAgentOwnership(agentId, userId)

    // Check if agent already has a number
    const existingAssignment = await this.getAgentPhoneNumber(agentId)
    if (existingAssignment) {
      throw new Error('Agent already has a phone number assigned')
    }

    // Update assignment
    await this.db.query(`
      UPDATE phone_numbers 
      SET assigned_agent_id = $1, updated_at = NOW()
      WHERE id = $2 AND user_id = $3
    `, [agentId, phoneNumberId, userId])

    // Configure number with provider
    await this.configureProviderNumber(phoneNumberId, agentId)

    // Log assignment
    await this.auditLog.log('phone_number_assigned', {
      phoneNumberId,
      agentId,
      userId
    })
  }

  async updatePhoneNumberConfiguration(
    phoneNumberId: string,
    configuration: PhoneNumberConfiguration,
    userId: string
  ): Promise<void> {
    await this.validatePhoneNumberOwnership(phoneNumberId, userId)
    
    // Validate configuration
    this.validateConfiguration(configuration)

    // Update in database
    await this.db.query(`
      UPDATE phone_numbers 
      SET configuration = $1, updated_at = NOW()
      WHERE id = $2 AND user_id = $3
    `, [JSON.stringify(configuration), phoneNumberId, userId])

    // Update provider configuration
    await this.updateProviderConfiguration(phoneNumberId, configuration)
  }

  async cancelPhoneNumber(
    phoneNumberId: string,
    userId: string,
    reason?: string
  ): Promise<void> {
    await this.validatePhoneNumberOwnership(phoneNumberId, userId)

    const phoneNumber = await this.getPhoneNumber(phoneNumberId)
    
    // Release from provider
    await this.provider.releaseNumber(phoneNumber.provider_sid)

    // Update status
    await this.db.query(`
      UPDATE phone_numbers 
      SET 
        status = 'cancelled',
        cancelled_at = NOW(),
        assigned_agent_id = NULL,
        updated_at = NOW()
      WHERE id = $1 AND user_id = $2
    `, [phoneNumberId, userId])

    // Stop billing
    await this.billingService.cancelPhoneNumberSubscription(phoneNumberId)

    // Clean up call logs and related data
    await this.cleanupPhoneNumberData(phoneNumberId)
  }
}
```

### Provider Integration Service
```typescript
export class ProviderIntegrationService {
  async configureProviderNumber(
    phoneNumber: PhoneNumber,
    agentConfig?: AgentConfiguration
  ): Promise<void> {
    const webhookUrl = `${process.env.BASE_URL}/api/webhooks/twilio/incoming-call`
    
    await this.provider.updateNumber(phoneNumber.provider_sid, {
      voiceUrl: webhookUrl,
      voiceMethod: 'POST',
      statusCallback: `${process.env.BASE_URL}/api/webhooks/twilio/call-status`,
      statusCallbackMethod: 'POST'
    })

    // Configure business hours and forwarding
    if (phoneNumber.configuration.business_hours) {
      await this.configureBusinessHours(phoneNumber)
    }

    if (phoneNumber.configuration.forwarding_rules) {
      await this.configureForwardingRules(phoneNumber)
    }
  }

  private async configureBusinessHours(phoneNumber: PhoneNumber): Promise<void> {
    // Implementation depends on provider capabilities
    // May require custom logic in webhook handlers
  }

  private async configureForwardingRules(phoneNumber: PhoneNumber): Promise<void> {
    // Configure TwiML applications for complex routing
    const twimlApp = await this.provider.createApplication({
      friendlyName: `Forwarding Rules for ${phoneNumber.number}`,
      voiceUrl: `${process.env.BASE_URL}/api/webhooks/twilio/forwarding/${phoneNumber.id}`,
      voiceMethod: 'POST'
    })

    await this.provider.updateNumber(phoneNumber.provider_sid, {
      voiceApplicationSid: twimlApp.sid
    })
  }
}
```

## Testkriterien

### Unit Tests
- [ ] Phone Number CRUD-Operationen
- [ ] Agent-Assignment-Logic
- [ ] Configuration-Validation
- [ ] Provider-Integration-Calls

### Integration Tests
- [ ] End-to-End Phone Number Management
- [ ] Agent-Assignment-Flow
- [ ] Provider-Configuration-Updates
- [ ] Billing-Integration

### UI Tests
- [ ] Phone Number Liste und Filter
- [ ] Assignment-Interface
- [ ] Configuration-Forms
- [ ] Cancel-Flow mit Bestätigung

### Performance Tests
- [ ] Large Phone Number Lists
- [ ] Configuration-Update-Performance
- [ ] Provider-API-Response-Times
- [ ] Concurrent Assignment-Operations

## Error-Handling

### Provider-Fehler
- Nummer nicht verfügbar → Alternative vorschlagen
- Konfiguration fehlgeschlagen → Rollback und Retry
- API-Timeouts → Graceful Degradation
- Provider-Outage → Status-Updates für Benutzer

### Validation-Fehler
- Ungültige Agent-Zuweisung → Clear Error Message
- Konfiguration-Conflicts → Guided Resolution
- Business-Rules-Violations → Helpful Suggestions
- Permission-Issues → Proper Access-Control

## Definition of Done
- [ ] Alle Akzeptanzkriterien erfüllt
- [ ] Telefonnummer-Übersicht mit Filtering und Sorting
- [ ] Agent-Assignment-System implementiert
- [ ] Konfiguration-Management für alle Features
- [ ] Provider-Integration für Number-Management
- [ ] Cancel/Deactivate-Funktionalität
- [ ] Database-Schema mit RLS-Policies
- [ ] Unit-, Integration- und Performance-Tests
- [ ] UI/UX für alle Management-Features
- [ ] Error-Handling und Validation
- [ ] Audit-Logging für alle Aktionen
- [ ] Code Review und Documentation

## Abhängigkeiten
- **Story 6.1**: Telefonnummer-Marktplatz (für gekaufte Nummern)
- **Epic 2**: Agent-Management (für Agent-Zuweisung)
- **Provider APIs**: Twilio-Configuration-Endpoints
- **Billing System**: Für Subscription-Management

## Risiken
- **Provider-Configuration-Complexity**: Komplexe Telefonie-Features
- **Agent-Assignment-Conflicts**: Race-Conditions bei Zuweisungen
- **Configuration-Validation**: Viele Edge-Cases zu behandeln
- **Billing-Synchronization**: Korrekte Abrechnung bei Status-Änderungen

## Nächste Schritte nach Completion
- **Story 6.3**: Anruf-Handling und -protokollierung
- **Integration**: Webhook-System für Incoming-Calls