# Epic 5: Wissensbasis-Integration (RAG)

**Status:** Not Started ❌  
**Priorität:** Hoch  
**Aufwand:** 34 Story Points  
**Zeitrahmen:** 3-4 Wochen  

## Übersicht

Epic 5 implementiert das RAG (Retrieval-Augmented Generation) System, das es Benutzern ermöglicht, Wissensdatenbanken zu erstellen, zu verwalten und mit ihren KI-Agenten zu verknüpfen. Dadurch können Agenten auf spezifische Dokumente und Informationen zugreifen und kontextuelle Antworten liefern.

## Geschäftsziele

### Primäre Ziele
- **Kontextuelle Antworten**: Agenten können auf spezifische Unternehmensdaten zugreifen
- **Dokumenten-Integration**: Upload und Verarbeitung verschiedener Dateiformate
- **Wissensmanagement**: Zentrale Verwaltung von Wissensdatenbanken
- **Qualitätssteigerung**: Präzisere und relevantere Agent-Antworten

### Geschäftswert
- **Antwortqualität**: 40% Verbesserung der Antwortrelevanz
- **Effizienzsteigerung**: 30% Reduzierung von Nachfragen
- **Skalierbarkeit**: Einmaliger Upload, mehrfache Nutzung
- **Compliance**: Kontrolle über verwendete Informationen

## Zielgruppen

### Primäre Benutzer
- **Administratoren**: Vollzugriff auf Wissensdatenbank-Management
- **Content-Manager**: Upload und Pflege von Dokumenten
- **Agent-Owner**: Verknüpfung von Wissen mit Agenten

## Technische Anforderungen

### Frontend-Stack
- **Next.js 15** mit React 19 und TypeScript
- **Supabase Storage** für Dateien
- **Vector Database** für Embeddings
- **File Processing** für verschiedene Formate

### Datenmodell

```typescript
interface KnowledgeBase {
  id: string
  user_id: string
  created_at: string
  updated_at: string
  name: string
  description?: string
  status: 'active' | 'inactive'
}

interface DataSource {
  id: string
  knowledge_base_id: string
  created_at: string
  updated_at: string
  type: 'file' | 'url' | 'text'
  source: string
  name: string
  status: 'pending' | 'processing' | 'ready' | 'error'
  file_size?: number
  file_type?: string
}

interface DocumentChunk {
  id: string
  data_source_id: string
  content: string
  embedding: number[]
  metadata: Record<string, any>
  chunk_index: number
}
```

## Stories Übersicht

### Story 5.1: Wissensdatenbank-Verwaltung
**Aufwand**: 8 SP | **Priorität**: Hoch
- CRUD-Operationen für Wissensdatenbanken
- Übersichtsseite und Detailansichten

### Story 5.2: Dokumenten-Upload und -verarbeitung
**Aufwand**: 13 SP | **Priorität**: Hoch
- Multi-Format-Upload (PDF, DOCX, TXT, MD)
- Automatische Textextraktion und Chunking

### Story 5.3: RAG-Integration für Agenten
**Aufwand**: 8 SP | **Priorität**: Hoch
- Verknüpfung von Agenten mit Wissensdatenbanken
- Vector Search und Context Retrieval

### Story 5.4: Erweiterte Dokumentenverwaltung
**Aufwand**: 5 SP | **Priorität**: Mittel
- Vorschau und Bearbeitung von Dokumenten
- Metadaten-Management

## API-Endpoints

```typescript
// Wissensdatenbank-Management
GET    /api/knowledge-bases          // Liste aller Wissensdatenbanken
POST   /api/knowledge-bases          // Neue Wissensdatenbank erstellen
GET    /api/knowledge-bases/[id]     // Einzelne Wissensdatenbank
PUT    /api/knowledge-bases/[id]     // Wissensdatenbank aktualisieren
DELETE /api/knowledge-bases/[id]     // Wissensdatenbank löschen

// Datenquellen-Management
GET    /api/knowledge-bases/[id]/data-sources     // Datenquellen einer KB
POST   /api/knowledge-bases/[id]/data-sources     // Neue Datenquelle hinzufügen
DELETE /api/data-sources/[id]                     // Datenquelle löschen

// Dokumenten-Upload
POST   /api/documents/upload         // Dokument hochladen
GET    /api/documents/[id]/status    // Upload-Status prüfen

// RAG-Queries
POST   /api/knowledge-bases/[id]/search  // Vector Search in KB
```

## UI-Struktur

```
/rag
├── /                           # Übersicht aller Wissensdatenbanken
├── /new                        # Neue Wissensdatenbank erstellen
├── /[id]                       # Details einer Wissensdatenbank
├── /[id]/edit                  # Wissensdatenbank bearbeiten
├── /[id]/documents             # Dokumente verwalten
└── /[id]/documents/upload      # Dokumente hochladen
```

## Dateien zu erstellen

### Backend
- `types/knowledge-base.ts` - TypeScript Interfaces
- `app/api/knowledge-bases/route.ts` - KB CRUD
- `app/api/knowledge-bases/[id]/route.ts` - Einzelne KB
- `app/api/knowledge-bases/[id]/data-sources/route.ts` - Datenquellen
- `app/api/documents/upload/route.ts` - Dokumenten-Upload
- `services/ragService.ts` - RAG-Integration
- `lib/document-processor.ts` - Dokumentenverarbeitung

### Frontend
- `app/rag/page.tsx` - Übersicht
- `app/rag/new/page.tsx` - Neue KB erstellen
- `app/rag/[id]/page.tsx` - KB Details
- `app/rag/[id]/edit/page.tsx` - KB bearbeiten
- `app/rag/[id]/documents/page.tsx` - Dokumentenverwaltung
- `app/rag/[id]/documents/upload/page.tsx` - Upload
- `components/rag/knowledge-base-list.tsx` - KB Liste
- `components/rag/knowledge-base-form.tsx` - KB Formular
- `components/rag/document-upload.tsx` - Upload-Komponente
- `components/rag/document-list.tsx` - Dokumentenliste

## Akzeptanzkriterien

### Funktionale Kriterien
- [ ] Benutzer können Wissensdatenbanken erstellen und verwalten
- [ ] Verschiedene Dateiformate können hochgeladen werden
- [ ] Dokumente werden automatisch verarbeitet und indiziert
- [ ] Agenten können mit Wissensdatenbanken verknüpft werden
- [ ] Vector Search funktioniert mit hoher Genauigkeit
- [ ] Upload-Status wird in Echtzeit angezeigt

### Technische Kriterien
- [ ] Unterstützung für PDF, DOCX, TXT, MD Dateien
- [ ] Automatisches Chunking mit konfigurierbaren Parametern
- [ ] Vector Embeddings für semantische Suche
- [ ] Row Level Security für Datensicherheit
- [ ] File Storage mit Supabase Integration

## Abhängigkeiten

### Interne Abhängigkeiten
- **Epic 2**: Agent-Management (abgeschlossen)
- **Vector Database**: Supabase pgvector Extension
- **File Processing**: PDF.js, docx-parser Libraries

### Externe Abhängigkeiten
- **OpenAI Embeddings API**: Für Vector Embeddings
- **Supabase Storage**: Für Dateispeicherung
- **PDF Processing**: Für Dokumentenextraktion

## Implementierungsplan

### Sprint 1 (1 Woche)
- Story 5.1: Wissensdatenbank-Verwaltung
- Datenbankschema und API-Grundlagen

### Sprint 2 (2 Wochen)
- Story 5.2: Dokumenten-Upload und -verarbeitung
- File Processing Integration

### Sprint 3 (1 Woche)
- Story 5.3: RAG-Integration für Agenten
- Vector Search Implementation

### Sprint 4 (1 Woche)
- Story 5.4: Erweiterte Dokumentenverwaltung
- Testing und Optimierung

## Nächste Schritte

Nach Abschluss von Epic 5 haben Benutzer vollständige RAG-Funktionalität. Epic 6 (Telefonie-Integration) kann dann implementiert werden für die vollständige Plattform-Funktionalität.