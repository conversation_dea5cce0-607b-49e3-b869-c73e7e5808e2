# Epic 6: Telefonie-Integration

**Status:** Not Started ❌  
**Priorität:** Hoch  
**Aufwand:** 42 Story Points  
**Zeitrahmen:** 4-5 Wochen  

## Übersicht

Epic 6 implementiert die vollständige Telefonie-Integration, die es Benutzern ermöglicht, Telefonnummern zu erwerben, zu verwalten und ihren KI-Agenten zuzuweisen. Dies vervollständigt den All-in-One-Ansatz der Plattform ohne externe Telefonie-Anbieter.

## Geschäftsziele

### Primäre Ziele
- **All-in-One-Lösung**: Keine externen Telefonie-Anbieter erforderlich
- **Einfache Telefonnummer-Verwaltung**: <PERSON><PERSON><PERSON>, zu<PERSON>sen und verwalten
- **Anrufprotokollierung**: Vollständige Dokumentation aller Anrufe
- **Kostenoptimierung**: Integrierte Abrechnung und Kostenkontrolle

### Geschäftswert
- **Einfachheit**: 80% weniger Setup-Zeit für neue Benutzer
- **Kostentransparenz**: Einheitliche Abrechnung
- **Skalierbarkeit**: Beliebig viele Nummern pro Agent
- **Compliance**: Vollständige Anrufprotokollierung

## Zielgruppen

### Primäre Benutzer
- **Geschäftsinhaber**: Einfache Telefonnummer-Integration
- **Call-Center-Manager**: Verwaltung mehrerer Nummern
- **Administratoren**: Überwachung und Abrechnung

## Technische Anforderungen

### Frontend-Stack
- **Next.js 15** mit React 19 und TypeScript
- **Voice Processing**: WebRTC für Browser-Integration
- **Telephony API**: Integration mit VoIP-Anbieter
- **Real-time Updates**: WebSocket für Live-Anrufe

### Datenmodell

```typescript
interface PhoneNumber {
  id: string
  user_id: string
  created_at: string
  updated_at: string
  number: string
  friendly_name?: string
  country_code: string
  area_code: string
  status: 'active' | 'inactive' | 'suspended'
  assigned_agent_id?: string
  monthly_cost: number
  provider_id: string
}

interface CallLog {
  id: string
  phone_number_id: string
  agent_id: string
  created_at: string
  start_time: string
  end_time?: string
  duration_seconds?: number
  caller_number: string
  status: 'active' | 'completed' | 'failed' | 'missed'
  end_reason?: 'caller_hangup' | 'agent_hangup' | 'system_error' | 'timeout'
  transcript_url?: string
  recording_url?: string
  cost?: number
  metadata: Record<string, any>
}

interface CallMetrics {
  phone_number_id: string
  date: string
  total_calls: number
  answered_calls: number
  missed_calls: number
  average_duration: number
  total_cost: number
}
```

## Stories Übersicht

### Story 6.1: Telefonnummer-Marktplatz
**Aufwand**: 13 SP | **Priorität**: Hoch
- Verfügbare Nummern durchsuchen und kaufen
- Länder- und Ortsvorwahlen-Filter

### Story 6.2: Telefonnummer-Verwaltung
**Aufwand**: 8 SP | **Priorität**: Hoch
- CRUD-Operationen für Telefonnummern
- Zuweisung zu Agenten

### Story 6.3: Anruf-Handling und -protokollierung
**Aufwand**: 13 SP | **Priorität**: Hoch
- Eingehende Anrufe verarbeiten
- Vollständige Anrufprotokolle

### Story 6.4: Abrechnungs-Integration
**Aufwand**: 5 SP | **Priorität**: Mittel
- Kosten tracking und Abrechnung
- Nutzungsübersicht

### Story 6.5: Erweiterte Anruf-Features
**Aufwand**: 3 SP | **Priorität**: Niedrig
- Anrufweiterleitung und Voicemail
- Geschäftszeiten-Einstellungen

## API-Endpoints

```typescript
// Telefonnummer-Verwaltung
GET    /api/phone-numbers                    // Benutzer-Telefonnummern
POST   /api/phone-numbers                    // Neue Nummer kaufen
GET    /api/phone-numbers/[id]               // Einzelne Nummer
PUT    /api/phone-numbers/[id]               // Nummer aktualisieren
DELETE /api/phone-numbers/[id]               // Nummer kündigen

// Marktplatz
GET    /api/phone-numbers/available          // Verfügbare Nummern
POST   /api/phone-numbers/purchase           // Nummer kaufen

// Anrufprotokolle
GET    /api/phone-numbers/[id]/calls         // Anrufe einer Nummer
GET    /api/calls/[id]                       // Einzelner Anruf
POST   /api/calls                            // Anruf erstellen (Webhook)

// Metriken
GET    /api/phone-numbers/[id]/metrics       // Anruf-Statistiken
GET    /api/billing/phone-usage              // Nutzungsübersicht
```

## UI-Struktur

```
/phone
├── /                              # Übersicht aller Telefonnummern
├── /marketplace                   # Verfügbare Nummern kaufen
├── /[id]                          # Details einer Telefonnummer
├── /[id]/edit                     # Telefonnummer bearbeiten
├── /[id]/calls                    # Anrufhistorie
├── /[id]/metrics                  # Anruf-Statistiken
└── /billing                       # Abrechnungsübersicht
```

## Dateien zu erstellen

### Backend
- `types/phone.ts` - TypeScript Interfaces
- `app/api/phone-numbers/route.ts` - Telefonnummer CRUD
- `app/api/phone-numbers/[id]/route.ts` - Einzelne Nummer
- `app/api/phone-numbers/available/route.ts` - Marktplatz
- `app/api/phone-numbers/purchase/route.ts` - Kauf-Prozess
- `app/api/calls/route.ts` - Anruf-Webhooks
- `app/api/calls/[id]/route.ts` - Anruf-Details
- `services/telephonyService.ts` - Telefonie-Integration
- `lib/call-handler.ts` - Anruf-Verarbeitung

### Frontend
- `app/phone/page.tsx` - Übersicht
- `app/phone/marketplace/page.tsx` - Nummern kaufen
- `app/phone/[id]/page.tsx` - Nummer-Details
- `app/phone/[id]/edit/page.tsx` - Nummer bearbeiten
- `app/phone/[id]/calls/page.tsx` - Anrufhistorie
- `app/phone/[id]/metrics/page.tsx` - Statistiken
- `app/phone/billing/page.tsx` - Abrechnung
- `components/phone/phone-number-list.tsx` - Nummern-Liste
- `components/phone/phone-number-form.tsx` - Nummern-Formular
- `components/phone/marketplace.tsx` - Marktplatz
- `components/phone/call-history.tsx` - Anrufhistorie
- `components/phone/billing-overview.tsx` - Abrechnung

## Telefonie-Provider Integration

### Option 1: Twilio Integration
```typescript
// Twilio-basierte Implementation
interface TwilioConfig {
  accountSid: string
  authToken: string
  webhookUrl: string
}

// Webhook Handler für eingehende Anrufe
POST /api/webhooks/twilio/incoming-call
```

### Option 2: Custom VoIP-Provider
```typescript
// Eigene VoIP-Integration
interface VoIPProvider {
  purchaseNumber(areaCode: string): Promise<PhoneNumber>
  configureWebhook(number: string, webhookUrl: string): Promise<void>
  handleIncomingCall(callData: IncomingCall): Promise<CallResponse>
}
```

## Akzeptanzkriterien

### Funktionale Kriterien
- [ ] Benutzer können verfügbare Telefonnummern durchsuchen
- [ ] Telefonnummern können gekauft und verwaltet werden
- [ ] Nummern können Agenten zugewiesen werden
- [ ] Eingehende Anrufe werden korrekt weitergeleitet
- [ ] Alle Anrufe werden vollständig protokolliert
- [ ] Kosten werden transparent dargestellt

### Technische Kriterien
- [ ] Integration mit Telefonie-Provider
- [ ] Webhook-basierte Anruf-Verarbeitung
- [ ] Real-time Updates für aktive Anrufe
- [ ] Sichere Abrechnung und Kostenverfolgung
- [ ] Skalierbare Anruf-Verarbeitung

## Abhängigkeiten

### Interne Abhängigkeiten
- **Epic 2**: Agent-Management (abgeschlossen)
- **Epic 3**: Monitoring für Live-Anrufe (abgeschlossen)
- **Billing System**: Für Kostenverfolgung

### Externe Abhängigkeiten
- **Telefonie-Provider**: Twilio oder alternativer VoIP-Anbieter
- **Payment Processing**: Für Telefonnummer-Käufe
- **Webhook Infrastructure**: Für Anruf-Events

## Sicherheitsüberlegungen

### Datenschutz
- **Anruferidentifikation**: GDPR-konforme Speicherung
- **Aufzeichnungen**: Opt-in für Anrufaufzeichnung
- **Protokollierung**: Minimale Datenspeicherung

### Sicherheit
- **Webhook-Validierung**: Signatur-basierte Verifikation
- **Rate Limiting**: Schutz vor Missbrauch
- **Encryption**: Sichere Übertragung aller Daten

## Implementierungsplan

### Sprint 1 (1 Woche)
- Story 6.2: Telefonnummer-Verwaltung (Basis)
- Datenbankschema und API-Grundlagen

### Sprint 2 (2 Wochen)
- Story 6.1: Telefonnummer-Marktplatz
- Provider-Integration

### Sprint 3 (2 Wochen)
- Story 6.3: Anruf-Handling und -protokollierung
- Webhook-Integration

### Sprint 4 (1 Woche)
- Story 6.4: Abrechnungs-Integration
- Story 6.5: Erweiterte Features
- Testing und Optimierung

## Nächste Schritte

Mit Epic 6 ist die KI-Sprachassistenten-Plattform vollständig. Alle Core-Features (Agenten, Testing, Monitoring, RAG, Telefonie) sind implementiert und bieten eine vollständige All-in-One-Lösung.