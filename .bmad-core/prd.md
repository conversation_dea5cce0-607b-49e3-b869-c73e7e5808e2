# **KI-Sprachassistenten-Plattform Product Requirements Document (PRD)**

## **1\. Ziele und Hintergrundkontext**

### **Ziele**

* **Einfachheit & Professionalität:** Eine selbsterklärende und professionell anmutende Benutzeroberfläche bereitstellen, die Vertrauen schafft.  
* **Wertschöpfung:** Benutzern ermöglichen, durch die Plattform entweder Kosten zu sparen oder ein eigenes Geschäft aufzubauen.  
* **Vertrauen durch Testen:** Eine robuste, interaktive Testumgebung anbieten, in der Benutzer die Zuverlässigkeit ihrer Agenten überprüfen können.  
* **Kostentransparenz:** Ein klares und verständliches Preis- und Nutzungsmodell durch den All-in-One-Ansatz gewährleisten.

### **Hintergrundkontext**

Dieses Dokument beschreibt die Anforderungen für eine Web-Plattform zur Erstellung und Verwaltung von KI-Sprachassistenten. Die Plattform differenziert sich durch einen All-in-One-Ansatz, der es den Benutzern erspart, Die<PERSON><PERSON> von Drittanbietern (wie Twilio für Telefonie oder Make.com für Workflows) integrieren zu müssen. Die Zielgruppe ist breit gefächert und umfasst sowohl technisch versierte Entwickler als auch Geschäftsanwender ohne Programmierkenntnisse und Hobbyisten.

### **Änderungsprotokoll**

| Datum | Version | Beschreibung | Autor |
| :---- | :---- | :---- | :---- |
| 29\. Juli 2025 | 1.0 | Erster Entwurf des PRD | John (PM) |

## **2\. Anforderungen**

### **Funktionale Anforderungen (FR)**

1. **FR1: Agenten-Verwaltung:** Benutzer müssen KI-Agenten erstellen, in einer Liste anzeigen, bearbeiten und löschen können.  
2. **FR2: Interaktives Testen:** Das System muss eine Testumgebung bereitstellen, in der ein Benutzer über das Browser-Mikrofon in Echtzeit mit seinem konfigurierten Agenten sprechen kann.  
3. **FR3: KPI-Dashboard:** Das System muss ein Dashboard anzeigen, das Leistungsindikatoren (KPIs) wie Anrufanzahl, Gesprächsdauer und Anrufergebnis für die Agenten darstellt.  
4. **FR4: RAG-System:** Benutzer müssen Wissensdatenbanken erstellen und verwalten und diese mit ihren Agenten verknüpfen können.  
5. **FR5: Integrierte Telefonie:** Die Plattform muss Telefonnummern bereitstellen, die den Agenten zugewiesen werden können, ohne dass ein externer Anbieter erforderlich ist.

### **Nicht-funktionale Anforderungen (NFR)**

1. **NFR1: Responsivität:** Die gesamte Benutzeroberfläche muss auf Desktop-, Tablet- und Mobilgeräten vollständig nutzbar sein.  
2. **NFR2: Barrierefreiheit:** Die Anwendung muss den WCAG 2.1 AA-Standards für Barrierefreiheit entsprechen.  
3. **NFR3: Performance:** Benutzerinteraktionen auf der Oberfläche sollen eine spürbare Rückmeldung in unter 100 Millisekunden geben.  
4. **NFR4: Transparenz:** Nutzungs- und Kostendaten müssen für den Benutzer jederzeit klar und verständlich einsehbar sein.

## **3\. User Interface Design Goals**

### **Overall UX Vision**

Die Benutzeroberfläche soll professionell, intuitiv und aufgeräumt sein. Das Design soll Vertrauen schaffen und den Benutzer durch klare, verständliche Darstellungen von komplexen Daten befähigen, seine KI-Agenten optimal zu verwalten und zu verbessern. Die Erfahrung soll sich einfach und direkt anfühlen, um die Einstiegshürde für alle Zielgruppen niedrig zu halten.

### **Key Interaction Paradigms**

Die Anwendung funktioniert als Single-Page-Application (SPA) mit einem zentralen Dashboard als Haupt-Ankerpunkt. Die Interaktion basiert auf direkter Manipulation (Listenansichten, Konfigurationsformulare, interaktive Testumgebung) und gibt dem Benutzer stets klares, visuelles Feedback auf seine Aktionen.

### **Core Screens and Views**

Um die funktionalen Anforderungen zu erfüllen, sind die folgenden Kernbildschirme oder \-ansichten für den MVP erforderlich:

* Dashboard: Globale Übersicht über alle Agenten und deren aggregierte Leistung.  
* Agenten-Liste: Eine Ansicht zur Verwaltung aller erstellten Agenten.  
* Agenten-Konfigurationsseite: Ein Formular zum Erstellen und Bearbeiten der Agenten-Einstellungen.  
* Agenten-Testbereich: Eine interaktive Oberfläche zum Testen eines Agenten per Spracheingabe.  
* Nutzungs- & Abrechnungsseite: Eine klare Übersicht über die aktuelle Nutzung und die Kosten.

### **Accessibility**

Die Anwendung muss den WCAG 2.1 Level AA Standards entsprechen, um die Nutzbarkeit für Menschen mit Behinderungen zu gewährleisten.

### **Branding**

Das Design orientiert sich am bereitgestellten Screenshot: ein modernes, professionelles Dark-Theme mit blauen Akzentfarben, einer klaren Typografie und großzügigen Abständen.

### **Target Device and Platforms**

Die Anwendung wird als responsive Web-App entwickelt, die auf allen gängigen Desktop-, Tablet- und Smartphone-Browsern eine optimale Benutzererfahrung bietet.

## **4\. Technische Annahmen**

Dieser Abschnitt fasst die technischen Entscheidungen zusammen, die als Grundlage für die Entwicklung dienen. Diese wurden bereits in der Architekturphase detailliert ausgearbeitet.

* **Repository-Struktur:** Die Anwendung wird als Monorepo entwickelt, das mit pnpm Workspaces verwaltet wird. Dies ermöglicht die einfache gemeinsame Nutzung von Code (z.B. Typ-Definitionen) zwischen Frontend und Backend.  
* **Service-Architektur:** Die Architektur basiert auf einem Full-Stack Next.js-Ansatz, der auf Vercel gehostet wird. Das Backend besteht aus Next.js API Routes (Serverless Functions) und Supabase als Backend-as-a-Service (BaaS) für Datenbank, Authentifizierung und Speicher.  
* **Testing-Anforderungen:** Für alle Teile der Anwendung werden Unit- und Integration-Tests mit Jest und der React Testing Library geschrieben. Eine hohe Testabdeckung für kritische Funktionen ist erforderlich.  
* **Zusätzliche Technische Annahmen:**  
  * Die primäre Programmiersprache ist TypeScript.  
  * Das Styling wird mit Tailwind CSS umgesetzt.  
  * Die UI-Komponenten basieren auf Shadcn/UI.

## **5\. Epic-Liste**

Die Entwicklung wird in der folgenden, logisch aufeinander aufbauenden Reihenfolge von Epics durchgeführt.

* **Epic 1: Fundament & Benutzer-Onboarding — Status: Done**
  * **Ziel:** Die grundlegende Projektinfrastruktur, die Benutzerauthentifizierung (Registrierung, Login) und die Haupt-Anwendungshülle einrichten, sodass sich ein Benutzer anmelden und eine leere Dashboard-Seite sehen kann.
* **Epic 2: Kernfunktion \- Agenten-Verwaltung — Status: In Progress**
  * **Ziel:** Benutzern ermöglichen, ihre KI-Sprachassistenten zu erstellen, in einer Liste anzuzeigen, zu konfigurieren und zu verwalten, um die Kernfunktionalität der Plattform bereitzustellen.
  * **Aktueller Stand:** E2E-Verdrahtung Liste/Create/Edit/Delete inkl. Toasts, Redirects und Fehlerzuständen in der UI ist umgesetzt. Zentrale Client-Schicht vorhanden: [services/agents.ts](services/agents.ts). Zentraler Fetch-Client vorhanden: [services/http.ts](services/http.ts).
* **Epic 3: Interaktives Agenten-Testing — Status: Planned**
  * **Ziel:** Eine interaktive Testumgebung bereitstellen, in der Benutzer per Sprache mit ihren Agenten kommunizieren können, um deren Verhalten zu validieren und Vertrauen aufzubauen.
* **Epic 4: Performance-Analyse & Dashboard — Status: Planned**
  * **Ziel:** Die im Dashboard entworfenen KPIs mit echten Daten zu füllen und Anrufprotokolle anzuzeigen, um den Benutzern umsetzbare Einblicke zur Optimierung ihrer Agenten zu geben.
* **Epic 5: Wissensbasis-Integration (RAG) — Status: Planned**
  * **Ziel:** Benutzern erlauben, Wissensdatenbanken zu erstellen, zu verwalten und mit Agenten zu verknüpfen, damit diese auf Basis spezifischer Informationen antworten können.
* **Epic 6: Telefonie-Integration — Status: Planned**
  * **Ziel:** Die Telefonie-Dienste integrieren, sodass Benutzer Telefonnummern erwerben und ihren Agenten zuweisen können, um echte Anrufe zu empfangen.

## **6\. Epic 1: Fundament & Benutzer-Onboarding**

**Ziel:** Die grundlegende Projektinfrastruktur, die Benutzerauthentifizierung (Registrierung, Login) und die Haupt-Anwendungshülle einrichten, sodass sich ein Benutzer anmelden und eine leere Dashboard-Seite sehen kann.

### **Story 1.1: Projekt-Initialisierung & Supabase-Anbindung**

* **Als** Entwickler,  
* **möchte ich** das Next.js-Projekt initialisieren und es mit einem neuen Supabase-Projekt verbinden,  
* **sodass** die grundlegende technische Infrastruktur vorhanden ist.

#### **Akzeptanzkriterien:**

1. Ein neues Next.js-Projekt ist mit pnpm create next-app erstellt.  
2. Ein neues Supabase-Projekt ist im Supabase-Dashboard erstellt.  
3. Die Umgebungsvariablen für die Supabase-URL und den anon key sind in .env.local hinterlegt.
4. Ein Supabase-Client (lib/supabase.ts) ist erstellt und konfiguriert.
5. Ein zentraler Fetch-Client ist eingerichtet ([services/http.ts](services/http.ts)) und eine zentrale Client-Schicht für Agenten ist vorhanden ([services/agents.ts](services/agents.ts)).
6. Eine Beispiel-Env-Datei existiert (.env.local.example) mit NEXT_PUBLIC_API_BASE_URL für zentrale API-Basis-URL.

### **Story 1.2: Benutzer-Registrierung**

* **Als** neuer Benutzer,  
* **möchte ich** mich mit meiner E-Mail-Adresse und einem Passwort registrieren können,  
* **sodass** ich einen Account auf der Plattform erstellen kann.

#### **Akzeptanzkriterien:**

1. Ein Registrierungsformular ist unter /signup (oder ähnlich) erreichbar.  
2. Das Formular ruft bei der Übermittlung die signUp-Methode von Supabase Auth auf.  
3. Ein neuer Benutzer wird in der auth.users-Tabelle in Supabase angelegt.  
4. Nach erfolgreicher Registrierung wird eine Nachricht angezeigt, die den Benutzer auffordert, seine E-Mail zu bestätigen.

### **Story 1.3: Benutzer-Login**

* **Als** registrierter Benutzer,  
* **möchte ich** mich mit meiner E-Mail-Adresse und meinem Passwort anmelden können,  
* **sodass** ich auf mein Dashboard zugreifen kann.

#### **Akzeptanzkriterien:**

1. Ein Login-Formular ist unter /login erreichbar.  
2. Das Formular ruft die signInWithPassword-Methode von Supabase Auth auf.  
3. Bei erfolgreicher Anmeldung wird der Benutzer zur Hauptseite (/) weitergeleitet.  
4. Bei ungültigen Anmeldedaten wird eine Fehlermeldung angezeigt.

### **Story 1.4: Anwendungs-Layout**

* **Als** eingeloggter Benutzer,  
* **möchte ich** das Haupt-Layout der Anwendung mit Seitenleiste und Kopfzeile sehen,  
* **sodass** ich durch die Plattform navigieren kann.

#### **Akzeptanzkriterien:**

1. Nach dem Login wird ein Layout mit einer linken Seitenleiste angezeigt.  
2. Die Seitenleiste enthält die Navigationspunkte (Dashboard, Agenten etc.).  
3. Die Kopfzeile zeigt grundlegende Benutzerinformationen an.  
4. Der Hauptinhaltsbereich zeigt eine Platzhalter-Nachricht an.

### **Story 1.5: Benutzer-Logout**

* **Als** eingeloggter Benutzer,  
* **möchte ich** mich abmelden können,  
* **sodass** ich meine Sitzung sicher beenden kann.

#### **Akzeptanzkriterien:**

1. Ein Logout-Button ist im Layout vorhanden.  
2. Ein Klick auf den Button ruft die signOut-Methode von Supabase Auth auf.  
3. Der Benutzer wird zur Login-Seite (/login) weitergeleitet.