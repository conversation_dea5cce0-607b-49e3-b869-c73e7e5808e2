import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

/**
 * Middleware: leitet /login nur in Produktion auf die externe Landing-URL weiter.
 * - Im Development bleibt /login lokal verfügbar, um Auth-Flows zu testen.
 * - 307 Temporary Redirect (bei Bedarf auf 308 permanent ändern).
 */
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  const isProd = process.env.NODE_ENV === 'production'
  if (isProd && pathname === '/login') {
    return NextResponse.redirect('https://jasz-ai.com/login', { status: 307 })
  }

  return NextResponse.next()
}

/**
 * Matcher: Nur die /login Route abfangen.
 */
export const config = {
  matcher: ['/login'],
}