'use client'

import React, { useState } from 'react'
import { supabase } from '@/lib/supabaseClient'

// shadcn/ui Komponenten
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'

// util: cn falls vorhanden, sonst Fallback
let cn: (...cls: Array<string | false | null | undefined>) => string
try {
  // optionales util, falls im Projekt vorhanden
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  cn = require('@/lib/utils').cn
} catch {
  cn = (...cls) => cls.filter(Boolean).join(' ')
}

export default function Page() {
  const [emailPwd, setEmailPwd] = useState('')
  const [password, setPassword] = useState('')
  const [emailMagic, setEmailMagic] = useState('')
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  const reset = () => {
    setSuccess(null)
    setError(null)
  }

  async function handleSignIn(e: React.FormEvent) {
    e.preventDefault()
    reset()
    setLoading(true)
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email: emailPwd,
        password,
      })
      if (error) throw error
      setSuccess('Login erfolgreich.')
      // window.location.href = '/dashboard'
    } catch (err: any) {
      setError(err?.message ?? 'Unbekannter Fehler beim Login')
    } finally {
      setLoading(false)
    }
  }

  async function handleSignUp(e: React.FormEvent) {
    e.preventDefault()
    reset()
    setLoading(true)
    try {
      const { error } = await supabase.auth.signUp({
        email: emailPwd,
        password,
        options: {
          emailRedirectTo: typeof window !== 'undefined' ? `${window.location.origin}/` : undefined,
        },
      })
      if (error) throw error
      setSuccess('Registrierung angelegt. Prüfe bitte deine E-Mail.')
    } catch (err: any) {
      setError(err?.message ?? 'Unbekannter Fehler bei der Registrierung')
    } finally {
      setLoading(false)
    }
  }

  async function handleMagicLink(e: React.FormEvent) {
    e.preventDefault()
    reset()
    setLoading(true)
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email: emailMagic,
        options: {
          emailRedirectTo: typeof window !== 'undefined' ? `${window.location.origin}/` : undefined,
        },
      })
      if (error) throw error
      setSuccess('Magic Link gesendet. Bitte Posteingang prüfen.')
    } catch (err: any) {
      setError(err?.message ?? 'Unbekannter Fehler beim Senden des Magic Links')
    } finally {
      setLoading(false)
    }
  }

  async function handleSignOut() {
    reset()
    setLoading(true)
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      setSuccess('Abgemeldet.')
    } catch (err: any) {
      setError(err?.message ?? 'Abmelden fehlgeschlagen')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className={cn('min-h-[calc(100dvh-64px)] w-full flex items-center justify-center p-6')}>
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Login</CardTitle>
          <CardDescription>Melde dich an oder registriere einen neuen Account</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="password" className="w-full">
            <TabsList className="grid grid-cols-2">
              <TabsTrigger value="password">E-Mail / Passwort</TabsTrigger>
              <TabsTrigger value="magic">Magic Link</TabsTrigger>
            </TabsList>

            <TabsContent value="password" className="mt-4">
              <form onSubmit={handleSignIn} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">E-Mail</Label>
                  <Input
                    id="email"
                    type="email"
                    value={emailPwd}
                    onChange={(e) => setEmailPwd(e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Passwort</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="********"
                    required
                  />
                </div>

                <div className="flex gap-2 flex-wrap">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Anmelden…' : 'Anmelden'}
                  </Button>
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={handleSignUp}
                    disabled={loading || !emailPwd || !password}
                  >
                    {loading ? 'Registriere…' : 'Registrieren'}
                  </Button>
                  <Button type="button" variant="outline" onClick={handleSignOut} disabled={loading}>
                    Abmelden
                  </Button>
                </div>
              </form>
            </TabsContent>

            <TabsContent value="magic" className="mt-4">
              <form onSubmit={handleMagicLink} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="magic-email">E-Mail</Label>
                  <Input
                    id="magic-email"
                    type="email"
                    value={emailMagic}
                    onChange={(e) => setEmailMagic(e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div className="flex gap-2 flex-wrap">
                  <Button type="submit" disabled={loading || !emailMagic}>
                    {loading ? 'Sende…' : 'Magic Link senden'}
                  </Button>
                  <Button type="button" variant="outline" onClick={handleSignOut} disabled={loading}>
                    Abmelden
                  </Button>
                </div>
              </form>
            </TabsContent>
          </Tabs>

          <div className="mt-4 space-y-2">
            {success && (
              <Alert className="border-green-600/40">
                <AlertDescription className="text-green-600">{success}</AlertDescription>
              </Alert>
            )}
            {error && (
              <Alert className="border-red-600/40">
                <AlertDescription className="text-red-600">{error}</AlertDescription>
              </Alert>
            )}
          </div>

          <div className="mt-6 text-xs text-muted-foreground">
            <p>
              Stelle in Supabase {'>'} Authentication {'>'} URL Configuration sicher, dass{' '}
              <span className="font-mono">http://localhost:3000</span> als Redirect-URL gesetzt ist.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
