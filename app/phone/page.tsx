'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { AppSidebar } from '@/components/app-sidebar'
import { AuthGuard } from '@/components/auth-guard'
import { SiteHeader } from '@/components/dashboard/site-header'
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar'
import {
  Phone,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  <PERSON><PERSON><PERSON>3,
  PhoneCall,
  Loader2,
  PhoneIncoming,
  Settings
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface PhoneNumber {
  id: string
  number: string
  friendly_name?: string
  country_code: string
  area_code?: string
  status: 'active' | 'inactive' | 'suspended'
  assigned_agent_id?: string
  monthly_cost: number
  created_at: string
  agents?: {
    id: string
    name: string
  }
}

export default function PhoneNumbersPage() {
  const [phoneNumbers, setPhoneNumbers] = useState<PhoneNumber[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [assignedFilter, setAssignedFilter] = useState<string>('all')

  useEffect(() => {
    fetchPhoneNumbers()
  }, [statusFilter, assignedFilter])

  const fetchPhoneNumbers = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (statusFilter !== 'all') params.set('status', statusFilter)
      if (assignedFilter !== 'all') params.set('assigned', assignedFilter)

      const response = await fetch(`/api/phone-numbers?${params}`)
      if (response.ok) {
        const data = await response.json()
        setPhoneNumbers(data)
      } else {
        console.error('Failed to fetch phone numbers')
      }
    } catch (error) {
      console.error('Error fetching phone numbers:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeletePhoneNumber = async (id: string) => {
    if (!confirm('Are you sure you want to delete this phone number? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/phone-numbers/${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setPhoneNumbers(prev => prev.filter(pn => pn.id !== id))
      } else {
        const errorData = await response.json()
        console.error('Failed to delete phone number:', errorData.error)
      }
    } catch (error) {
      console.error('Error deleting phone number:', error)
    }
  }

  const formatPhoneNumber = (number: string): string => {
    // Format phone number for display
    if (number.startsWith('+1')) {
      const cleaned = number.substring(2)
      return `+1 (${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6)}`
    }
    return number
  }

  const filteredPhoneNumbers = phoneNumbers.filter(phone =>
    phone.number.toLowerCase().includes(searchQuery.toLowerCase()) ||
    phone.friendly_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    phone.agents?.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const activeNumbers = phoneNumbers.filter(phone => phone.status === 'active').length
  const totalCost = phoneNumbers.reduce((sum, phone) => sum + phone.monthly_cost, 0)

  if (loading) {
    return (
      <AuthGuard>
        <SidebarProvider
          style={
            {
              "--sidebar-width": "calc(var(--spacing) * 72)",
              "--header-height": "calc(var(--spacing) * 12)",
            } as React.CSSProperties
          }
        >
          <AppSidebar variant="inset" />
          <SidebarInset>
            <SiteHeader title="Phone Numbers" />
            <div className="flex flex-1 flex-col">
              <div className="@container/main flex flex-1 flex-col gap-2">
                <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                  <div className="px-4 lg:px-6">
                    <div className="flex items-center justify-center py-12">
                      <Loader2 className="h-8 w-8 animate-spin" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </SidebarInset>
        </SidebarProvider>
      </AuthGuard>
    )
  }

  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader
            title="Phone Numbers"
            showCreateButton
            createButtonText="Buy Number"
            createButtonHref="/phone/marketplace"
          />
          <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                <div className="px-4 lg:px-6">
                  {/* Toolbar with secondary actions */}
                  <div className="flex justify-end mb-4 gap-3">
                    <Button variant="outline" asChild>
                      <Link href="/phone/billing">
                        <BarChart3 className="h-4 w-4 mr-2" />
                        Billing
                      </Link>
                    </Button>
                  </div>

                  {/* Statistics Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base flex items-center gap-2">
                          <PhoneCall className="h-4 w-4" />
                          Active Numbers
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{activeNumbers}</div>
                        <p className="text-sm text-gray-500">
                          {phoneNumbers.length} total numbers
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base flex items-center gap-2">
                          <PhoneIncoming className="h-4 w-4" />
                          Assigned Numbers
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {phoneNumbers.filter(phone => phone.assigned_agent_id).length}
                        </div>
                        <p className="text-sm text-gray-500">
                          Connected to agents
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">Monthly Cost</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">${totalCost.toFixed(2)}</div>
                        <p className="text-sm text-gray-500">
                          Total monthly fees
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Filters and Search */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span>Phone Numbers ({phoneNumbers.length})</span>
                      </CardTitle>
                      <div className="flex gap-4">
                        <div className="relative flex-1 max-w-sm">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <Input
                            placeholder="Search phone numbers..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-10"
                          />
                        </div>
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                          <SelectTrigger className="w-32">
                            <SelectValue placeholder="Status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Status</SelectItem>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="inactive">Inactive</SelectItem>
                            <SelectItem value="suspended">Suspended</SelectItem>
                          </SelectContent>
                        </Select>
                        <Select value={assignedFilter} onValueChange={setAssignedFilter}>
                          <SelectTrigger className="w-32">
                            <SelectValue placeholder="Assignment" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All</SelectItem>
                            <SelectItem value="true">Assigned</SelectItem>
                            <SelectItem value="false">Unassigned</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {filteredPhoneNumbers.length === 0 ? (
                        <div className="text-center py-8">
                          <Phone className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                          <h3 className="text-lg font-medium text-gray-700 mb-2">
                            {phoneNumbers.length === 0 ? 'No phone numbers yet' : 'No phone numbers found'}
                          </h3>
                          <p className="text-gray-500 mb-6">
                            {phoneNumbers.length === 0
                              ? 'Purchase your first phone number to get started with voice calls.'
                              : 'Try adjusting your search or filter criteria.'
                            }
                          </p>
                          {phoneNumbers.length === 0 && (
                            <Button asChild>
                              <Link href="/phone/marketplace">
                                <Plus className="h-4 w-4 mr-2" />
                                Buy Your First Number
                              </Link>
                            </Button>
                          )}
                        </div>
                      ) : (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Phone Number</TableHead>
                              <TableHead>Name</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Assigned Agent</TableHead>
                              <TableHead>Monthly Cost</TableHead>
                              <TableHead>Added</TableHead>
                              <TableHead className="w-12"></TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {filteredPhoneNumbers.map((phone) => (
                              <TableRow key={phone.id}>
                                <TableCell>
                                  <div className="font-medium">
                                    {formatPhoneNumber(phone.number)}
                                  </div>
                                  {phone.area_code && (
                                    <div className="text-sm text-gray-500">
                                      {phone.country_code} • Area {phone.area_code}
                                    </div>
                                  )}
                                </TableCell>
                                <TableCell>
                                  <div className="font-medium">
                                    {phone.friendly_name || 'Unnamed'}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge
                                    variant={
                                      phone.status === 'active' ? 'default' :
                                      phone.status === 'inactive' ? 'secondary' :
                                      'destructive'
                                    }
                                  >
                                    {phone.status}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  {phone.agents ? (
                                    <div className="flex items-center gap-2">
                                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                      <span className="font-medium">{phone.agents.name}</span>
                                    </div>
                                  ) : (
                                    <span className="text-gray-500">Unassigned</span>
                                  )}
                                </TableCell>
                                <TableCell className="font-medium">
                                  ${phone.monthly_cost.toFixed(2)}
                                </TableCell>
                                <TableCell className="text-gray-500">
                                  {formatDistanceToNow(new Date(phone.created_at), { addSuffix: true })}
                                </TableCell>
                                <TableCell>
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                        <MoreHorizontal className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem asChild>
                                        <Link href={`/phone/${phone.id}`}>
                                          <BarChart3 className="mr-2 h-4 w-4" />
                                          View Details
                                        </Link>
                                      </DropdownMenuItem>
                                      <DropdownMenuItem asChild>
                                        <Link href={`/phone/${phone.id}/edit`}>
                                          <Edit className="mr-2 h-4 w-4" />
                                          Edit
                                        </Link>
                                      </DropdownMenuItem>
                                      <DropdownMenuItem
                                        className="text-red-600"
                                        onClick={() => handleDeletePhoneNumber(phone.id)}
                                      >
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Delete
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </SidebarInset>
        </SidebarProvider>
      </AuthGuard>
    )
}