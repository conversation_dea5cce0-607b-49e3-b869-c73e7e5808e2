'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { 
  Phone, 
  ArrowLeft, 
  Search, 
  Filter, 
  ShoppingCart, 
  Loader2,
  MessageSquare,
  PhoneCall,
  MapPin,
  DollarSign,
  Check
} from 'lucide-react'

interface AvailablePhoneNumber {
  phone_number: string
  country_code: string
  area_code: string
  region: string
  capabilities: {
    voice: boolean
    sms: boolean
    mms?: boolean
  }
  monthly_cost: number
  setup_cost?: number
}

interface Agent {
  id: string
  name: string
  description?: string
}

export default function PhoneMarketplacePage() {
  const router = useRouter()
  const [availableNumbers, setAvailableNumbers] = useState<AvailablePhoneNumber[]>([])
  const [agents, setAgents] = useState<Agent[]>([])
  const [loading, setLoading] = useState(false)
  const [purchasing, setPurchasing] = useState(false)
  
  // Filters
  const [countryCode, setCountryCode] = useState('US')
  const [areaCode, setAreaCode] = useState('')
  const [contains, setContains] = useState('')
  const [voiceEnabled, setVoiceEnabled] = useState(true)
  const [smsEnabled, setSmsEnabled] = useState(false)
  
  // Purchase dialog
  const [purchaseDialogOpen, setPurchaseDialogOpen] = useState(false)
  const [selectedNumber, setSelectedNumber] = useState<AvailablePhoneNumber | null>(null)
  const [friendlyName, setFriendlyName] = useState('')
  const [assignedAgentId, setAssignedAgentId] = useState<string>('')

  useEffect(() => {
    fetchAgents()
    searchNumbers()
  }, [])

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      searchNumbers()
    }, 500)
    
    return () => clearTimeout(debounceTimer)
  }, [countryCode, areaCode, contains, voiceEnabled, smsEnabled])

  const fetchAgents = async () => {
    try {
      const response = await fetch('/api/agents')
      if (response.ok) {
        const data = await response.json()
        setAgents(data)
      }
    } catch (error) {
      console.error('Error fetching agents:', error)
    }
  }

  const searchNumbers = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        country_code: countryCode,
        limit: '20'
      })
      
      if (areaCode) params.set('area_code', areaCode)
      if (contains) params.set('contains', contains)
      if (voiceEnabled) params.set('voice_enabled', 'true')
      if (smsEnabled) params.set('sms_enabled', 'true')

      const response = await fetch(`/api/phone-numbers/available?${params}`)
      if (response.ok) {
        const data = await response.json()
        setAvailableNumbers(data.numbers)
      } else {
        console.error('Failed to fetch available numbers')
      }
    } catch (error) {
      console.error('Error searching numbers:', error)
    } finally {
      setLoading(false)
    }
  }

  const handlePurchaseClick = (number: AvailablePhoneNumber) => {
    setSelectedNumber(number)
    setFriendlyName(`Phone ${formatPhoneNumber(number.phone_number)}`)
    setPurchaseDialogOpen(true)
  }

  const handlePurchase = async () => {
    if (!selectedNumber) return

    try {
      setPurchasing(true)
      const response = await fetch('/api/phone-numbers/purchase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          phone_number: selectedNumber.phone_number,
          friendly_name: friendlyName,
          assigned_agent_id: assignedAgentId || undefined
        })
      })

      if (response.ok) {
        const purchasedNumber = await response.json()
        setPurchaseDialogOpen(false)
        router.push(`/phone/${purchasedNumber.id}`)
      } else {
        const errorData = await response.json()
        console.error('Purchase failed:', errorData.error)
        alert(`Purchase failed: ${errorData.error}`)
      }
    } catch (error) {
      console.error('Error purchasing number:', error)
      alert('Purchase failed. Please try again.')
    } finally {
      setPurchasing(false)
    }
  }

  const formatPhoneNumber = (number: string): string => {
    if (number.startsWith('+1')) {
      const cleaned = number.substring(2)
      return `+1 (${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6)}`
    }
    return number
  }

  const getCapabilityBadges = (capabilities: AvailablePhoneNumber['capabilities']) => {
    const badges = []
    if (capabilities.voice) {
      badges.push(
        <Badge key="voice" variant="default" className="bg-green-100 text-green-800">
          <PhoneCall className="h-3 w-3 mr-1" />
          Voice
        </Badge>
      )
    }
    if (capabilities.sms) {
      badges.push(
        <Badge key="sms" variant="default" className="bg-blue-100 text-blue-800">
          <MessageSquare className="h-3 w-3 mr-1" />
          SMS
        </Badge>
      )
    }
    return badges
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/phone">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Phone Numbers
          </Link>
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <ShoppingCart className="h-8 w-8 text-blue-600" />
            Phone Number Marketplace
          </h1>
          <p className="text-gray-600 mt-2">
            Find and purchase phone numbers for your voice agents
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search Available Numbers</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <Label>Country</Label>
              <Select value={countryCode} onValueChange={setCountryCode}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="US">🇺🇸 United States</SelectItem>
                  <SelectItem value="GB">🇬🇧 United Kingdom</SelectItem>
                  <SelectItem value="CA">🇨🇦 Canada</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Area Code (Optional)</Label>
              <Input
                placeholder="555"
                value={areaCode}
                onChange={(e) => setAreaCode(e.target.value)}
                maxLength={3}
              />
            </div>

            <div>
              <Label>Contains Digits</Label>
              <Input
                placeholder="123"
                value={contains}
                onChange={(e) => setContains(e.target.value)}
              />
            </div>

            <div>
              <Label>Refresh Results</Label>
              <Button 
                onClick={searchNumbers} 
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Search className="h-4 w-4 mr-2" />
                )}
                Search
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-6">
            <div className="flex items-center space-x-2">
              <Switch
                id="voice-enabled"
                checked={voiceEnabled}
                onCheckedChange={setVoiceEnabled}
              />
              <Label htmlFor="voice-enabled">Voice Calling Required</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="sms-enabled"
                checked={smsEnabled}
                onCheckedChange={setSmsEnabled}
              />
              <Label htmlFor="sms-enabled">SMS Required</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <Card>
        <CardHeader>
          <CardTitle>
            Available Numbers {loading && <Loader2 className="h-4 w-4 ml-2 animate-spin inline" />}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>Searching for available numbers...</p>
            </div>
          ) : availableNumbers.length === 0 ? (
            <div className="text-center py-8">
              <Phone className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-700 mb-2">No numbers found</h3>
              <p className="text-gray-500">Try adjusting your search criteria.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {availableNumbers.map((number, index) => (
                <Card key={index} className="border-2 hover:border-blue-200 transition-colors">
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div>
                        <div className="text-lg font-mono font-bold">
                          {formatPhoneNumber(number.phone_number)}
                        </div>
                        <div className="flex items-center text-sm text-gray-500 mt-1">
                          <MapPin className="h-4 w-4 mr-1" />
                          {number.region}
                        </div>
                      </div>

                      <div className="flex gap-2 flex-wrap">
                        {getCapabilityBadges(number.capabilities)}
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-sm font-medium">
                          <DollarSign className="h-4 w-4 mr-1" />
                          ${number.monthly_cost.toFixed(2)}/month
                        </div>
                        <Button 
                          size="sm"
                          onClick={() => handlePurchaseClick(number)}
                        >
                          <ShoppingCart className="h-4 w-4 mr-2" />
                          Buy
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Purchase Dialog */}
      <Dialog open={purchaseDialogOpen} onOpenChange={setPurchaseDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Purchase Phone Number</DialogTitle>
            <DialogDescription>
              Configure your new phone number before purchasing.
            </DialogDescription>
          </DialogHeader>

          {selectedNumber && (
            <div className="space-y-4 py-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-lg font-mono font-bold">
                  {formatPhoneNumber(selectedNumber.phone_number)}
                </div>
                <div className="flex items-center text-sm text-gray-500 mt-1">
                  <MapPin className="h-4 w-4 mr-1" />
                  {selectedNumber.region}
                </div>
                <div className="flex gap-2 mt-2">
                  {getCapabilityBadges(selectedNumber.capabilities)}
                </div>
              </div>

              <div>
                <Label htmlFor="friendly-name">Friendly Name</Label>
                <Input
                  id="friendly-name"
                  value={friendlyName}
                  onChange={(e) => setFriendlyName(e.target.value)}
                  placeholder="Enter a name for this number"
                />
              </div>

              <div>
                <Label htmlFor="assigned-agent">Assign to Agent (Optional)</Label>
                <Select value={assignedAgentId} onValueChange={setAssignedAgentId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select an agent" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No agent assigned</SelectItem>
                    {agents.map((agent) => (
                      <SelectItem key={agent.id} value={agent.id}>
                        {agent.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span>Monthly cost:</span>
                  <span className="font-bold">${selectedNumber.monthly_cost.toFixed(2)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Setup fee:</span>
                  <span className="font-bold">${(selectedNumber.setup_cost || 0).toFixed(2)}</span>
                </div>
                <hr className="my-2" />
                <div className="flex items-center justify-between text-lg font-bold">
                  <span>First month total:</span>
                  <span>${(selectedNumber.monthly_cost + (selectedNumber.setup_cost || 0)).toFixed(2)}</span>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setPurchaseDialogOpen(false)}
              disabled={purchasing}
            >
              Cancel
            </Button>
            <Button
              onClick={handlePurchase}
              disabled={purchasing || !friendlyName.trim()}
            >
              {purchasing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Purchasing...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Purchase Number
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}