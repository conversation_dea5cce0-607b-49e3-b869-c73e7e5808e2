'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Phone, 
  ArrowLeft, 
  Edit, 
  BarChart3, 
  PhoneCall, 
  MessageSquare,
  Clock,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Loader2,
  User,
  Calendar
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface PhoneNumber {
  id: string
  number: string
  friendly_name?: string
  country_code: string
  area_code?: string
  status: 'active' | 'inactive' | 'suspended'
  assigned_agent_id?: string
  monthly_cost: number
  created_at: string
  agents?: {
    id: string
    name: string
    description?: string
  }
}

interface CallLog {
  id: string
  start_time: string
  end_time?: string
  duration_seconds?: number
  caller_number: string
  status: string
  cost?: number
}

interface PhoneStats {
  overview: {
    total_calls: number
    answered_calls: number
    missed_calls: number
    failed_calls: number
    answer_rate: string
    average_duration: number
    total_cost: number
    last_call_date?: string
  }
  daily_metrics: any[]
  recent_calls: CallLog[]
  period: {
    start_date: string
    end_date: string
    days: number
  }
}

interface PhoneDetailsPageProps {
  params: {
    id: string
  }
}

export default function PhoneDetailsPage({ params }: PhoneDetailsPageProps) {
  const router = useRouter()
  const [phoneNumber, setPhoneNumber] = useState<PhoneNumber | null>(null)
  const [stats, setStats] = useState<PhoneStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchPhoneNumber()
    fetchStats()
  }, [params.id])

  const fetchPhoneNumber = async () => {
    try {
      const response = await fetch(`/api/phone-numbers/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setPhoneNumber(data)
      } else {
        if (response.status === 404) {
          router.push('/phone')
        }
      }
    } catch (error) {
      console.error('Error fetching phone number:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await fetch(`/api/phone-numbers/${params.id}/stats?period=30`)
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const formatPhoneNumber = (number: string): string => {
    if (number.startsWith('+1')) {
      const cleaned = number.substring(2)
      return `+1 (${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6)}`
    }
    return number
  }

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return '-'
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatCurrency = (amount?: number): string => {
    if (amount === undefined || amount === null) return '-'
    return `$${amount.toFixed(4)}`
  }

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (!phoneNumber) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-gray-500">Phone number not found</p>
            <Button asChild className="mt-4">
              <Link href="/phone">Back to Phone Numbers</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/phone">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Phone Numbers
          </Link>
        </Button>
        <div className="flex-1">
          <div className="flex items-center gap-3">
            <Phone className="h-6 w-6 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold">
                {formatPhoneNumber(phoneNumber.number)}
              </h1>
              <p className="text-gray-600">{phoneNumber.friendly_name}</p>
            </div>
            <Badge variant={phoneNumber.status === 'active' ? 'default' : 'secondary'}>
              {phoneNumber.status}
            </Badge>
          </div>
        </div>
        <Button asChild>
          <Link href={`/phone/${params.id}/edit`}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Link>
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <PhoneCall className="h-4 w-4" />
              Total Calls
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.overview.total_calls || 0}
            </div>
            <p className="text-sm text-gray-500">
              Last 30 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Answer Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.overview.answer_rate || 0}%
            </div>
            <div className="flex items-center text-sm text-gray-500">
              {parseFloat(stats?.overview.answer_rate || '0') > 80 ? (
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              {stats?.overview.answered_calls || 0} answered
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Avg Duration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(stats?.overview.average_duration)}
            </div>
            <p className="text-sm text-gray-500">
              Per call
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Call Costs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${(stats?.overview.total_cost || 0).toFixed(2)}
            </div>
            <p className="text-sm text-gray-500">
              Last 30 days
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Details and Recent Activity */}
      <Tabs defaultValue="details" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="calls">Recent Calls</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Phone Number Information</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Number</label>
                  <div className="font-mono text-lg">
                    {formatPhoneNumber(phoneNumber.number)}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Friendly Name</label>
                  <div>{phoneNumber.friendly_name || 'Not set'}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Country</label>
                  <div>{phoneNumber.country_code}</div>
                </div>
                {phoneNumber.area_code && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Area Code</label>
                    <div>{phoneNumber.area_code}</div>
                  </div>
                )}
              </div>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div>
                    <Badge variant={phoneNumber.status === 'active' ? 'default' : 'secondary'}>
                      {phoneNumber.status}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Assigned Agent</label>
                  <div className="flex items-center gap-2">
                    {phoneNumber.agents ? (
                      <>
                        <User className="h-4 w-4 text-green-500" />
                        <span>{phoneNumber.agents.name}</span>
                      </>
                    ) : (
                      <span className="text-gray-500">Not assigned</span>
                    )}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Monthly Cost</label>
                  <div className="text-lg font-medium">${phoneNumber.monthly_cost.toFixed(2)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Added</label>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    {formatDistanceToNow(new Date(phoneNumber.created_at), { addSuffix: true })}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calls" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Calls</CardTitle>
            </CardHeader>
            <CardContent>
              {stats?.recent_calls.length === 0 ? (
                <div className="text-center py-8">
                  <PhoneCall className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-700 mb-2">No calls yet</h3>
                  <p className="text-gray-500">Calls will appear here once you start receiving them.</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Caller</TableHead>
                      <TableHead>Time</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Cost</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {stats?.recent_calls.map((call) => (
                      <TableRow key={call.id}>
                        <TableCell className="font-medium">
                          {call.caller_number}
                        </TableCell>
                        <TableCell>
                          {formatDistanceToNow(new Date(call.start_time), { addSuffix: true })}
                        </TableCell>
                        <TableCell>
                          {formatDuration(call.duration_seconds)}
                        </TableCell>
                        <TableCell>
                          <Badge variant={
                            call.status === 'completed' ? 'default' :
                            call.status === 'missed' ? 'destructive' : 'secondary'
                          }>
                            {call.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {formatCurrency(call.cost)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Call Analytics (Last 30 Days)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {stats?.overview.answered_calls || 0}
                  </div>
                  <div className="text-sm text-gray-500">Answered</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {stats?.overview.missed_calls || 0}
                  </div>
                  <div className="text-sm text-gray-500">Missed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {stats?.overview.failed_calls || 0}
                  </div>
                  <div className="text-sm text-gray-500">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {stats?.overview.total_calls || 0}
                  </div>
                  <div className="text-sm text-gray-500">Total</div>
                </div>
              </div>
              
              {stats?.overview.last_call_date && (
                <div className="text-sm text-gray-500">
                  Last call: {formatDistanceToNow(new Date(stats.overview.last_call_date), { addSuffix: true })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}