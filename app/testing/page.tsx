'use client'

import { useEffect, useMemo, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { AppSidebar } from '@/components/app-sidebar'
import { AuthGuard } from '@/components/auth-guard'
import { SiteHeader } from '@/components/dashboard/site-header'
import { Agent } from '@/types/agent'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'

export default function TestingPage() {
  const router = useRouter()
  const [agents, setAgents] = useState<Agent[]>([])
  const [selectedAgentId, setSelectedAgentId] = useState<string | undefined>(undefined)
  const [loadingAgents, setLoadingAgents] = useState(true)
  const [running, setRunning] = useState(false)
  const [lines, setLines] = useState<string[]>([])
  const timerRef = useRef<NodeJS.Timer | null>(null)
  const transcriptRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    let aborted = false
    ;(async () => {
      setLoadingAgents(true)
      try {
        const { default: AgentsService } = await import('@/services/agents')
        const data = await AgentsService.list()
        if (!aborted) setAgents(data.agents || [])
      } catch (e: any) {
        if (!aborted) toast.error(e?.message || 'Fehler beim Laden der Agenten')
      } finally {
        if (!aborted) setLoadingAgents(false)
      }
    })()
    return () => { aborted = true }
  }, [])

  useEffect(() => {
    if (transcriptRef.current) transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight
  }, [lines])

  const selectedAgent = useMemo(() => agents.find(a => a.id === selectedAgentId), [agents, selectedAgentId])

  const start = () => {
    if (!selectedAgent) {
      toast.error('Bitte einen Agenten auswählen')
      return
    }
    if (running) return
    try {
      setRunning(true)
      toast.success(`Starte Test für ${selectedAgent.name}`)
      setLines([])
      const samples = [
        'Hallo, ich bin dein Voice-Agent. Wie kann ich helfen?',
        'Ich prüfe gerade die Informationen...',
        'Verstanden. Einen Moment bitte.',
        'Hier ist die Zusammenfassung deiner Anfrage.',
        'Gibt es noch etwas, wobei ich unterstützen kann?'
      ]
      let i = 0
      timerRef.current = setInterval(() => {
        setLines(prev => [...prev, samples[i % samples.length]])
        i += 1
      }, 700)
    } catch (e: any) {
      toast.error(e?.message || 'Mikrofonberechtigung verweigert')
      setRunning(false)
    }
  }

  const stop = () => {
    if (!running) return
    if (timerRef.current) clearInterval(timerRef.current)
    timerRef.current = null
    setRunning(false)
    toast.success('Test gestoppt')
  }

  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current)
    }
  }, [])

  return (
    <AuthGuard>
      <SidebarProvider
        style={{
          ['--sidebar-width' as any]: 'calc(var(--spacing) * 72)',
          ['--header-height' as any]: 'calc(var(--spacing) * 12)'
        }}
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader title="Agent Testing" showCreateButton={false} />
          <div className="flex flex-1 flex-col">
            <div className="container mx-auto p-6 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Test-Session</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                    <div className="md:col-span-2">
                      <Select
                        value={selectedAgentId}
                        onValueChange={(v) => setSelectedAgentId(v)}
                        disabled={loadingAgents || running}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={loadingAgents ? 'Lade Agenten…' : 'Agent auswählen'} />
                        </SelectTrigger>
                        <SelectContent>
                          {agents.map(a => (
                            <SelectItem key={a.id} value={a.id}>{a.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex gap-2">
                      <Button onClick={start} disabled={running || loadingAgents}>Start</Button>
                      <Button variant="outline" onClick={stop} disabled={!running}>Stop</Button>
                    </div>
                  </div>

                  <div className="border rounded-md h-80 overflow-auto p-3 bg-muted" ref={transcriptRef}>
                    {lines.length === 0 ? (
                      <div className="text-sm text-muted-foreground">Keine Transkripte. Starte die Session, um Streaming zu simulieren.</div>
                    ) : (
                      <div className="space-y-2">
                        {lines.map((l, idx) => (
                          <div key={idx} className="text-sm">{l}</div>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}
