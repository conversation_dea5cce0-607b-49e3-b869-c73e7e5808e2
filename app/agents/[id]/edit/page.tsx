'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { AuthGuard } from '@/components/auth-guard'
import { AppSidebar } from '@/components/app-sidebar'
import { SiteHeader } from '@/components/dashboard/site-header'
import { AgentForm } from '@/components/agents/agent-form'
import { AgentTabNavigation } from '@/components/agents/agent-tab-navigation'
import { Agent, CreateAgentRequest } from '@/types/agent'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'
import { toast } from 'sonner'

export default function EditAgentPage() {
  const params = useParams()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [agent, setAgent] = useState<Agent | null>(null)
  const [loadError, setLoadError] = useState<string | null>(null)

  useEffect(() => {
    let aborted = false
    async function load() {
      setInitialLoading(true)
      setLoadError(null)
      try {
        const { default: AgentsService } = await import('@/services/agents')
        const { agent } = await AgentsService.get(String(params.id))
        if (!aborted) {
          setAgent(agent)
        }
      } catch (e: any) {
        if (!aborted) {
          setLoadError(e?.message || 'Unbekannter Fehler beim Laden des Agenten')
        }
      } finally {
        if (!aborted) setInitialLoading(false)
      }
    }
    load()
    return () => { aborted = true }
  }, [params.id])

  const handleSubmit = async (formData: CreateAgentRequest) => {
    setLoading(true)
    try {
      const { default: AgentsService } = await import('@/services/agents')
      await AgentsService.update(String(params.id), {
        name: formData.name,
        description: formData.description || undefined,
        system_prompt: formData.system_prompt,
        voice: formData.voice,
        language: formData.language,
        status: formData.status,
      })
      toast.success('Agent erfolgreich aktualisiert')
      router.push(`/agents/${params.id}`)
    } catch (error: any) {
      console.error('Error updating agent:', error)
      toast.error(error?.message || 'Fehler beim Aktualisieren des Agenten')
      throw error
    } finally {
      setLoading(false)
    }
  }

  if (initialLoading) {
    return (
      <AuthGuard>
        <SidebarProvider
          style={
            {
              "--sidebar-width": "calc(var(--spacing) * 72)",
              "--header-height": "calc(var(--spacing) * 12)",
            } as React.CSSProperties
          }
        >
          <AppSidebar variant="inset" />
          <SidebarInset>
            <SiteHeader title="Edit Agent" />
            <div className="flex flex-1 flex-col">
              <div className="container mx-auto p-6">
                <div className="animate-pulse">
                  <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                  <div className="h-64 bg-gray-200 rounded"></div>
                </div>
                {loadError && (
                  <div className="mt-4 text-sm text-destructive">
                    {loadError}
                  </div>
                )}
              </div>
            </div>
          </SidebarInset>
        </SidebarProvider>
      </AuthGuard>
    )
  }

  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader title={`Edit ${agent?.name || 'Agent'}`} />
          <div className="flex flex-1 flex-col">
            {agent && (
              <>
                <AgentTabNavigation
                  activeTab="model"
                  onTabChange={() => {}}
                />
                <AgentForm
                  mode="edit"
                  initialData={agent}
                  onSubmit={handleSubmit}
                  loading={loading}
                />
              </>
            )}
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}
