'use client'

import { useState, useEffect } from 'react'
import { useSearchPara<PERSON>, useRouter } from 'next/navigation'
import { AppSidebar } from '@/components/app-sidebar'
import { AuthGuard } from '@/components/auth-guard'
import { AgentList } from '@/components/agents/agent-list'
import { SiteHeader } from '@/components/dashboard/site-header'
import { Agent } from '@/types/agent'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'
import AgentsService from '@/services/agents'
import { toast } from 'sonner'

export default function AgentsPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [agents, setAgents] = useState<Agent[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Optional Pagination aus Query übernehmen (Fallback: keine Pagination)
  const pageParam = searchParams.get('page')
  const pageSizeParam = searchParams.get('pageSize')

  async function refreshList() {
    setLoading(true)
    setError(null)
    try {
      const page = pageParam ? Number(pageParam) : undefined
      const pageSize = pageSizeParam ? Number(pageSizeParam) : undefined
      const data = await AgentsService.list({ page, pageSize })
      setAgents(data.agents ?? [])
    } catch (e: any) {
      setError(e?.message || 'Unbekannter Fehler beim Laden der Agenten')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    let aborted = false
    ;(async () => {
      setLoading(true)
      setError(null)
      try {
        const page = pageParam ? Number(pageParam) : undefined
        const pageSize = pageSizeParam ? Number(pageSizeParam) : undefined
        const data = await AgentsService.list({ page, pageSize })
        if (!aborted) {
          setAgents(data.agents ?? [])
        }
      } catch (e: any) {
        if (!aborted) {
          setError(e?.message || 'Unbekannter Fehler beim Laden der Agenten')
        }
      } finally {
        if (!aborted) setLoading(false)
      }
    })()
    return () => {
      aborted = true
    }
  }, [pageParam, pageSizeParam])

  async function handleDelete(id: string) {
    if (!confirm('Diesen Agent wirklich löschen?')) return
    // Optimistisches Update
    const prev = agents
    setAgents((curr) => curr.filter(a => a.id !== id))
    try {
      await AgentsService.remove(id)
      toast.success('Agent erfolgreich gelöscht')
    } catch (e: any) {
      // Rollback
      setAgents(prev)
      toast.error(e?.message || 'Fehler beim Löschen des Agenten')
    }
  }

  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader
            title="My Agents"
            showCreateButton={true}
            createButtonText="Create New Agent"
            createButtonHref="/agents/new"
            createButtonPosition="left"
          />
          <div className="flex flex-1 flex-col">
            <div className="container mx-auto p-6">
              <AgentList
                agents={agents}
                loading={loading}
                onDelete={handleDelete}
                onRefresh={refreshList}
              />
              {error && (
                <div className="mt-4 text-sm text-destructive">
                  {error}
                </div>
              )}
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}