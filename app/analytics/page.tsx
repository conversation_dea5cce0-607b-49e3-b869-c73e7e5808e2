'use client'

import { useState } from 'react'
import { BarChart3, FileText, TrendingUp, Users } from 'lucide-react'

import { AppSidebar } from '@/components/app-sidebar'
import { AuthGuard } from '@/components/auth-guard'
import { SiteHeader } from '@/components/dashboard/site-header'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'

// Import analytics components
import { PeriodicReportDashboard } from '@/components/analytics/PeriodicReportDashboard'
import { SuccessRateAnalysisDashboard } from '@/components/analytics/SuccessRateAnalysisDashboard'
import { ReportConfigurationModal } from '@/components/analytics/ReportConfigurationModal'

export default function AnalyticsPage() {
  const [showReportModal, setShowReportModal] = useState(false)
  
  // Default query for modal
  const defaultQuery = {
    timeRange: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate: new Date(),
      period: 'daily' as const
    },
    agentIds: []
  }
  
  const handleSaveReport = (query: any) => {
    console.log('Saving report with query:', query)
    setShowReportModal(false)
  }

  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader
            title="Analytics & Reports"
            showCreateButton={true}
            createButtonText="Generate Report"
            createButtonHref="/analytics/reports/new"
            createButtonPosition="left"
          />
          <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                <div className="px-4 lg:px-6">
                  <div className="space-y-6">
                    {/* Description */}
                    <div className="mb-6">
                      <p className="text-muted-foreground">
                        Analyze agent performance, track success rates, and generate comprehensive reports.
                      </p>
                    </div>

                    {/* Overview Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                          <CardTitle className="text-sm font-medium">
                            Total Conversations
                          </CardTitle>
                          <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">1,284</div>
                          <p className="text-xs text-muted-foreground">
                            +12% from last month
                          </p>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                          <CardTitle className="text-sm font-medium">
                            Success Rate
                          </CardTitle>
                          <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">87.3%</div>
                          <p className="text-xs text-muted-foreground">
                            +2.1% from last month
                          </p>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                          <CardTitle className="text-sm font-medium">
                            Avg. Duration
                          </CardTitle>
                          <BarChart3 className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">4m 32s</div>
                          <p className="text-xs text-muted-foreground">
                            -8s from last month
                          </p>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                          <CardTitle className="text-sm font-medium">
                            Reports Generated
                          </CardTitle>
                          <FileText className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">23</div>
                          <p className="text-xs text-muted-foreground">
                            +5 from last month
                          </p>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Analytics Tabs */}
                    <Tabs defaultValue="periodic-reports" className="space-y-4">
                      <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="periodic-reports">Periodic Reports</TabsTrigger>
                        <TabsTrigger value="success-rate">Success Rate</TabsTrigger>
                        <TabsTrigger value="conversation-duration">Duration Stats</TabsTrigger>
                        <TabsTrigger value="tool-usage">Tool Usage</TabsTrigger>
                      </TabsList>

                      <TabsContent value="periodic-reports" className="space-y-4">
                        <PeriodicReportDashboard />
                      </TabsContent>

                      <TabsContent value="success-rate" className="space-y-4">
                        <SuccessRateAnalysisDashboard />
                      </TabsContent>

                      <TabsContent value="conversation-duration" className="space-y-4">
                        <Card>
                          <CardHeader>
                            <CardTitle>Conversation Duration Analytics</CardTitle>
                            <CardDescription>
                              Analyze conversation durations and patterns over time.
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="text-center py-8">
                              <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                              <h3 className="text-lg font-semibold mb-2">Coming Soon</h3>
                              <p className="text-gray-600 mb-4">
                                Conversation duration analytics are being implemented.
                              </p>
                            </div>
                          </CardContent>
                        </Card>
                      </TabsContent>

                      <TabsContent value="tool-usage" className="space-y-4">
                        <Card>
                          <CardHeader>
                            <CardTitle>Tool Usage Analytics</CardTitle>
                            <CardDescription>
                              Track which tools your agents use most frequently and their effectiveness.
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="text-center py-8">
                              <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                              <h3 className="text-lg font-semibold mb-2">Coming Soon</h3>
                              <p className="text-gray-600 mb-4">
                                Tool usage analytics are being implemented.
                              </p>
                            </div>
                          </CardContent>
                        </Card>
                      </TabsContent>
                    </Tabs>

                    {/* Report Configuration Modal */}
                    {showReportModal && (
                      <ReportConfigurationModal
                        currentQuery={defaultQuery}
                        onSave={handleSaveReport}
                        onClose={() => setShowReportModal(false)}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}