import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import type { CreateKnowledgeBaseRequest } from '@/types/knowledge-base'

export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get knowledge bases with document count
    const { data: knowledgeBases, error } = await supabase
      .from('knowledge_bases')
      .select(`
        *,
        data_sources(count)
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching knowledge bases:', error)
      return NextResponse.json({ error: 'Failed to fetch knowledge bases' }, { status: 500 })
    }

    // Transform data to include document count
    const transformedData = knowledgeBases?.map(kb => ({
      ...kb,
      document_count: kb.data_sources?.[0]?.count || 0
    })) || []

    return NextResponse.json(transformedData)
  } catch (error) {
    console.error('Error in knowledge bases API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body: CreateKnowledgeBaseRequest = await request.json()

    // Validate required fields
    if (!body.name || body.name.trim().length === 0) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 })
    }

    if (body.name.length > 100) {
      return NextResponse.json({ error: 'Name must be less than 100 characters' }, { status: 400 })
    }

    // Create knowledge base
    const { data: knowledgeBase, error } = await supabase
      .from('knowledge_bases')
      .insert({
        user_id: user.id,
        name: body.name.trim(),
        description: body.description?.trim() || null,
        status: body.status || 'active'
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating knowledge base:', error)
      return NextResponse.json({ error: 'Failed to create knowledge base' }, { status: 500 })
    }

    return NextResponse.json(knowledgeBase, { status: 201 })
  } catch (error) {
    console.error('Error in knowledge bases POST API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}