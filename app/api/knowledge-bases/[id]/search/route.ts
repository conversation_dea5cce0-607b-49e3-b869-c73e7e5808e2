import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { ragService } from '@/services/ragService'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { 
      query, 
      similarity_threshold = 0.75, 
      max_chunks = 10,
      include_metadata = true,
      source_attribution = true 
    } = body

    if (!query || typeof query !== 'string') {
      return NextResponse.json({ error: 'Query is required and must be a string' }, { status: 400 })
    }

    // Verify knowledge base ownership
    const { data: knowledgeBase, error: kbError } = await supabase
      .from('knowledge_bases')
      .select('id, name')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (kbError) {
      if (kbError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Knowledge base not found' }, { status: 404 })
      }
      console.error('Error verifying knowledge base:', kbError)
      return NextResponse.json({ error: 'Failed to verify knowledge base' }, { status: 500 })
    }

    // Perform search
    const searchConfig = {
      similarity_threshold,
      max_chunks,
      include_metadata,
      source_attribution
    }

    const chunks = await ragService.searchRelevantChunks(
      query, 
      [params.id], 
      searchConfig
    )

    const context = await ragService.generateContext(
      chunks,
      include_metadata,
      source_attribution
    )

    // Calculate sources
    const sourceMap = new Map<string, any>()
    chunks.forEach(chunk => {
      const sourceId = chunk.metadata.source_id
      if (sourceMap.has(sourceId)) {
        sourceMap.get(sourceId)!.chunks_used++
      } else {
        sourceMap.set(sourceId, {
          id: sourceId,
          name: chunk.metadata.source_name,
          file_type: chunk.metadata.document_type,
          chunks_used: 1
        })
      }
    })

    const sources = Array.from(sourceMap.values())
    
    // Estimate total tokens
    const totalTokens = chunks.reduce((sum, chunk) => 
      sum + (chunk.metadata.token_count || 0), 0
    )

    return NextResponse.json({
      query,
      knowledge_base: {
        id: knowledgeBase.id,
        name: knowledgeBase.name
      },
      chunks,
      context,
      sources,
      total_tokens: totalTokens,
      search_config: searchConfig
    })
  } catch (error) {
    console.error('Error in knowledge base search API:', error)
    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}