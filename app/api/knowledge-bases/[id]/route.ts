import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import type { UpdateKnowledgeBaseRequest } from '@/types/knowledge-base'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get knowledge base with document count
    const { data: knowledgeBase, error } = await supabase
      .from('knowledge_bases')
      .select(`
        *,
        data_sources(count)
      `)
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Knowledge base not found' }, { status: 404 })
      }
      console.error('Error fetching knowledge base:', error)
      return NextResponse.json({ error: 'Failed to fetch knowledge base' }, { status: 500 })
    }

    // Add document count
    const transformedData = {
      ...knowledgeBase,
      document_count: knowledgeBase.data_sources?.[0]?.count || 0
    }

    return NextResponse.json(transformedData)
  } catch (error) {
    console.error('Error in knowledge base GET API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body: UpdateKnowledgeBaseRequest = await request.json()

    // Validate fields if provided
    if (body.name !== undefined) {
      if (!body.name || body.name.trim().length === 0) {
        return NextResponse.json({ error: 'Name cannot be empty' }, { status: 400 })
      }
      if (body.name.length > 100) {
        return NextResponse.json({ error: 'Name must be less than 100 characters' }, { status: 400 })
      }
    }

    // Build update object
    const updateData: Record<string, unknown> = {
      updated_at: new Date().toISOString()
    }

    if (body.name !== undefined) updateData.name = body.name.trim()
    if (body.description !== undefined) updateData.description = body.description?.trim() || null
    if (body.status !== undefined) updateData.status = body.status

    // Update knowledge base
    const { data: knowledgeBase, error } = await supabase
      .from('knowledge_bases')
      .update(updateData)
      .eq('id', params.id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Knowledge base not found' }, { status: 404 })
      }
      console.error('Error updating knowledge base:', error)
      return NextResponse.json({ error: 'Failed to update knowledge base' }, { status: 500 })
    }

    return NextResponse.json(knowledgeBase)
  } catch (error) {
    console.error('Error in knowledge base PUT API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if knowledge base exists and belongs to user
    const { error: checkError } = await supabase
      .from('knowledge_bases')
      .select('id, name')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Knowledge base not found' }, { status: 404 })
      }
      console.error('Error checking knowledge base:', checkError)
      return NextResponse.json({ error: 'Failed to delete knowledge base' }, { status: 500 })
    }

    // Delete knowledge base (cascade will handle data_sources and document_chunks)
    const { error: deleteError } = await supabase
      .from('knowledge_bases')
      .delete()
      .eq('id', params.id)
      .eq('user_id', user.id)

    if (deleteError) {
      console.error('Error deleting knowledge base:', deleteError)
      return NextResponse.json({ error: 'Failed to delete knowledge base' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Knowledge base deleted successfully' })
  } catch (error) {
    console.error('Error in knowledge base DELETE API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}