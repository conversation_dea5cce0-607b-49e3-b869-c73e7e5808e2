import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { documentProcessor } from '@/lib/document-processor'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify knowledge base ownership
    const { data: knowledgeBase, error: kbError } = await supabase
      .from('knowledge_bases')
      .select('id')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (kbError) {
      if (kbError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Knowledge base not found' }, { status: 404 })
      }
      console.error('Error verifying knowledge base:', kbError)
      return NextResponse.json({ error: 'Failed to verify knowledge base' }, { status: 500 })
    }

    // Get data sources
    const { data: dataSources, error } = await supabase
      .from('data_sources')
      .select('*')
      .eq('knowledge_base_id', params.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching data sources:', error)
      return NextResponse.json({ error: 'Failed to fetch data sources' }, { status: 500 })
    }

    return NextResponse.json(dataSources || [])
  } catch (error) {
    console.error('Error in data sources API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify knowledge base ownership
    const { data: knowledgeBase, error: kbError } = await supabase
      .from('knowledge_bases')
      .select('id, name')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (kbError) {
      if (kbError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Knowledge base not found' }, { status: 404 })
      }
      console.error('Error verifying knowledge base:', kbError)
      return NextResponse.json({ error: 'Failed to verify knowledge base' }, { status: 500 })
    }

    // Parse multipart form data
    const formData = await request.formData()
    const file = formData.get('file') as File | null
    const name = formData.get('name') as string | null

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'text/markdown']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ 
        error: 'Unsupported file type. Only PDF, DOCX, TXT, and MD files are allowed.' 
      }, { status: 400 })
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({ 
        error: 'File too large. Maximum size is 10MB.' 
      }, { status: 400 })
    }

    // Determine file type
    const fileType = getFileType(file.type)
    const displayName = name || file.name

    try {
      // Upload file to Supabase Storage
      const fileBuffer = Buffer.from(await file.arrayBuffer())
      const fileName = `${Date.now()}-${file.name}`
      const filePath = `knowledge-bases/${params.id}/${fileName}`

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('documents')
        .upload(filePath, fileBuffer, {
          contentType: file.type,
          cacheControl: '3600',
        })

      if (uploadError) {
        console.error('Error uploading file to storage:', uploadError)
        return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 })
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('documents')
        .getPublicUrl(filePath)

      // Create data source record
      const { data: dataSource, error: createError } = await supabase
        .from('data_sources')
        .insert({
          knowledge_base_id: params.id,
          name: displayName,
          original_filename: file.name,
          file_type: fileType,
          file_size: file.size,
          file_url: publicUrl,
          status: 'uploading',
          metadata: {}
        })
        .select()
        .single()

      if (createError) {
        console.error('Error creating data source:', createError)
        // Clean up uploaded file
        await supabase.storage.from('documents').remove([filePath])
        return NextResponse.json({ error: 'Failed to create data source' }, { status: 500 })
      }

      // Start background processing
      processDocumentAsync(dataSource.id, fileBuffer, fileType, file.name)
        .catch(error => {
          console.error('Error in background processing:', error)
        })

      return NextResponse.json(dataSource, { status: 201 })
    } catch (error) {
      console.error('Error processing file:', error)
      return NextResponse.json({ error: 'Failed to process file' }, { status: 500 })
    }
  } catch (error) {
    console.error('Error in upload API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

function getFileType(mimeType: string): string {
  switch (mimeType) {
    case 'application/pdf':
      return 'pdf'
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return 'docx'
    case 'text/plain':
      return 'txt'
    case 'text/markdown':
      return 'md'
    default:
      return 'unknown'
  }
}

async function processDocumentAsync(dataSourceId: string, fileBuffer: Buffer, fileType: string, originalFilename: string) {
  const cookieStore = await cookies()
  const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

  try {
    // Update status to processing
    await supabase
      .from('data_sources')
      .update({ status: 'processing' })
      .eq('id', dataSourceId)

    // Extract text from document
    const extractedContent = await documentProcessor.extractText(fileBuffer, fileType, originalFilename)
    
    // Generate chunks
    const chunks = await documentProcessor.generateChunks(extractedContent.text)
    
    // Generate embeddings
    const chunkTexts = chunks.map(chunk => chunk.content)
    const embeddings = await documentProcessor.generateEmbeddings(chunkTexts)

    // Save chunks to database
    const chunksToInsert = chunks.map((chunk, index) => ({
      data_source_id: dataSourceId,
      content: chunk.content,
      embedding: `[${embeddings[index].join(',')}]`, // PostgreSQL vector format
      metadata: chunk.metadata
    }))

    const { error: chunksError } = await supabase
      .from('document_chunks')
      .insert(chunksToInsert)

    if (chunksError) {
      throw new Error(`Failed to save chunks: ${chunksError.message}`)
    }

    // Update data source with final status and metadata
    await supabase
      .from('data_sources')
      .update({
        status: 'ready',
        metadata: extractedContent.metadata,
        chunk_count: chunks.length
      })
      .eq('id', dataSourceId)

  } catch (error) {
    console.error('Error processing document:', error)
    
    // Update status to error
    await supabase
      .from('data_sources')
      .update({ 
        status: 'error',
        error_message: error instanceof Error ? error.message : 'Unknown error'
      })
      .eq('id', dataSourceId)
  }
}