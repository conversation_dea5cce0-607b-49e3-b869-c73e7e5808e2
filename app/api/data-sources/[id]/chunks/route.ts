import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Verify data source ownership through knowledge base
    const { data: dataSource, error: checkError } = await supabase
      .from('data_sources')
      .select(`
        id,
        knowledge_bases!inner(user_id)
      `)
      .eq('id', params.id)
      .single()

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Data source not found' }, { status: 404 })
      }
      console.error('Error checking data source:', checkError)
      return NextResponse.json({ error: 'Failed to fetch chunks' }, { status: 500 })
    }

    // Check ownership
    if ((dataSource as any).knowledge_bases.user_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    // Get chunks
    const { data: chunks, error } = await supabase
      .from('document_chunks')
      .select('id, content, metadata')
      .eq('data_source_id', params.id)
      .order('metadata->chunk_index', { ascending: true })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching chunks:', error)
      return NextResponse.json({ error: 'Failed to fetch chunks' }, { status: 500 })
    }

    return NextResponse.json(chunks || [])
  } catch (error) {
    console.error('Error in chunks API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}