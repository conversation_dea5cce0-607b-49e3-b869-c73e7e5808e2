import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get data source with knowledge base info
    const { data: dataSource, error } = await supabase
      .from('data_sources')
      .select(`
        *,
        knowledge_bases!inner(id, name, user_id)
      `)
      .eq('id', params.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Data source not found' }, { status: 404 })
      }
      console.error('Error fetching data source:', error)
      return NextResponse.json({ error: 'Failed to fetch data source' }, { status: 500 })
    }

    // Check ownership
    if ((dataSource as any).knowledge_bases.user_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    return NextResponse.json(dataSource)
  } catch (error) {
    console.error('Error in data source GET API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify data source ownership through knowledge base
    const { data: dataSource, error: checkError } = await supabase
      .from('data_sources')
      .select(`
        id,
        knowledge_bases!inner(user_id)
      `)
      .eq('id', params.id)
      .single()

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Data source not found' }, { status: 404 })
      }
      console.error('Error checking data source:', checkError)
      return NextResponse.json({ error: 'Failed to delete data source' }, { status: 500 })
    }

    // Check ownership
    if ((dataSource as any).knowledge_bases.user_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    // Delete data source (cascade will handle document_chunks)
    const { error: deleteError } = await supabase
      .from('data_sources')
      .delete()
      .eq('id', params.id)

    if (deleteError) {
      console.error('Error deleting data source:', deleteError)
      return NextResponse.json({ error: 'Failed to delete data source' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Data source deleted successfully' })
  } catch (error) {
    console.error('Error in data source DELETE API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}