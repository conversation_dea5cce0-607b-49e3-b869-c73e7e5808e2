import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { z } from 'zod'

/**
 * Zod Schemas
 */
const LanguageSchema = z.enum(['de-DE', 'en-US'])
const StatusSchema = z.enum(['active', 'inactive'])

const CreateAgentSchema = z.object({
  name: z.string().min(1, 'Name ist erforderlich').max(120, 'Name ist zu lang'),
  description: z.string().max(2000, 'Beschreibung ist zu lang').optional().nullable(),
  system_prompt: z.string().min(50, 'System Prompt muss mindestens 50 Zeichen haben').max(20000, 'System Prompt ist zu lang'),
  voice: z.string().min(1, 'Stimme ist erforderlich').max(120, 'Stimme ist zu lang'),
  language: LanguageSchema,
  status: StatusSchema.optional()
})

/**
 * Utils
 */
function jsonError(status: number, error: string, message: string, extra?: Record<string, any>) {
  return NextResponse.json({ error, message, ...(extra ?? {}) }, { status })
}

function parsePagination(url: string) {
  const { searchParams } = new URL(url)
  const pageRaw = searchParams.get('page') ?? '1'
  const sizeRaw = searchParams.get('pageSize') ?? '0'
  const page = Number.isFinite(Number(pageRaw)) ? Math.max(1, Number(pageRaw)) : 1
  const pageSize = Number.isFinite(Number(sizeRaw)) ? Math.min(100, Math.max(0, Number(sizeRaw))) : 0 // hard cap 100
  const from = pageSize > 0 ? (page - 1) * pageSize : undefined
  const to = pageSize > 0 ? from! + pageSize - 1 : undefined
  return { page, pageSize, from, to }
}

/**
 * GET /api/agents
 * Optional: page, pageSize (Default: komplette Liste)
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // Session/User ermitteln
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    if (sessionError || !session?.user) {
      return jsonError(401, 'Unauthorized', 'Benutzer nicht authentifiziert')
    }

    const { page, pageSize, from, to } = parsePagination(request.url)

    let query = supabase
      .from('agents')
      .select('*', { count: 'exact' })
      .eq('user_id', session.user.id) // expliziter Owner-Filter zusätzlich zu RLS
      .order('created_at', { ascending: false })

    if (pageSize > 0) {
      query = query.range(from!, to!)
    }

    const { data, error, count } = await query
    if (error) {
      console.error('Database error:', error)
      return jsonError(500, 'Database Error', 'Fehler beim Laden der Agenten')
    }

    return NextResponse.json({
      agents: data ?? [],
      total: count ?? (data?.length ?? 0),
      page: pageSize > 0 ? page : null,
      pageSize: pageSize > 0 ? pageSize : null
    })
  } catch (error) {
    console.error('API error:', error)
    return jsonError(500, 'Internal Server Error', 'Ein unerwarteter Fehler ist aufgetreten')
  }
}

/**
 * POST /api/agents
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // Session/User ermitteln
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    if (sessionError || !session?.user) {
      return jsonError(401, 'Unauthorized', 'Benutzer nicht authentifiziert')
    }

    const json = await request.json()

    // Sicherheit: clientseitig mitgeschickte user_id strikt verwerfen
    if (typeof json?.user_id !== 'undefined') {
      delete json.user_id
    }

    const parseResult = CreateAgentSchema.safeParse(json)
    if (!parseResult.success) {
      const issues = parseResult.error.issues.map(i => ({ path: i.path.join('.'), message: i.message }))
      return NextResponse.json({ error: 'Validation Error', issues }, { status: 400 })
    }

    const payload = parseResult.data

    const { data: inserted, error } = await supabase
      .from('agents')
      .insert({
        user_id: session.user.id,
        name: payload.name.trim(),
        description: payload.description?.trim() || null,
        system_prompt: payload.system_prompt.trim(),
        voice: payload.voice.trim(),
        language: payload.language,
        status: payload.status ?? 'inactive'
      })
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      return jsonError(500, 'Database Error', 'Fehler beim Erstellen des Agenten')
    }

    return NextResponse.json({ agent: inserted }, { status: 201 })
  } catch (error) {
    console.error('API error:', error)
    return jsonError(500, 'Internal Server Error', 'Ein unerwarteter Fehler ist aufgetreten')
  }
}
