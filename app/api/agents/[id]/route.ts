import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createRouteHandlerClient, type SupabaseClient } from '@supabase/auth-helpers-nextjs'
import { z } from 'zod'

/**
 * Shared Zod Schemas
 */
const LanguageSchema = z.enum(['de-DE', 'en-US'])
const StatusSchema = z.enum(['active', 'inactive'])

const UpdateAgentSchema = z.object({
  name: z.string().min(1, 'Name ist erforderlich').max(120, 'Name ist zu lang').optional(),
  description: z.string().max(2000, 'Beschreibung ist zu lang').optional().nullable(),
  system_prompt: z.string().min(50, 'System Prompt muss mindestens 50 Zeichen haben').max(20000, 'System Prompt ist zu lang').optional(),
  voice: z.string().min(1, 'Stimme ist erforderlich').max(120, 'Stimme ist zu lang').optional(),
  language: LanguageSchema.optional(),
  status: StatusSchema.optional()
}).refine((data) => Object.keys(data).length > 0, {
  message: 'Mindestens ein Feld muss aktualisiert werden'
})

/**
 * Utils
 */
function jsonError(status: number, error: string, message: string, extra?: Record<string, any>) {
  return NextResponse.json({ error, message, ...(extra ?? {}) }, { status })
}

function isUUID(id: string) {
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
}

/**
 * Helper: prüft, ob Agent dem Nutzer gehört (zusätzlich zu RLS)
 */
async function ensureOwnership(supabase: SupabaseClient, agentId: string, userId: string) {
  const { data, error } = await supabase
    .from('agents')
    .select('id, user_id')
    .eq('id', agentId)
    .single()

  if (error || !data) {
    return { ok: false, status: 404 as const, body: { error: 'Not Found', message: 'Agent nicht gefunden' } }
  }

  if (data.user_id !== userId) {
    return { ok: false, status: 403 as const, body: { error: 'Forbidden', message: 'Kein Zugriff auf diesen Agent' } }
  }

  return { ok: true as const }
}

/**
 * GET /api/agents/[id]
 */
export async function GET(_request: NextRequest, context: { params: { id: string } }) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()

    if (sessionError || !session?.user) {
      return jsonError(401, 'Unauthorized', 'Benutzer nicht authentifiziert')
    }

    const agentId = context.params.id
    if (!isUUID(agentId)) {
      return jsonError(400, 'Bad Request', 'Ungültige Agent-ID')
    }

    const ownership = await ensureOwnership(supabase, agentId, session.user.id)
    if (!ownership.ok) {
      return NextResponse.json(ownership.body, { status: ownership.status })
    }

    const { data, error } = await supabase
      .from('agents')
      .select('*')
      .eq('id', agentId)
      .single()

    if (error || !data) {
      return jsonError(404, 'Not Found', 'Agent nicht gefunden')
    }

    return NextResponse.json({ agent: data })
  } catch (error) {
    console.error('GET /api/agents/[id] error:', error)
    return jsonError(500, 'Internal Server Error', 'Ein unerwarteter Fehler ist aufgetreten')
  }
}

/**
 * PUT /api/agents/[id]
 */
export async function PUT(request: NextRequest, context: { params: { id: string } }) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()

    if (sessionError || !session?.user) {
      return jsonError(401, 'Unauthorized', 'Benutzer nicht authentifiziert')
    }

    const agentId = context.params.id
    if (!isUUID(agentId)) {
      return jsonError(400, 'Bad Request', 'Ungültige Agent-ID')
    }

    const ownership = await ensureOwnership(supabase, agentId, session.user.id)
    if (!ownership.ok) {
      return NextResponse.json(ownership.body, { status: ownership.status })
    }

    const json = await request.json()

    // Sicherheit: serverseitig user_id Mutationen unterbinden
    if (typeof json?.user_id !== 'undefined') {
      delete json.user_id
    }

    const parseResult = UpdateAgentSchema.safeParse(json)
    if (!parseResult.success) {
      const issues = parseResult.error.issues.map(i => ({ path: i.path.join('.'), message: i.message }))
      return NextResponse.json({ error: 'Validation Error', issues }, { status: 400 })
    }

    // Trim Felder, wo sinnvoll
    const payload = parseResult.data
    const updateValues: Record<string, any> = {}
    if (payload.name !== undefined) updateValues.name = payload.name.trim()
    if (payload.description !== undefined) updateValues.description = payload.description?.trim() || null
    if (payload.system_prompt !== undefined) updateValues.system_prompt = payload.system_prompt.trim()
    if (payload.voice !== undefined) updateValues.voice = payload.voice.trim()
    if (payload.language !== undefined) updateValues.language = payload.language
    if (payload.status !== undefined) updateValues.status = payload.status

    const { data, error } = await supabase
      .from('agents')
      .update(updateValues)
      .eq('id', agentId)
      .select()
      .single()

    if (error || !data) {
      console.error('Update error:', error)
      return jsonError(500, 'Database Error', 'Fehler beim Aktualisieren des Agenten')
    }

    return NextResponse.json({ agent: data })
  } catch (error) {
    console.error('PUT /api/agents/[id] error:', error)
    return jsonError(500, 'Internal Server Error', 'Ein unerwarteter Fehler ist aufgetreten')
  }
}

/**
 * DELETE /api/agents/[id]
 */
export async function DELETE(_request: NextRequest, context: { params: { id: string } }) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()

    if (sessionError || !session?.user) {
      return jsonError(401, 'Unauthorized', 'Benutzer nicht authentifiziert')
    }

    const agentId = context.params.id
    if (!isUUID(agentId)) {
      return jsonError(400, 'Bad Request', 'Ungültige Agent-ID')
    }

    const ownership = await ensureOwnership(supabase, agentId, session.user.id)
    if (!ownership.ok) {
      return NextResponse.json(ownership.body, { status: ownership.status })
    }

    const { error } = await supabase
      .from('agents')
      .delete()
      .eq('id', agentId)

    if (error) {
      console.error('Delete error:', error)
      return jsonError(500, 'Database Error', 'Fehler beim Löschen des Agenten')
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('DELETE /api/agents/[id] error:', error)
    return jsonError(500, 'Internal Server Error', 'Ein unerwarteter Fehler ist aufgetreten')
  }
}