import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string, kbId: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify agent ownership and link exists
    const { data: link, error: checkError } = await supabase
      .from('agent_knowledge_bases')
      .select(`
        id,
        agents!inner(user_id)
      `)
      .eq('agent_id', params.id)
      .eq('knowledge_base_id', params.kbId)
      .single()

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Link not found' }, { status: 404 })
      }
      console.error('Error checking link:', checkError)
      return NextResponse.json({ error: 'Failed to remove link' }, { status: 500 })
    }

    // Check ownership
    if ((link as any).agents.user_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    // Remove the link
    const { error: deleteError } = await supabase
      .from('agent_knowledge_bases')
      .delete()
      .eq('agent_id', params.id)
      .eq('knowledge_base_id', params.kbId)

    if (deleteError) {
      console.error('Error deleting link:', deleteError)
      return NextResponse.json({ error: 'Failed to remove link' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Knowledge base unlinked successfully' })
  } catch (error) {
    console.error('Error in agent knowledge base unlinking API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string, kbId: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { priority } = body

    if (typeof priority !== 'number') {
      return NextResponse.json({ error: 'Priority must be a number' }, { status: 400 })
    }

    // Verify agent ownership and link exists
    const { data: link, error: checkError } = await supabase
      .from('agent_knowledge_bases')
      .select(`
        id,
        agents!inner(user_id)
      `)
      .eq('agent_id', params.id)
      .eq('knowledge_base_id', params.kbId)
      .single()

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Link not found' }, { status: 404 })
      }
      console.error('Error checking link:', checkError)
      return NextResponse.json({ error: 'Failed to update link' }, { status: 500 })
    }

    // Check ownership
    if ((link as any).agents.user_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    // Update the priority
    const { data: updatedLink, error: updateError } = await supabase
      .from('agent_knowledge_bases')
      .update({ priority })
      .eq('agent_id', params.id)
      .eq('knowledge_base_id', params.kbId)
      .select(`
        id,
        priority,
        created_at,
        knowledge_bases!inner(
          id,
          name,
          description,
          status,
          created_at,
          updated_at
        )
      `)
      .single()

    if (updateError) {
      console.error('Error updating link:', updateError)
      return NextResponse.json({ error: 'Failed to update link' }, { status: 500 })
    }

    return NextResponse.json(updatedLink)
  } catch (error) {
    console.error('Error in agent knowledge base link update API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}