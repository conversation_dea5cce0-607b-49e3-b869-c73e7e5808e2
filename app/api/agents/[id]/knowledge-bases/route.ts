import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify agent ownership
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('id')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (agentError) {
      if (agentError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Agent not found' }, { status: 404 })
      }
      console.error('Error verifying agent:', agentError)
      return NextResponse.json({ error: 'Failed to verify agent' }, { status: 500 })
    }

    // Get linked knowledge bases
    const { data: linkedKBs, error } = await supabase
      .from('agent_knowledge_bases')
      .select(`
        id,
        priority,
        created_at,
        knowledge_bases!inner(
          id,
          name,
          description,
          status,
          created_at,
          updated_at
        )
      `)
      .eq('agent_id', params.id)
      .order('priority', { ascending: true })

    if (error) {
      console.error('Error fetching linked knowledge bases:', error)
      return NextResponse.json({ error: 'Failed to fetch knowledge bases' }, { status: 500 })
    }

    return NextResponse.json(linkedKBs || [])
  } catch (error) {
    console.error('Error in agent knowledge bases API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { knowledge_base_id, priority = 1 } = body

    if (!knowledge_base_id) {
      return NextResponse.json({ error: 'knowledge_base_id is required' }, { status: 400 })
    }

    // Verify agent ownership
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('id')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (agentError) {
      if (agentError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Agent not found' }, { status: 404 })
      }
      console.error('Error verifying agent:', agentError)
      return NextResponse.json({ error: 'Failed to verify agent' }, { status: 500 })
    }

    // Verify knowledge base ownership
    const { data: knowledgeBase, error: kbError } = await supabase
      .from('knowledge_bases')
      .select('id')
      .eq('id', knowledge_base_id)
      .eq('user_id', user.id)
      .single()

    if (kbError) {
      if (kbError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Knowledge base not found' }, { status: 404 })
      }
      console.error('Error verifying knowledge base:', kbError)
      return NextResponse.json({ error: 'Failed to verify knowledge base' }, { status: 500 })
    }

    // Create the link
    const { data: link, error: linkError } = await supabase
      .from('agent_knowledge_bases')
      .insert({
        agent_id: params.id,
        knowledge_base_id,
        priority
      })
      .select(`
        id,
        priority,
        created_at,
        knowledge_bases!inner(
          id,
          name,
          description,
          status,
          created_at,
          updated_at
        )
      `)
      .single()

    if (linkError) {
      if (linkError.code === '23505') { // Unique constraint violation
        return NextResponse.json({ error: 'Knowledge base already linked to this agent' }, { status: 409 })
      }
      console.error('Error creating link:', linkError)
      return NextResponse.json({ error: 'Failed to link knowledge base' }, { status: 500 })
    }

    return NextResponse.json(link, { status: 201 })
  } catch (error) {
    console.error('Error in agent knowledge base linking API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}