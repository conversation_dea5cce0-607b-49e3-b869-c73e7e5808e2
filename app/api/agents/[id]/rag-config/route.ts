import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify agent ownership
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('id, rag_enabled')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (agentError) {
      if (agentError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Agent not found' }, { status: 404 })
      }
      console.error('Error verifying agent:', agentError)
      return NextResponse.json({ error: 'Failed to verify agent' }, { status: 500 })
    }

    // Get RAG config (create default if doesn't exist)
    let { data: config, error: configError } = await supabase
      .from('agent_rag_configs')
      .select('*')
      .eq('agent_id', params.id)
      .single()

    if (configError && configError.code === 'PGRST116') {
      // Create default config
      const { data: defaultConfig, error: createError } = await supabase
        .from('agent_rag_configs')
        .insert({
          agent_id: params.id,
          enabled: false,
          similarity_threshold: 0.75,
          max_chunks: 5,
          chunk_overlap: true,
          include_metadata: true,
          source_attribution: true
        })
        .select('*')
        .single()

      if (createError) {
        console.error('Error creating default RAG config:', createError)
        return NextResponse.json({ error: 'Failed to create RAG config' }, { status: 500 })
      }

      config = defaultConfig
    } else if (configError) {
      console.error('Error fetching RAG config:', configError)
      return NextResponse.json({ error: 'Failed to fetch RAG config' }, { status: 500 })
    }

    return NextResponse.json({
      ...config,
      agent_rag_enabled: agent.rag_enabled
    })
  } catch (error) {
    console.error('Error in RAG config GET API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const {
      enabled,
      similarity_threshold,
      max_chunks,
      chunk_overlap,
      include_metadata,
      source_attribution
    } = body

    // Validation
    if (similarity_threshold !== undefined && (similarity_threshold < 0 || similarity_threshold > 1)) {
      return NextResponse.json({ error: 'Similarity threshold must be between 0 and 1' }, { status: 400 })
    }
    
    if (max_chunks !== undefined && (max_chunks < 1 || max_chunks > 20)) {
      return NextResponse.json({ error: 'Max chunks must be between 1 and 20' }, { status: 400 })
    }

    // Verify agent ownership
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('id')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (agentError) {
      if (agentError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Agent not found' }, { status: 404 })
      }
      console.error('Error verifying agent:', agentError)
      return NextResponse.json({ error: 'Failed to verify agent' }, { status: 500 })
    }

    // Update RAG config (upsert)
    const { data: config, error: updateError } = await supabase
      .from('agent_rag_configs')
      .upsert({
        agent_id: params.id,
        enabled: enabled !== undefined ? enabled : true,
        similarity_threshold: similarity_threshold !== undefined ? similarity_threshold : 0.75,
        max_chunks: max_chunks !== undefined ? max_chunks : 5,
        chunk_overlap: chunk_overlap !== undefined ? chunk_overlap : true,
        include_metadata: include_metadata !== undefined ? include_metadata : true,
        source_attribution: source_attribution !== undefined ? source_attribution : true
      })
      .select('*')
      .single()

    if (updateError) {
      console.error('Error updating RAG config:', updateError)
      return NextResponse.json({ error: 'Failed to update RAG config' }, { status: 500 })
    }

    // Update agent rag_enabled flag
    if (enabled !== undefined) {
      const { error: agentUpdateError } = await supabase
        .from('agents')
        .update({ rag_enabled: enabled })
        .eq('id', params.id)

      if (agentUpdateError) {
        console.error('Error updating agent rag_enabled flag:', agentUpdateError)
      }
    }

    return NextResponse.json(config)
  } catch (error) {
    console.error('Error in RAG config PUT API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}