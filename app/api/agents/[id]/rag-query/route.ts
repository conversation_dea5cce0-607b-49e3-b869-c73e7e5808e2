import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { ragService } from '@/services/ragService'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { query } = body

    if (!query || typeof query !== 'string') {
      return NextResponse.json({ error: 'Query is required and must be a string' }, { status: 400 })
    }

    // Verify agent ownership
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('id, rag_enabled')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (agentError) {
      if (agentError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Agent not found' }, { status: 404 })
      }
      console.error('Error verifying agent:', agentError)
      return NextResponse.json({ error: 'Failed to verify agent' }, { status: 500 })
    }

    if (!agent.rag_enabled) {
      return NextResponse.json({ 
        error: 'RAG is not enabled for this agent' 
      }, { status: 400 })
    }

    // Perform RAG query
    const ragResponse = await ragService.performRAGQuery(params.id, query)

    return NextResponse.json(ragResponse)
  } catch (error) {
    console.error('Error in RAG query API:', error)
    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}