// Twilio Incoming Call Webhook Handler
// Epic 6 Story 6.3: Anruf-Handling und -protokollierung

import { NextRequest, NextResponse } from 'next/server'
import { CallHandlingService } from '@/services/callHandlingService'
import type { TwilioIncomingCallWebhook } from '@/types/call-handling'

const callHandlingService = new CallHandlingService()

// Mock TwiML response for development
class TwiMLResponse {
  private content: string[] = []

  say(options: { voice?: string; language?: string }, text: string): void {
    this.content.push(`<Say voice="${options.voice || 'alice'}" language="${options.language || 'en-US'}">${text}</Say>`)
  }

  record(options: {
    action: string
    transcribe?: boolean
    transcribeCallback?: string
  }): void {
    this.content.push(`<Record action="${options.action}" transcribe="${options.transcribe || false}" transcribeCallback="${options.transcribeCallback || ''}" />`)
  }

  stream(options: {
    name: string
    url: string
    track?: string
  }): void {
    this.content.push(`<Stream name="${options.name}" url="${options.url}" track="${options.track || 'both'}" />`)
  }

  start(): this {
    this.content.push('<Start>')
    return this
  }

  toString(): string {
    return `<?xml version="1.0" encoding="UTF-8"?><Response>${this.content.join('')}</Response>`
  }
}

export async function POST(request: NextRequest) {
  try {
    // Parse Twilio webhook data
    const body = await request.formData()
    const webhookData: TwilioIncomingCallWebhook = {
      CallSid: body.get('CallSid') as string,
      AccountSid: body.get('AccountSid') as string,
      From: body.get('From') as string,
      To: body.get('To') as string,
      CallStatus: body.get('CallStatus') as string,
      CallerName: body.get('CallerName') as string || undefined,
      CallerCity: body.get('CallerCity') as string || undefined,
      CallerState: body.get('CallerState') as string || undefined,
      CallerCountry: body.get('CallerCountry') as string || undefined,
      CallerZip: body.get('CallerZip') as string || undefined
    }

    console.log('Incoming call webhook:', webhookData)

    // Find the phone number record
    const { createClient } = await import('@/lib/supabaseServer')
    const supabase = createClient()
    
    const { data: phoneNumber, error: phoneError } = await supabase
      .from('phone_numbers')
      .select(`
        *,
        agents(id, name, description, system_prompt, voice, language)
      `)
      .eq('number', webhookData.To)
      .single()

    if (phoneError || !phoneNumber) {
      console.error('Phone number not found:', webhookData.To)
      return new NextResponse(createErrorTwiML('Phone number not configured'), {
        headers: { 'Content-Type': 'text/xml' }
      })
    }

    if (!phoneNumber.assigned_agent_id || !phoneNumber.agents) {
      console.error('No agent assigned to phone number:', webhookData.To)
      return new NextResponse(createErrorTwiML('No agent assigned to this number'), {
        headers: { 'Content-Type': 'text/xml' }
      })
    }

    // Create call log
    try {
      const callLog = await callHandlingService.createCallLog(phoneNumber.user_id, {
        phone_number_id: phoneNumber.id,
        agent_id: phoneNumber.assigned_agent_id,
        caller_number: webhookData.From,
        caller_name: webhookData.CallerName,
        provider_call_sid: webhookData.CallSid,
        recording_enabled: phoneNumber.agents.recording_enabled || false,
        metadata: {
          caller_city: webhookData.CallerCity,
          caller_state: webhookData.CallerState,
          caller_country: webhookData.CallerCountry,
          caller_zip: webhookData.CallerZip,
          twilio_account_sid: webhookData.AccountSid
        }
      })

      console.log('Created call log:', callLog.id)

      // Check business hours (mock implementation)
      const isBusinessHours = callHandlingService.isWithinBusinessHours({
        timezone: 'America/New_York',
        days: {
          monday: { start: '09:00', end: '17:00' },
          tuesday: { start: '09:00', end: '17:00' },
          wednesday: { start: '09:00', end: '17:00' },
          thursday: { start: '09:00', end: '17:00' },
          friday: { start: '09:00', end: '17:00' },
          saturday: null,
          sunday: null
        }
      })

      if (!isBusinessHours) {
        // Handle after-hours call
        await callHandlingService.updateCallLog(callLog.id, {
          status: 'missed',
          end_reason: 'timeout'
        })

        return new NextResponse(createAfterHoursTwiML(), {
          headers: { 'Content-Type': 'text/xml' }
        })
      }

      // Create TwiML response for agent handling
      const twiML = createAgentTwiML(phoneNumber.agents, callLog.id)
      
      // Update call status to in_progress
      await callHandlingService.updateCallLog(callLog.id, {
        status: 'in_progress'
      })

      // Add initial transcript entry
      await callHandlingService.addTranscriptEntry(callLog.id, {
        timestamp: new Date().toISOString(),
        speaker: 'agent',
        text: getGreetingMessage(phoneNumber.agents),
        event_type: 'system_event',
        event_data: {
          event: 'call_answered',
          agent_id: phoneNumber.assigned_agent_id
        }
      })

      return new NextResponse(twiML, {
        headers: { 'Content-Type': 'text/xml' }
      })

    } catch (error) {
      console.error('Error creating call log:', error)
      
      // Log the error
      await callHandlingService.logError({
        type: 'webhook_error',
        message: `Failed to handle incoming call: ${error.message}`,
        details: {
          webhook_data: webhookData,
          error: error.message
        },
        timestamp: new Date().toISOString(),
        severity: 'high'
      })

      return new NextResponse(createErrorTwiML('Internal server error'), {
        headers: { 'Content-Type': 'text/xml' }
      })
    }

  } catch (error) {
    console.error('Webhook processing error:', error)
    
    return new NextResponse(createErrorTwiML('Webhook processing failed'), {
      status: 500,
      headers: { 'Content-Type': 'text/xml' }
    })
  }
}

// Helper functions
function createAgentTwiML(agent: any, callLogId: string): string {
  const twiML = new TwiMLResponse()
  
  // Play greeting message
  const greeting = getGreetingMessage(agent)
  twiML.say({
    voice: agent.voice || 'alice',
    language: agent.language || 'en-US'
  }, greeting)

  // Start recording if enabled
  if (agent.recording_enabled) {
    twiML.record({
      action: `/api/webhooks/twilio/recording-complete/${callLogId}`,
      transcribe: true,
      transcribeCallback: `/api/webhooks/twilio/transcription/${callLogId}`
    })
  }

  // Set up real-time stream for speech processing
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://localhost:3000'
  twiML.start().stream({
    name: 'agent-stream',
    url: `wss://${baseUrl}/ws/calls/${callLogId}/stream`,
    track: 'both_tracks'
  })

  return twiML.toString()
}

function getGreetingMessage(agent: any): string {
  // Extract greeting from system prompt or use default
  if (agent.system_prompt) {
    const lines = agent.system_prompt.split('\n')
    const greetingLine = lines.find(line => 
      line.toLowerCase().includes('greeting') || 
      line.toLowerCase().includes('hello') ||
      line.toLowerCase().includes('welcome')
    )
    if (greetingLine) {
      return greetingLine.replace(/^(greeting|hello|welcome):?\s*/i, '').trim()
    }
  }
  
  return `Hello! You've reached ${agent.name}. How can I help you today?`
}

function createErrorTwiML(message: string): string {
  const twiML = new TwiMLResponse()
  twiML.say({
    voice: 'alice',
    language: 'en-US'
  }, `I'm sorry, ${message}. Please try again later or contact support.`)
  
  return twiML.toString()
}

function createAfterHoursTwiML(): string {
  const twiML = new TwiMLResponse()
  twiML.say({
    voice: 'alice',
    language: 'en-US'
  }, 'Thank you for calling. We are currently outside of business hours. Please call back during business hours or leave a message.')
  
  // In production, you might add voicemail recording here
  twiML.record({
    action: '/api/webhooks/twilio/voicemail',
    transcribe: true
  })
  
  return twiML.toString()
}

// Verify webhook authenticity (optional but recommended for production)
function verifyTwilioSignature(request: NextRequest): boolean {
  // In production, implement Twilio signature verification
  // const signature = request.headers.get('x-twilio-signature')
  // const authToken = process.env.TWILIO_AUTH_TOKEN
  // return twilioClient.validateRequest(authToken, signature, url, params)
  return true // For development
}