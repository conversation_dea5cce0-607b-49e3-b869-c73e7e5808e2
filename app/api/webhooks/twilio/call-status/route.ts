// Twi<PERSON> Call Status Webhook Handler
// Epic 6 Story 6.3: An<PERSON>f-Handling und -protokollierung

import { NextRequest, NextResponse } from 'next/server'
import { CallHandlingService } from '@/services/callHandlingService'
import type { TwilioCallStatusWebhook } from '@/types/call-handling'

const callHandlingService = new CallHandlingService()

export async function POST(request: NextRequest) {
  try {
    // Parse Twilio webhook data
    const body = await request.formData()
    const webhookData: TwilioCallStatusWebhook = {
      CallSid: body.get('CallSid') as string,
      AccountSid: body.get('AccountSid') as string,
      From: body.get('From') as string,
      To: body.get('To') as string,
      CallStatus: body.get('CallStatus') as any,
      CallDuration: body.get('CallDuration') as string,
      RecordingUrl: body.get('RecordingUrl') as string || undefined,
      RecordingDuration: body.get('RecordingDuration') as string || undefined
    }

    console.log('Call status webhook:', webhookData)

    // Find the call log by provider_call_sid
    const { createClient } = await import('@/lib/supabaseServer')
    const supabase = createClient()
    
    const { data: callLog, error: callError } = await supabase
      .from('call_logs')
      .select('*')
      .eq('provider_call_sid', webhookData.CallSid)
      .single()

    if (callError || !callLog) {
      console.error('Call log not found for CallSid:', webhookData.CallSid)
      return NextResponse.json({ error: 'Call log not found' }, { status: 404 })
    }

    // Map Twilio status to our status
    const status = mapTwilioStatusToCallStatus(webhookData.CallStatus)
    const endReason = mapTwilioStatusToEndReason(webhookData.CallStatus)

    // Update call log
    const updateData: any = {
      status
    }

    // Set end reason and time for completed calls
    if (['completed', 'failed', 'missed'].includes(status)) {
      updateData.end_reason = endReason
      updateData.end_time = new Date().toISOString()
    }

    // Add recording information if available
    if (webhookData.RecordingUrl) {
      updateData.recording_url = webhookData.RecordingUrl
      updateData.recording_duration_seconds = webhookData.RecordingDuration 
        ? parseInt(webhookData.RecordingDuration) 
        : undefined
    }

    // Calculate and update costs
    if (webhookData.CallDuration) {
      const durationSeconds = parseInt(webhookData.CallDuration)
      const durationCost = callHandlingService.calculateCallCost(durationSeconds)
      
      let recordingCost = 0
      if (webhookData.RecordingDuration) {
        const recordingMinutes = Math.ceil(parseInt(webhookData.RecordingDuration) / 60)
        recordingCost = recordingMinutes * 0.0025 // $0.0025 per minute for recording
      }

      updateData.cost_breakdown = {
        duration_cost: durationCost,
        recording_cost: recordingCost,
        total_cost: durationCost + recordingCost,
        currency: 'USD',
        minutes_billed: Math.ceil(durationSeconds / 60),
        per_minute_rate: 0.0125
      }
      updateData.total_cost = durationCost + recordingCost
    }

    // Update quality metrics based on call outcome
    updateData.quality_metrics = {
      ...callLog.quality_metrics,
      call_completed_successfully: status === 'completed',
      call_duration_seconds: webhookData.CallDuration ? parseInt(webhookData.CallDuration) : 0,
      had_recording: !!webhookData.RecordingUrl,
      provider_status: webhookData.CallStatus
    }

    await callHandlingService.updateCallLog(callLog.id, updateData)

    // Add system event for status change
    await callHandlingService.addSystemEvent(callLog.id, {
      event_type: getSystemEventType(webhookData.CallStatus),
      event_description: `Call status changed to ${webhookData.CallStatus}`,
      timestamp: new Date().toISOString(),
      event_data: {
        twilio_status: webhookData.CallStatus,
        call_duration: webhookData.CallDuration,
        recording_url: webhookData.RecordingUrl,
        recording_duration: webhookData.RecordingDuration
      },
      severity: status === 'failed' ? 'error' : 'info'
    })

    // If call ended, finalize transcript and calculate final costs
    if (['completed', 'failed', 'missed'].includes(status)) {
      await finalizeCall(callLog.id, webhookData)
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Call status webhook error:', error)
    
    // Log the error
    await callHandlingService.logError({
      type: 'webhook_error',
      message: `Failed to process call status webhook: ${error.message}`,
      details: {
        error: error.message,
        stack: error.stack
      },
      timestamp: new Date().toISOString(),
      severity: 'medium'
    })

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Helper functions
function mapTwilioStatusToCallStatus(twilioStatus: string): string {
  const statusMap: Record<string, string> = {
    'queued': 'ringing',
    'ringing': 'ringing', 
    'in-progress': 'in_progress',
    'completed': 'completed',
    'busy': 'missed',
    'failed': 'failed',
    'no-answer': 'missed',
    'canceled': 'missed'
  }
  
  return statusMap[twilioStatus] || 'failed'
}

function mapTwilioStatusToEndReason(twilioStatus: string): string | undefined {
  const reasonMap: Record<string, string> = {
    'completed': 'caller_hangup', // Default assumption
    'busy': 'caller_hangup',
    'failed': 'system_error',
    'no-answer': 'timeout',
    'canceled': 'caller_hangup'
  }
  
  return reasonMap[twilioStatus]
}

function getSystemEventType(twilioStatus: string): string {
  if (twilioStatus === 'completed') return 'call_started' // Using existing enum
  if (['failed', 'busy', 'no-answer'].includes(twilioStatus)) return 'error'
  return 'call_started' // Default to call_started for now
}

async function finalizeCall(callLogId: string, webhookData: TwilioCallStatusWebhook): Promise<void> {
  try {
    // Add final transcript entry
    const finalMessage = webhookData.CallStatus === 'completed' 
      ? 'Call completed successfully'
      : `Call ended with status: ${webhookData.CallStatus}`

    await callHandlingService.addTranscriptEntry(callLogId, {
      timestamp: new Date().toISOString(),
      speaker: 'system',
      text: finalMessage,
      event_type: 'system_event',
      event_data: {
        event: 'call_ended',
        twilio_status: webhookData.CallStatus,
        duration: webhookData.CallDuration
      }
    })

    // Calculate additional costs (AI processing, etc.)
    // In a real implementation, you'd track these throughout the call
    const transcript = await callHandlingService.getTranscript(callLogId)
    const totalTokens = transcript.reduce((sum, entry) => sum + (entry.tokens_used || 0), 0)
    const aiProcessingCost = totalTokens * 0.000002 // Rough estimate for GPT-4

    if (aiProcessingCost > 0) {
      await callHandlingService.updateCallCosts(callLogId, {
        ai_processing_cost: aiProcessingCost
      })
    }

    // Add system event for call finalization
    await callHandlingService.addSystemEvent(callLogId, {
      event_type: 'call_started', // Using existing type
      event_description: 'Call processing finalized',
      timestamp: new Date().toISOString(),
      event_data: {
        total_transcript_entries: transcript.length,
        total_tokens_used: totalTokens,
        ai_processing_cost: aiProcessingCost,
        finalization_time: new Date().toISOString()
      },
      severity: 'info'
    })

    console.log(`Call ${callLogId} finalized successfully`)

  } catch (error) {
    console.error(`Error finalizing call ${callLogId}:`, error)
    
    await callHandlingService.logError({
      type: 'system_error',
      message: `Failed to finalize call: ${error.message}`,
      call_log_id: callLogId,
      details: { error: error.message },
      timestamp: new Date().toISOString(),
      severity: 'medium'
    })
  }
}