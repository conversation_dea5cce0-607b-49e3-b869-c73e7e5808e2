import { NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabaseServer'

export async function GET() {
  try {
    const supabase = createSupabaseServerClient()

    // 1) <PERSON><PERSON><PERSON><PERSON> Auth (falls Session vorhanden)
    const { data: userData, error: userError } = await supabase.auth.getUser()

    // 2) Einfache DB-Operation (optional anpassbar)
    //    Hier wird nur eine triviale RPC-/Query-Simulation probiert.
    //    Passe bei Bedarf eine echte Tabelle/Query an, z. B. .from('profiles').select('*').limit(1)
    let dbOk = false
    let dbError: string | null = null
    try {
      const { error } = await supabase.from('profiles').select('*').limit(1)
      if (error) {
        dbError = error.message
      } else {
        dbOk = true
      }
    } catch (e: any) {
      dbError = e?.message ?? 'Unknown DB error'
    }

    return NextResponse.json(
      {
        ok: true,
        auth: {
          ok: !userError,
          error: userError?.message ?? null,
          user: userData?.user ?? null,
        },
        db: {
          ok: dbOk,
          error: dbError,
        },
        env: {
          hasUrl: Boolean(process.env.NEXT_PUBLIC_SUPABASE_URL),
          hasAnonKey: Boolean(process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY),
        },
      },
      { status: 200 }
    )
  } catch (e: any) {
    return NextResponse.json(
      {
        ok: false,
        error: e?.message ?? 'Unknown error',
        env: {
          hasUrl: Boolean(process.env.NEXT_PUBLIC_SUPABASE_URL),
          hasAnonKey: Boolean(process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY),
        },
      },
      { status: 500 }
    )
  }
}