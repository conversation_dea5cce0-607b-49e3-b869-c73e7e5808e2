import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify phone number ownership
    const { data: phoneNumber, error: phoneError } = await supabase
      .from('phone_numbers')
      .select('id')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (phoneError) {
      if (phoneError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Phone number not found' }, { status: 404 })
      }
      console.error('Error verifying phone number:', phoneError)
      return NextResponse.json({ error: 'Failed to fetch statistics' }, { status: 500 })
    }

    // Parse query parameters for date range
    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')
    const period = searchParams.get('period') || '30' // days

    // Get phone number statistics
    const { data: stats, error: statsError } = await supabase.rpc(
      'get_phone_number_stats',
      { phone_number_uuid: params.id }
    )

    if (statsError) {
      console.error('Error fetching phone number stats:', statsError)
      return NextResponse.json({ error: 'Failed to fetch statistics' }, { status: 500 })
    }

    // Get recent call metrics
    let metricsQuery = supabase
      .from('call_metrics')
      .select('*')
      .eq('phone_number_id', params.id)
      .order('date', { ascending: false })

    if (startDate && endDate) {
      metricsQuery = metricsQuery
        .gte('date', startDate)
        .lte('date', endDate)
    } else {
      // Default to last 30 days
      const daysAgo = new Date()
      daysAgo.setDate(daysAgo.getDate() - parseInt(period))
      metricsQuery = metricsQuery.gte('date', daysAgo.toISOString().split('T')[0])
    }

    const { data: metrics, error: metricsError } = await metricsQuery

    if (metricsError) {
      console.error('Error fetching call metrics:', metricsError)
      return NextResponse.json({ error: 'Failed to fetch call metrics' }, { status: 500 })
    }

    // Get recent calls
    const { data: recentCalls, error: callsError } = await supabase
      .from('call_logs')
      .select('*')
      .eq('phone_number_id', params.id)
      .order('start_time', { ascending: false })
      .limit(10)

    if (callsError) {
      console.error('Error fetching recent calls:', callsError)
      return NextResponse.json({ error: 'Failed to fetch recent calls' }, { status: 500 })
    }

    // Calculate summary statistics from metrics
    const totalMetrics = metrics?.reduce(
      (acc, metric) => ({
        total_calls: acc.total_calls + metric.total_calls,
        answered_calls: acc.answered_calls + metric.answered_calls,
        missed_calls: acc.missed_calls + metric.missed_calls,
        failed_calls: acc.failed_calls + metric.failed_calls,
        total_duration: acc.total_duration + metric.total_duration,
        total_cost: acc.total_cost + (metric.total_cost || 0)
      }),
      {
        total_calls: 0,
        answered_calls: 0,
        missed_calls: 0,
        failed_calls: 0,
        total_duration: 0,
        total_cost: 0
      }
    ) || {
      total_calls: stats?.[0]?.total_calls || 0,
      answered_calls: stats?.[0]?.answered_calls || 0,
      missed_calls: stats?.[0]?.missed_calls || 0,
      failed_calls: 0,
      total_duration: 0,
      total_cost: stats?.[0]?.total_cost || 0
    }

    const response = {
      overview: {
        ...totalMetrics,
        answer_rate: totalMetrics.total_calls > 0 
          ? (totalMetrics.answered_calls / totalMetrics.total_calls * 100).toFixed(1)
          : 0,
        average_duration: stats?.[0]?.average_duration || 0,
        last_call_date: stats?.[0]?.last_call_date
      },
      daily_metrics: metrics || [],
      recent_calls: recentCalls || [],
      period: {
        start_date: startDate || new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end_date: endDate || new Date().toISOString().split('T')[0],
        days: parseInt(period)
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error in phone number stats API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}