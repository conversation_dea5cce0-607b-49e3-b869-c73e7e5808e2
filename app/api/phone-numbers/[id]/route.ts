import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get phone number with agent info
    const { data: phoneNumber, error } = await supabase
      .from('phone_numbers')
      .select(`
        *,
        agents(id, name, description)
      `)
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Phone number not found' }, { status: 404 })
      }
      console.error('Error fetching phone number:', error)
      return NextResponse.json({ error: 'Failed to fetch phone number' }, { status: 500 })
    }

    return NextResponse.json(phoneNumber)
  } catch (error) {
    console.error('Error in phone number GET API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const {
      friendly_name,
      status,
      assigned_agent_id,
      forwarding_settings,
      business_hours,
      webhook_url
    } = body

    // Verify phone number ownership
    const { data: existingPhoneNumber, error: checkError } = await supabase
      .from('phone_numbers')
      .select('id')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Phone number not found' }, { status: 404 })
      }
      console.error('Error verifying phone number:', checkError)
      return NextResponse.json({ error: 'Failed to update phone number' }, { status: 500 })
    }

    // Verify agent ownership if assigned
    if (assigned_agent_id) {
      const { data: agent, error: agentError } = await supabase
        .from('agents')
        .select('id')
        .eq('id', assigned_agent_id)
        .eq('user_id', user.id)
        .single()

      if (agentError) {
        return NextResponse.json({ error: 'Invalid agent assignment' }, { status: 400 })
      }
    }

    // Prepare update data
    const updateData: any = {}
    if (friendly_name !== undefined) updateData.friendly_name = friendly_name
    if (status !== undefined) updateData.status = status
    if (assigned_agent_id !== undefined) updateData.assigned_agent_id = assigned_agent_id
    if (forwarding_settings !== undefined) updateData.forwarding_settings = forwarding_settings
    if (business_hours !== undefined) updateData.business_hours = business_hours
    if (webhook_url !== undefined) updateData.webhook_url = webhook_url

    // Update phone number
    const { data: phoneNumber, error: updateError } = await supabase
      .from('phone_numbers')
      .update(updateData)
      .eq('id', params.id)
      .eq('user_id', user.id)
      .select(`
        *,
        agents(id, name, description)
      `)
      .single()

    if (updateError) {
      console.error('Error updating phone number:', updateError)
      return NextResponse.json({ error: 'Failed to update phone number' }, { status: 500 })
    }

    return NextResponse.json(phoneNumber)
  } catch (error) {
    console.error('Error in phone number PUT API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify phone number ownership
    const { data: phoneNumber, error: checkError } = await supabase
      .from('phone_numbers')
      .select('id, number')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single()

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Phone number not found' }, { status: 404 })
      }
      console.error('Error verifying phone number:', checkError)
      return NextResponse.json({ error: 'Failed to delete phone number' }, { status: 500 })
    }

    // Delete phone number (cascade will handle call_logs, call_metrics, billing_usage)
    const { error: deleteError } = await supabase
      .from('phone_numbers')
      .delete()
      .eq('id', params.id)
      .eq('user_id', user.id)

    if (deleteError) {
      console.error('Error deleting phone number:', deleteError)
      return NextResponse.json({ error: 'Failed to delete phone number' }, { status: 500 })
    }

    return NextResponse.json({ 
      message: 'Phone number deleted successfully',
      number: phoneNumber.number 
    })
  } catch (error) {
    console.error('Error in phone number DELETE API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}