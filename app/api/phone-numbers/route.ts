import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const assigned = searchParams.get('assigned')

    let query = supabase
      .from('phone_numbers')
      .select(`
        *,
        agents(id, name)
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status)
    }

    if (assigned === 'true') {
      query = query.not('assigned_agent_id', 'is', null)
    } else if (assigned === 'false') {
      query = query.is('assigned_agent_id', null)
    }

    const { data: phoneNumbers, error } = await query

    if (error) {
      console.error('Error fetching phone numbers:', error)
      return NextResponse.json({ error: 'Failed to fetch phone numbers' }, { status: 500 })
    }

    return NextResponse.json(phoneNumbers || [])
  } catch (error) {
    console.error('Error in phone numbers API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { phone_number, friendly_name, assigned_agent_id } = body

    if (!phone_number) {
      return NextResponse.json({ error: 'phone_number is required' }, { status: 400 })
    }

    // Extract country code and area code from phone number
    // This is a simplified implementation - in production, use a proper phone number parsing library
    const countryCode = phone_number.startsWith('+1') ? 'US' : 'UNKNOWN'
    const areaCode = phone_number.startsWith('+1') ? phone_number.substring(2, 5) : undefined

    // Verify agent ownership if assigned
    if (assigned_agent_id) {
      const { data: agent, error: agentError } = await supabase
        .from('agents')
        .select('id')
        .eq('id', assigned_agent_id)
        .eq('user_id', user.id)
        .single()

      if (agentError) {
        return NextResponse.json({ error: 'Invalid agent assignment' }, { status: 400 })
      }
    }

    // Create phone number record
    const { data: phoneNumber, error: createError } = await supabase
      .from('phone_numbers')
      .insert({
        user_id: user.id,
        number: phone_number,
        friendly_name: friendly_name || `Phone ${phone_number}`,
        country_code: countryCode,
        area_code: areaCode,
        assigned_agent_id,
        monthly_cost: 1.00, // Default cost - should be set by provider integration
        provider_id: 'twilio',
        capabilities: {
          voice: true,
          sms: false
        }
      })
      .select(`
        *,
        agents(id, name)
      `)
      .single()

    if (createError) {
      if (createError.code === '23505') { // Unique constraint violation
        return NextResponse.json({ error: 'Phone number already exists' }, { status: 409 })
      }
      console.error('Error creating phone number:', createError)
      return NextResponse.json({ error: 'Failed to create phone number' }, { status: 500 })
    }

    return NextResponse.json(phoneNumber, { status: 201 })
  } catch (error) {
    console.error('Error in phone number creation API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}