import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { phone_number, friendly_name, assigned_agent_id } = body

    if (!phone_number) {
      return NextResponse.json({ error: 'phone_number is required' }, { status: 400 })
    }

    // Validate phone number format
    if (!phone_number.match(/^\+\d{10,15}$/)) {
      return NextResponse.json({ error: 'Invalid phone number format' }, { status: 400 })
    }

    // Check if number already exists
    const { data: existingNumber } = await supabase
      .from('phone_numbers')
      .select('id')
      .eq('number', phone_number)
      .single()

    if (existingNumber) {
      return NextResponse.json({ error: 'Phone number already owned' }, { status: 409 })
    }

    // Extract country code and area code
    const countryCode = phone_number.startsWith('+1') ? 'US' : 
                       phone_number.startsWith('+44') ? 'GB' : 'UNKNOWN'
    const areaCode = phone_number.startsWith('+1') ? phone_number.substring(2, 5) : 
                    phone_number.startsWith('+44') ? phone_number.substring(3, 6) : undefined

    // Verify agent ownership if assigned
    if (assigned_agent_id) {
      const { data: agent, error: agentError } = await supabase
        .from('agents')
        .select('id')
        .eq('id', assigned_agent_id)
        .eq('user_id', user.id)
        .single()

      if (agentError) {
        return NextResponse.json({ error: 'Invalid agent assignment' }, { status: 400 })
      }
    }

    // Mock provider integration - in production, this would call Twilio API
    const mockProviderResponse = {
      success: true,
      provider_phone_number_sid: `PN${Math.random().toString(36).substring(2, 15)}`,
      monthly_cost: countryCode === 'US' ? 1.00 : 1.50,
      capabilities: {
        voice: true,
        sms: true
      }
    }

    if (!mockProviderResponse.success) {
      return NextResponse.json({ error: 'Failed to purchase phone number from provider' }, { status: 500 })
    }

    // Create phone number record
    const { data: phoneNumber, error: createError } = await supabase
      .from('phone_numbers')
      .insert({
        user_id: user.id,
        number: phone_number,
        friendly_name: friendly_name || `Phone ${phone_number}`,
        country_code: countryCode,
        area_code: areaCode,
        assigned_agent_id,
        monthly_cost: mockProviderResponse.monthly_cost,
        provider_id: 'twilio',
        provider_phone_number_sid: mockProviderResponse.provider_phone_number_sid,
        capabilities: mockProviderResponse.capabilities,
        status: 'active'
      })
      .select(`
        *,
        agents(id, name)
      `)
      .single()

    if (createError) {
      console.error('Error creating phone number:', createError)
      return NextResponse.json({ error: 'Failed to create phone number record' }, { status: 500 })
    }

    // Create initial billing record
    const today = new Date()
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
    
    await supabase
      .from('billing_usage')
      .insert({
        user_id: user.id,
        phone_number_id: phoneNumber.id,
        period_start: today.toISOString().split('T')[0],
        period_end: endOfMonth.toISOString().split('T')[0],
        usage_type: 'monthly_fee',
        quantity: 1,
        unit_cost: mockProviderResponse.monthly_cost,
        total_cost: mockProviderResponse.monthly_cost,
        currency: 'USD'
      })

    return NextResponse.json({
      ...phoneNumber,
      provider_response: {
        provider_phone_number_sid: mockProviderResponse.provider_phone_number_sid,
        monthly_cost: mockProviderResponse.monthly_cost
      }
    }, { status: 201 })
  } catch (error) {
    console.error('Error in phone number purchase API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}