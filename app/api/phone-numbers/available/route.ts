import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

// Mock data for available phone numbers
// In production, this would integrate with Twilio or another provider
const generateMockPhoneNumbers = (countryCode: string, areaCode?: string, limit: number = 20) => {
  const numbers = []
  const baseAreaCode = areaCode || (countryCode === 'US' ? '555' : '123')
  
  for (let i = 0; i < limit; i++) {
    const suffix = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
    const prefix = Math.floor(Math.random() * 900 + 100).toString()
    
    numbers.push({
      phone_number: countryCode === 'US' ? `+1${baseAreaCode}${prefix}${suffix}` : `+44${baseAreaCode}${prefix}${suffix}`,
      country_code: countryCode,
      area_code: baseAreaCode,
      region: countryCode === 'US' ? getUSRegion(baseAreaCode) : 'London',
      capabilities: {
        voice: true,
        sms: Math.random() > 0.3, // 70% have SMS
        mms: Math.random() > 0.7   // 30% have MMS
      },
      monthly_cost: countryCode === 'US' ? 1.00 : 1.50,
      setup_cost: 0.00
    })
  }
  
  return numbers
}

const getUSRegion = (areaCode: string): string => {
  const regionMap: Record<string, string> = {
    '212': 'New York, NY',
    '213': 'Los Angeles, CA',
    '415': 'San Francisco, CA',
    '555': 'Various Locations',
    '617': 'Boston, MA',
    '312': 'Chicago, IL',
    '713': 'Houston, TX',
    '305': 'Miami, FL',
    '206': 'Seattle, WA',
    '404': 'Atlanta, GA'
  }
  return regionMap[areaCode] || `Area ${areaCode}`
}

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const countryCode = searchParams.get('country_code') || 'US'
    const areaCode = searchParams.get('area_code')
    const contains = searchParams.get('contains')
    const voiceEnabled = searchParams.get('voice_enabled') === 'true'
    const smsEnabled = searchParams.get('sms_enabled') === 'true'
    const limit = parseInt(searchParams.get('limit') || '20')

    // Generate mock phone numbers
    let availableNumbers = generateMockPhoneNumbers(countryCode, areaCode, limit * 2)

    // Apply filters
    if (contains) {
      availableNumbers = availableNumbers.filter(num => 
        num.phone_number.includes(contains)
      )
    }

    if (voiceEnabled) {
      availableNumbers = availableNumbers.filter(num => num.capabilities.voice)
    }

    if (smsEnabled) {
      availableNumbers = availableNumbers.filter(num => num.capabilities.sms)
    }

    // Check which numbers are already owned by users
    const phoneNumbers = availableNumbers.map(num => num.phone_number)
    const { data: existingNumbers } = await supabase
      .from('phone_numbers')
      .select('number')
      .in('number', phoneNumbers)

    const existingNumbersSet = new Set(existingNumbers?.map(num => num.number) || [])
    
    // Filter out existing numbers
    availableNumbers = availableNumbers.filter(num => 
      !existingNumbersSet.has(num.phone_number)
    )

    // Limit results
    availableNumbers = availableNumbers.slice(0, limit)

    return NextResponse.json({
      numbers: availableNumbers,
      total: availableNumbers.length,
      filters: {
        country_code: countryCode,
        area_code: areaCode,
        contains,
        voice_enabled: voiceEnabled,
        sms_enabled: smsEnabled
      }
    })
  } catch (error) {
    console.error('Error in available phone numbers API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}