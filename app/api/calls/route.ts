// Call Logs API Endpoints
// Epic 6 Story 6.3: Anruf-Handling und -protokollierung

import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { CallHandlingService } from '@/services/callHandlingService'
import type { CallLogFilters } from '@/types/call-handling'

const callHandlingService = new CallHandlingService()

// GET /api/calls - Search and filter call logs
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    
    const filters: CallLogFilters = {
      status: searchParams.get('status')?.split(',') as any,
      agent_ids: searchParams.get('agent_ids')?.split(','),
      phone_number_ids: searchParams.get('phone_number_ids')?.split(','),
      date_from: searchParams.get('date_from') || undefined,
      date_to: searchParams.get('date_to') || undefined,
      duration_min: searchParams.get('duration_min') ? parseInt(searchParams.get('duration_min')!) : undefined,
      duration_max: searchParams.get('duration_max') ? parseInt(searchParams.get('duration_max')!) : undefined,
      caller_number: searchParams.get('caller_number') || undefined,
      search_query: searchParams.get('search_query') || undefined,
      has_recording: searchParams.get('has_recording') ? searchParams.get('has_recording') === 'true' : undefined,
      success_rating_min: searchParams.get('success_rating_min') ? parseInt(searchParams.get('success_rating_min')!) : undefined,
      page: parseInt(searchParams.get('page') || '1'),
      page_size: parseInt(searchParams.get('page_size') || '50'),
      sort_by: searchParams.get('sort_by') as any || 'start_time',
      sort_order: searchParams.get('sort_order') as any || 'desc'
    }

    const result = await callHandlingService.searchCallLogs(user.id, filters)

    return NextResponse.json(result)

  } catch (error) {
    console.error('Error fetching call logs:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/calls - Create a new call log (primarily for testing)
export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { phone_number_id, agent_id, caller_number, caller_name, recording_enabled, metadata } = body

    if (!phone_number_id || !agent_id || !caller_number) {
      return NextResponse.json({ 
        error: 'phone_number_id, agent_id, and caller_number are required' 
      }, { status: 400 })
    }

    // Verify phone number ownership
    const { data: phoneNumber, error: phoneError } = await supabase
      .from('phone_numbers')
      .select('id, user_id')
      .eq('id', phone_number_id)
      .eq('user_id', user.id)
      .single()

    if (phoneError || !phoneNumber) {
      return NextResponse.json({ error: 'Phone number not found' }, { status: 404 })
    }

    // Verify agent ownership
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('id, user_id')
      .eq('id', agent_id)
      .eq('user_id', user.id)
      .single()

    if (agentError || !agent) {
      return NextResponse.json({ error: 'Agent not found' }, { status: 404 })
    }

    const callLog = await callHandlingService.createCallLog(user.id, {
      phone_number_id,
      agent_id,
      caller_number,
      caller_name,
      recording_enabled,
      metadata
    })

    return NextResponse.json(callLog, { status: 201 })

  } catch (error) {
    console.error('Error creating call log:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}