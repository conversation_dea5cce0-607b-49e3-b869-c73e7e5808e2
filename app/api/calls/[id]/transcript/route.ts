// Call Transcript API Endpoints
// Epic 6 Story 6.3: Anruf-Handling und -protokollierung

import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { CallHandlingService } from '@/services/callHandlingService'

const callHandlingService = new CallHandlingService()

// GET /api/calls/[id]/transcript - Get call transcript
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify call log ownership
    const callLog = await callHandlingService.getCallLog(params.id)
    if (!callLog) {
      return NextResponse.json({ error: 'Call log not found' }, { status: 404 })
    }

    if (callLog.user_id !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const transcript = await callHandlingService.getTranscript(params.id)

    return NextResponse.json({
      call_log_id: params.id,
      entries: transcript,
      total_entries: transcript.length
    })

  } catch (error) {
    console.error('Error fetching transcript:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/calls/[id]/transcript - Add transcript entry
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify call log ownership
    const callLog = await callHandlingService.getCallLog(params.id)
    if (!callLog) {
      return NextResponse.json({ error: 'Call log not found' }, { status: 404 })
    }

    if (callLog.user_id !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const { timestamp, speaker, text, confidence, processing_time_ms, tokens_used, model_used, event_type, event_data } = body

    if (!timestamp || !speaker || !text) {
      return NextResponse.json({ 
        error: 'timestamp, speaker, and text are required' 
      }, { status: 400 })
    }

    if (!['caller', 'agent', 'system'].includes(speaker)) {
      return NextResponse.json({ 
        error: 'speaker must be one of: caller, agent, system' 
      }, { status: 400 })
    }

    const entry = await callHandlingService.addTranscriptEntry(params.id, {
      timestamp,
      speaker,
      text,
      confidence,
      processing_time_ms,
      tokens_used,
      model_used,
      event_type,
      event_data
    })

    return NextResponse.json(entry, { status: 201 })

  } catch (error) {
    console.error('Error adding transcript entry:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}