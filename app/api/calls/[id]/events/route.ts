// Call System Events API Endpoints
// Epic 6 Story 6.3: Anruf-Handling und -protokollierung

import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { CallHandlingService } from '@/services/callHandlingService'

const callHandlingService = new CallHandlingService()

// GET /api/calls/[id]/events - Get system events for a call
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify call log ownership
    const callLog = await callHandlingService.getCallLog(params.id)
    if (!callLog) {
      return NextResponse.json({ error: 'Call log not found' }, { status: 404 })
    }

    if (callLog.user_id !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const events = await callHandlingService.getSystemEvents(params.id)

    return NextResponse.json(events)

  } catch (error) {
    console.error('Error fetching system events:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/calls/[id]/events - Add system event
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify call log ownership
    const callLog = await callHandlingService.getCallLog(params.id)
    if (!callLog) {
      return NextResponse.json({ error: 'Call log not found' }, { status: 404 })
    }

    if (callLog.user_id !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const { event_type, event_description, timestamp, duration_ms, event_data, severity } = body

    if (!event_type || !event_description || !timestamp) {
      return NextResponse.json({ 
        error: 'event_type, event_description, and timestamp are required' 
      }, { status: 400 })
    }

    const validEventTypes = ['call_started', 'agent_joined', 'recording_started', 'tool_used', 'manual_takeover', 'transferred', 'error']
    if (!validEventTypes.includes(event_type)) {
      return NextResponse.json({ 
        error: `event_type must be one of: ${validEventTypes.join(', ')}` 
      }, { status: 400 })
    }

    const event = await callHandlingService.addSystemEvent(params.id, {
      event_type,
      event_description,
      timestamp,
      duration_ms,
      event_data,
      severity
    })

    return NextResponse.json(event, { status: 201 })

  } catch (error) {
    console.error('Error adding system event:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}