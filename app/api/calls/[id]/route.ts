// Individual Call Log API Endpoints
// Epic 6 Story 6.3: An<PERSON>f-Handling und -protokollierung

import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { CallHandlingService } from '@/services/callHandlingService'

const callHandlingService = new CallHandlingService()

// GET /api/calls/[id] - Get call log details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const callLog = await callHandlingService.getCallLog(params.id)
    
    if (!callLog) {
      return NextResponse.json({ error: 'Call log not found' }, { status: 404 })
    }

    // Verify ownership
    if (callLog.user_id !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    return NextResponse.json(callLog)

  } catch (error) {
    console.error('Error fetching call log:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/calls/[id] - Update call log
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify ownership first
    const existingCallLog = await callHandlingService.getCallLog(params.id)
    if (!existingCallLog) {
      return NextResponse.json({ error: 'Call log not found' }, { status: 404 })
    }

    if (existingCallLog.user_id !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const updatedCallLog = await callHandlingService.updateCallLog(params.id, body)

    return NextResponse.json(updatedCallLog)

  } catch (error) {
    console.error('Error updating call log:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE /api/calls/[id] - Delete call log
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify ownership first
    const callLog = await callHandlingService.getCallLog(params.id)
    if (!callLog) {
      return NextResponse.json({ error: 'Call log not found' }, { status: 404 })
    }

    if (callLog.user_id !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    await callHandlingService.deleteCallLog(params.id)

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error deleting call log:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}