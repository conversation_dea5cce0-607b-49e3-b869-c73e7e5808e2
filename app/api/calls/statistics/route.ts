// Call Statistics API Endpoint
// Epic 6 Story 6.3: Anruf-Handling und -protokollierung

import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { CallHandlingService } from '@/services/callHandlingService'

const callHandlingService = new CallHandlingService()

// GET /api/calls/statistics - Get call statistics and analytics
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')
    const includeDaily = searchParams.get('include_daily') === 'true'
    const includeHourly = searchParams.get('include_hourly') === 'true'
    const agentId = searchParams.get('agent_id')
    const phoneNumberId = searchParams.get('phone_number_id')

    // Get basic statistics
    const statistics = await callHandlingService.getCallStatistics(user.id, dateFrom, dateTo)

    let result: any = {
      ...statistics,
      period: {
        date_from: dateFrom,
        date_to: dateTo
      }
    }

    // Add time-series data if requested
    if (includeDaily) {
      const dailyStats = await getDailyCallStats(user.id, dateFrom, dateTo, agentId, phoneNumberId)
      result.daily_stats = dailyStats
    }

    if (includeHourly) {
      const hourlyStats = await getHourlyCallStats(user.id, dateFrom, dateTo, agentId, phoneNumberId)
      result.hourly_stats = hourlyStats
    }

    // Add additional metrics
    const additionalMetrics = await getAdditionalMetrics(user.id, dateFrom, dateTo, agentId, phoneNumberId)
    result = { ...result, ...additionalMetrics }

    return NextResponse.json(result)

  } catch (error) {
    console.error('Error fetching call statistics:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Helper functions for detailed statistics
async function getDailyCallStats(
  userId: string, 
  dateFrom?: string, 
  dateTo?: string,
  agentId?: string,
  phoneNumberId?: string
) {
  const { createClient } = await import('@/lib/supabaseServer')
  const supabase = createClient()

  let query = supabase
    .from('call_logs')
    .select(`
      start_time,
      status,
      duration_seconds,
      total_cost,
      success_rating
    `)
    .eq('user_id', userId)

  if (dateFrom) query = query.gte('start_time', dateFrom)
  if (dateTo) query = query.lte('start_time', dateTo)
  if (agentId) query = query.eq('agent_id', agentId)
  if (phoneNumberId) query = query.eq('phone_number_id', phoneNumberId)

  const { data, error } = await query

  if (error) throw error

  // Group by date
  const dailyGroups: Record<string, any> = {}
  
  data?.forEach(call => {
    const date = call.start_time.split('T')[0]
    if (!dailyGroups[date]) {
      dailyGroups[date] = {
        date,
        call_count: 0,
        completed_calls: 0,
        total_duration: 0,
        total_cost: 0,
        ratings: []
      }
    }
    
    dailyGroups[date].call_count++
    if (call.status === 'completed') dailyGroups[date].completed_calls++
    if (call.duration_seconds) dailyGroups[date].total_duration += call.duration_seconds
    if (call.total_cost) dailyGroups[date].total_cost += call.total_cost
    if (call.success_rating) dailyGroups[date].ratings.push(call.success_rating)
  })

  return Object.values(dailyGroups).map((day: any) => ({
    ...day,
    average_rating: day.ratings.length > 0 
      ? day.ratings.reduce((sum: number, rating: number) => sum + rating, 0) / day.ratings.length 
      : null
  }))
}

async function getHourlyCallStats(
  userId: string,
  dateFrom?: string,
  dateTo?: string,
  agentId?: string,
  phoneNumberId?: string
) {
  const { createClient } = await import('@/lib/supabaseServer')
  const supabase = createClient()

  let query = supabase
    .from('call_logs')
    .select(`
      start_time,
      status,
      duration_seconds
    `)
    .eq('user_id', userId)

  if (dateFrom) query = query.gte('start_time', dateFrom)
  if (dateTo) query = query.lte('start_time', dateTo)
  if (agentId) query = query.eq('agent_id', agentId)
  if (phoneNumberId) query = query.eq('phone_number_id', phoneNumberId)

  const { data, error } = await query

  if (error) throw error

  // Group by hour
  const hourlyGroups: Record<number, any> = {}
  
  for (let hour = 0; hour < 24; hour++) {
    hourlyGroups[hour] = {
      hour,
      call_count: 0,
      completed_calls: 0,
      total_duration: 0
    }
  }
  
  data?.forEach(call => {
    const hour = new Date(call.start_time).getHours()
    hourlyGroups[hour].call_count++
    if (call.status === 'completed') hourlyGroups[hour].completed_calls++
    if (call.duration_seconds) hourlyGroups[hour].total_duration += call.duration_seconds
  })

  return Object.values(hourlyGroups).map((hourData: any) => ({
    ...hourData,
    average_duration: hourData.call_count > 0 
      ? Math.round(hourData.total_duration / hourData.call_count) 
      : 0
  }))
}

async function getAdditionalMetrics(
  userId: string,
  dateFrom?: string,
  dateTo?: string,
  agentId?: string,
  phoneNumberId?: string
) {
  const { createClient } = await import('@/lib/supabaseServer')
  const supabase = createClient()

  // Get agent performance breakdown
  let agentQuery = supabase
    .from('call_logs')
    .select(`
      agent_id,
      status,
      duration_seconds,
      total_cost,
      agents(id, name)
    `)
    .eq('user_id', userId)

  if (dateFrom) agentQuery = agentQuery.gte('start_time', dateFrom)
  if (dateTo) agentQuery = agentQuery.lte('start_time', dateTo)
  if (agentId) agentQuery = agentQuery.eq('agent_id', agentId)
  if (phoneNumberId) agentQuery = agentQuery.eq('phone_number_id', phoneNumberId)

  const { data: agentData } = await agentQuery

  // Group by agent
  const agentStats: Record<string, any> = {}
  
  agentData?.forEach(call => {
    if (!call.agent_id || !call.agents) return
    
    if (!agentStats[call.agent_id]) {
      agentStats[call.agent_id] = {
        agent_id: call.agent_id,
        agent_name: call.agents.name,
        call_count: 0,
        completed_calls: 0,
        total_duration: 0,
        total_cost: 0
      }
    }
    
    const stats = agentStats[call.agent_id]
    stats.call_count++
    if (call.status === 'completed') stats.completed_calls++
    if (call.duration_seconds) stats.total_duration += call.duration_seconds
    if (call.total_cost) stats.total_cost += call.total_cost
  })

  // Get phone number performance breakdown
  let phoneQuery = supabase
    .from('call_logs')
    .select(`
      phone_number_id,
      status,
      duration_seconds,
      total_cost,
      phone_numbers(id, number, friendly_name)
    `)
    .eq('user_id', userId)

  if (dateFrom) phoneQuery = phoneQuery.gte('start_time', dateFrom)
  if (dateTo) phoneQuery = phoneQuery.lte('start_time', dateTo)
  if (agentId) phoneQuery = phoneQuery.eq('agent_id', agentId)
  if (phoneNumberId) phoneQuery = phoneQuery.eq('phone_number_id', phoneNumberId)

  const { data: phoneData } = await phoneQuery

  // Group by phone number
  const phoneStats: Record<string, any> = {}
  
  phoneData?.forEach(call => {
    if (!call.phone_number_id || !call.phone_numbers) return
    
    if (!phoneStats[call.phone_number_id]) {
      phoneStats[call.phone_number_id] = {
        phone_number_id: call.phone_number_id,
        phone_number: call.phone_numbers.number,
        friendly_name: call.phone_numbers.friendly_name,
        call_count: 0,
        completed_calls: 0,
        total_duration: 0,
        total_cost: 0
      }
    }
    
    const stats = phoneStats[call.phone_number_id]
    stats.call_count++
    if (call.status === 'completed') stats.completed_calls++
    if (call.duration_seconds) stats.total_duration += call.duration_seconds
    if (call.total_cost) stats.total_cost += call.total_cost
  })

  // Calculate success rates
  Object.values(agentStats).forEach((stats: any) => {
    stats.success_rate = stats.call_count > 0 
      ? Math.round((stats.completed_calls / stats.call_count) * 100) 
      : 0
    stats.average_duration = stats.completed_calls > 0 
      ? Math.round(stats.total_duration / stats.completed_calls) 
      : 0
  })

  Object.values(phoneStats).forEach((stats: any) => {
    stats.success_rate = stats.call_count > 0 
      ? Math.round((stats.completed_calls / stats.call_count) * 100) 
      : 0
    stats.average_duration = stats.completed_calls > 0 
      ? Math.round(stats.total_duration / stats.completed_calls) 
      : 0
  })

  return {
    agent_performance: Object.values(agentStats),
    phone_number_performance: Object.values(phoneStats)
  }
}