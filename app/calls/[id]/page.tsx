'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { AppSidebar } from '@/components/app-sidebar'
import { AuthGuard } from '@/components/auth-guard'
import { SiteHeader } from '@/components/dashboard/site-header'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  ArrowLeft,
  Phone,
  User,
  Clock,
  DollarSign,
  Calendar,
  MapPin,
  Star,
  Play,
  Download,
  MessageSquare,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  PhoneMissed,
  Loader2,
  PhoneCall,
  TrendingUp,
  Activity
} from 'lucide-react'
import { formatDistanceToNow, format } from 'date-fns'
import type { CallLog, CallTranscriptEntry, CallSystemEvent } from '@/types/call-handling'

interface CallDetailsPageProps {
  params: {
    id: string
  }
}

export default function CallDetailsPage({ params }: CallDetailsPageProps) {
  const router = useRouter()
  const [callLog, setCallLog] = useState<CallLog | null>(null)
  const [transcript, setTranscript] = useState<CallTranscriptEntry[]>([])
  const [systemEvents, setSystemEvents] = useState<CallSystemEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [transcriptLoading, setTranscriptLoading] = useState(true)

  useEffect(() => {
    fetchCallDetails()
    fetchTranscript()
    fetchSystemEvents()
  }, [params.id])

  const fetchCallDetails = async () => {
    try {
      const response = await fetch(`/api/calls/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setCallLog(data)
      } else {
        if (response.status === 404) {
          router.push('/calls')
        }
      }
    } catch (error) {
      console.error('Error fetching call details:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchTranscript = async () => {
    try {
      const response = await fetch(`/api/calls/${params.id}/transcript`)
      if (response.ok) {
        const data = await response.json()
        setTranscript(data.entries)
      }
    } catch (error) {
      console.error('Error fetching transcript:', error)
    } finally {
      setTranscriptLoading(false)
    }
  }

  const fetchSystemEvents = async () => {
    try {
      const response = await fetch(`/api/calls/${params.id}/events`)
      if (response.ok) {
        const data = await response.json()
        setSystemEvents(data)
      }
    } catch (error) {
      console.error('Error fetching system events:', error)
    }
  }

  const formatPhoneNumber = (number: string): string => {
    if (number.startsWith('+1')) {
      const cleaned = number.substring(2)
      return `+1 (${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6)}`
    }
    return number
  }

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return '-'
    const hours = Math.floor(seconds / 3600)
    const mins = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getStatusIcon = (status: CallLog['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'failed': return <XCircle className="h-5 w-5 text-red-500" />
      case 'missed': return <PhoneMissed className="h-5 w-5 text-orange-500" />
      case 'in_progress': return <Phone className="h-5 w-5 text-blue-500 animate-pulse" />
      default: return <AlertTriangle className="h-5 w-5 text-gray-500" />
    }
  }

  const getSpeakerIcon = (speaker: string) => {
    switch (speaker) {
      case 'agent': return <User className="h-4 w-4 text-blue-500" />
      case 'caller': return <Phone className="h-4 w-4 text-green-500" />
      case 'system': return <Settings className="h-4 w-4 text-gray-500" />
      default: return <MessageSquare className="h-4 w-4 text-gray-500" />
    }
  }

  const renderStarRating = (rating?: number) => {
    if (!rating) return <span className="text-gray-500">Not rated</span>
    
    return (
      <div className="flex items-center gap-1">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${
              i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-2 text-sm text-gray-600">({rating}/5)</span>
      </div>
    )
  }

  if (loading) {
    return (
      <AuthGuard>
        <SidebarProvider>
          <AppSidebar variant="inset" />
          <SidebarInset>
            <SiteHeader />
            <div className="flex flex-1 flex-col">
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            </div>
          </SidebarInset>
        </SidebarProvider>
      </AuthGuard>
    )
  }

  if (!callLog) {
    return (
      <AuthGuard>
        <SidebarProvider>
          <AppSidebar variant="inset" />
          <SidebarInset>
            <SiteHeader />
            <div className="flex flex-1 flex-col">
              <div className="px-4 lg:px-6 py-8">
                <Card>
                  <CardContent className="py-8 text-center">
                    <p className="text-gray-500">Call not found</p>
                    <Button asChild className="mt-4">
                      <Link href="/calls">Back to Call History</Link>
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </SidebarInset>
        </SidebarProvider>
      </AuthGuard>
    )
  }

  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader />
          <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                <div className="px-4 lg:px-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/calls">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Call History
          </Link>
        </Button>
        <div className="flex-1">
          <div className="flex items-center gap-3">
            {getStatusIcon(callLog.status)}
            <div>
              <h1 className="text-2xl font-bold">
                Call from {formatPhoneNumber(callLog.caller_number)}
              </h1>
              <p className="text-gray-600">
                {callLog.caller_name && `${callLog.caller_name} • `}
                {format(new Date(callLog.start_time), 'PPpp')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Duration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(callLog.duration_seconds)}
            </div>
            <p className="text-sm text-gray-500">
              {callLog.end_time ? 
                `Ended ${formatDistanceToNow(new Date(callLog.end_time), { addSuffix: true })}` :
                'In progress'
              }
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Cost
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${callLog.total_cost.toFixed(4)}
            </div>
            <p className="text-sm text-gray-500">
              Total processing cost
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Star className="h-4 w-4" />
              Rating
            </CardTitle>
          </CardHeader>
          <CardContent>
            {renderStarRating(callLog.success_rating)}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Quality
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <div className="text-sm">
                Avg Response: {callLog.quality_metrics.average_response_time_ms || 0}ms
              </div>
              {callLog.quality_metrics.speech_recognition_accuracy && (
                <div className="text-sm">
                  Speech Accuracy: {Math.round(callLog.quality_metrics.speech_recognition_accuracy * 100)}%
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Details Tabs */}
      <Tabs defaultValue="details" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="transcript">Transcript</TabsTrigger>
          <TabsTrigger value="events">System Events</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Call Information */}
            <Card>
              <CardHeader>
                <CardTitle>Call Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Caller Number</label>
                  <div className="font-mono text-lg">
                    {formatPhoneNumber(callLog.caller_number)}
                  </div>
                </div>
                
                {callLog.caller_name && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Caller Name</label>
                    <div>{callLog.caller_name}</div>
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium text-gray-500">Start Time</label>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    {format(new Date(callLog.start_time), 'PPpp')}
                  </div>
                </div>

                {callLog.end_time && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">End Time</label>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      {format(new Date(callLog.end_time), 'PPpp')}
                    </div>
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div className="flex items-center gap-2 mt-1">
                    {getStatusIcon(callLog.status)}
                    <span className="capitalize">{callLog.status.replace('_', ' ')}</span>
                  </div>
                </div>

                {callLog.end_reason && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">End Reason</label>
                    <div className="capitalize">{callLog.end_reason.replace('_', ' ')}</div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Agent & Phone Number */}
            <Card>
              <CardHeader>
                <CardTitle>Agent & Number</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Agent</label>
                  <div className="flex items-center gap-2 mt-1">
                    <User className="h-4 w-4 text-blue-500" />
                    <span>{callLog.agent?.name || 'Unknown Agent'}</span>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Phone Number</label>
                  <div className="font-mono">
                    {formatPhoneNumber(callLog.phone_number?.number || '')}
                  </div>
                  {callLog.phone_number?.friendly_name && (
                    <div className="text-sm text-gray-500">
                      {callLog.phone_number.friendly_name}
                    </div>
                  )}
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Recording</label>
                  <div className="flex items-center gap-2 mt-1">
                    {callLog.recording_enabled ? (
                      <>
                        {callLog.recording_url ? (
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span>Available</span>
                            <Button size="sm" variant="outline">
                              <Play className="h-4 w-4 mr-2" />
                              Play
                            </Button>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span>Processing...</span>
                          </div>
                        )}
                      </>
                    ) : (
                      <div className="flex items-center gap-2">
                        <XCircle className="h-4 w-4 text-red-500" />
                        <span>Not recorded</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Cost Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Cost Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Duration Cost</label>
                  <div className="font-mono text-lg">
                    ${callLog.cost_breakdown.duration_cost?.toFixed(4) || '0.0000'}
                  </div>
                </div>
                {callLog.cost_breakdown.recording_cost && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Recording Cost</label>
                    <div className="font-mono text-lg">
                      ${callLog.cost_breakdown.recording_cost.toFixed(4)}
                    </div>
                  </div>
                )}
                {callLog.cost_breakdown.ai_processing_cost && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">AI Processing</label>
                    <div className="font-mono text-lg">
                      ${callLog.cost_breakdown.ai_processing_cost.toFixed(4)}
                    </div>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-gray-500">Total Cost</label>
                  <div className="font-mono text-lg font-bold">
                    ${callLog.total_cost.toFixed(4)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transcript" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Call Transcript</span>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {transcriptLoading ? (
                <div className="text-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p>Loading transcript...</p>
                </div>
              ) : transcript.length === 0 ? (
                <div className="text-center py-8">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-700 mb-2">No transcript available</h3>
                  <p className="text-gray-500">This call may still be in progress or transcript processing may be pending.</p>
                </div>
              ) : (
                <ScrollArea className="h-96">
                  <div className="space-y-4">
                    {transcript.map((entry, index) => (
                      <div key={entry.id} className="flex gap-3">
                        <div className="flex flex-col items-center">
                          {getSpeakerIcon(entry.speaker)}
                          <div className="w-px bg-gray-200 flex-1 mt-2" />
                        </div>
                        <div className="flex-1 pb-4">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium capitalize">
                              {entry.speaker === 'agent' ? callLog.agent?.name : entry.speaker}
                            </span>
                            <span className="text-sm text-gray-500">
                              {format(new Date(entry.timestamp), 'HH:mm:ss')}
                            </span>
                            {entry.confidence && (
                              <Badge variant="secondary" className="text-xs">
                                {Math.round(entry.confidence * 100)}% confidence
                              </Badge>
                            )}
                          </div>
                          <p className="text-gray-700 leading-relaxed">{entry.text}</p>
                          {entry.processing_time_ms && (
                            <div className="text-xs text-gray-400 mt-1">
                              Processed in {entry.processing_time_ms}ms
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>System Events</CardTitle>
            </CardHeader>
            <CardContent>
              {systemEvents.length === 0 ? (
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-500">No system events recorded</p>
                </div>
              ) : (
                <ScrollArea className="h-64">
                  <div className="space-y-3">
                    {systemEvents.map((event) => (
                      <div key={event.id} className="flex items-start gap-3 p-3 border rounded-lg">
                        <div className={`w-2 h-2 rounded-full mt-2 ${
                          event.severity === 'error' ? 'bg-red-500' :
                          event.severity === 'warning' ? 'bg-yellow-500' :
                          event.severity === 'critical' ? 'bg-red-600' :
                          'bg-blue-500'
                        }`} />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium">{event.event_type.replace('_', ' ')}</span>
                            <span className="text-sm text-gray-500">
                              {format(new Date(event.timestamp), 'HH:mm:ss')}
                            </span>
                            <Badge variant="outline" className="text-xs">
                              {event.severity}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600">{event.event_description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Average Response Time</label>
                  <div className="text-2xl font-bold">
                    {callLog.quality_metrics.average_response_time_ms || 0}ms
                  </div>
                </div>
                
                {callLog.quality_metrics.speech_recognition_accuracy && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Speech Recognition Accuracy</label>
                    <div className="text-2xl font-bold">
                      {Math.round(callLog.quality_metrics.speech_recognition_accuracy * 100)}%
                    </div>
                  </div>
                )}
                
                <div>
                  <label className="text-sm font-medium text-gray-500">Technical Issues</label>
                  <div>
                    {callLog.quality_metrics.technical_issues?.length === 0 ? (
                      <span className="text-green-600">None reported</span>
                    ) : (
                      <ul className="text-sm text-red-600">
                        {callLog.quality_metrics.technical_issues?.map((issue, index) => (
                          <li key={index}>• {issue}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Call Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Transcript Entries</label>
                    <div className="text-2xl font-bold">{transcript.length}</div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-500">Total Tokens Used</label>
                    <div className="text-2xl font-bold">
                      {transcript.reduce((sum, entry) => sum + (entry.tokens_used || 0), 0)}
                    </div>
                  </div>
                  
                  {callLog.success_rating && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Success Rating</label>
                      <div>{renderStarRating(callLog.success_rating)}</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
                </div>
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}