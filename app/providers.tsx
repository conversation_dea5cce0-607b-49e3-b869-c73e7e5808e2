"use client"

import { ThemeProvider } from "@/lib/theme-context"
import { AuthProvider } from "@/lib/auth-context"

interface ProvidersProps {
  children: React.ReactNode
}

export function Providers({ children }: ProvidersProps) {
  return (
    <AuthProvider>
      <ThemeProvider defaultTheme="system" storageKey="dashboard-theme">
        {children}
      </ThemeProvider>
    </AuthProvider>
  )
}

