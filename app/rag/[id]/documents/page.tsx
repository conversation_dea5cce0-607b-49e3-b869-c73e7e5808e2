'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { DocumentUpload } from '@/components/rag/document-upload'
import { DocumentList } from '@/components/rag/document-list'
import { 
  ArrowLeft, 
  Database, 
  FileText, 
  Upload,
  Settings,
  BarChart3
} from 'lucide-react'
import { createClient } from '@/lib/supabaseClient'

interface KnowledgeBase {
  id: string
  name: string
  description?: string
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

interface DocumentsPageProps {
  params: {
    id: string
  }
}

export default function DocumentsPage({ params }: DocumentsPageProps) {
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeBase | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [activeTab, setActiveTab] = useState('upload')
  const router = useRouter()

  useEffect(() => {
    fetchKnowledgeBase()
  }, [params.id])

  const fetchKnowledgeBase = async () => {
    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('knowledge_bases')
        .select('*')
        .eq('id', params.id)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          router.push('/rag')
          return
        }
        throw error
      }

      setKnowledgeBase(data)
    } catch (error) {
      console.error('Error fetching knowledge base:', error)
      router.push('/rag')
    } finally {
      setLoading(false)
    }
  }

  const handleUploadComplete = () => {
    setRefreshTrigger(prev => prev + 1)
    setActiveTab('documents') // Switch to documents tab to see the uploaded file
  }

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!knowledgeBase) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-gray-500">Knowledge base not found</p>
            <Button asChild className="mt-4">
              <Link href="/rag">Back to Knowledge Bases</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button 
          variant="ghost" 
          size="sm" 
          asChild
          className="flex items-center gap-2"
        >
          <Link href="/rag">
            <ArrowLeft className="h-4 w-4" />
            Back
          </Link>
        </Button>
        <div className="flex-1">
          <div className="flex items-center gap-3">
            <Database className="h-6 w-6 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold">{knowledgeBase.name}</h1>
              {knowledgeBase.description && (
                <p className="text-gray-600">{knowledgeBase.description}</p>
              )}
            </div>
            <Badge variant={knowledgeBase.status === 'active' ? 'default' : 'secondary'}>
              {knowledgeBase.status}
            </Badge>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href={`/rag/${params.id}/edit`} className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/rag/${params.id}`} className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Overview
            </Link>
          </Button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="upload" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Upload Documents
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Manage Documents
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-6">
          <DocumentUpload 
            knowledgeBaseId={params.id} 
            onUploadComplete={handleUploadComplete}
          />
          
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Tips</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium mb-2">Supported File Types</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• PDF documents (.pdf)</li>
                    <li>• Word documents (.docx)</li>
                    <li>• Text files (.txt)</li>
                    <li>• Markdown files (.md)</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Best Practices</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• Keep files under 10MB for faster processing</li>
                    <li>• Use descriptive filenames</li>
                    <li>• Ensure text is selectable in PDFs</li>
                    <li>• Break up very long documents</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <DocumentList 
            knowledgeBaseId={params.id} 
            refreshTrigger={refreshTrigger}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}