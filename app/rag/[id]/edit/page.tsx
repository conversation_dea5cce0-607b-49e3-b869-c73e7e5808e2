'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { toast } from 'sonner'

import { AppSidebar } from '@/components/app-sidebar'
import { AuthGuard } from '@/components/auth-guard'
import { SiteHeader } from '@/components/dashboard/site-header'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { KnowledgeBaseForm } from '@/components/rag/knowledge-base-form'

import { knowledgeBaseService } from '@/services/knowledgeBaseService'
import type { KnowledgeBase } from '@/types/knowledge-base'

export default function EditKnowledgeBasePage() {
  const params = useParams()
  const router = useRouter()
  const id = params.id as string
  
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeBase | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (id) {
      loadKnowledgeBase()
    }
  }, [id])

  const loadKnowledgeBase = async () => {
    try {
      setLoading(true)
      const data = await knowledgeBaseService.getKnowledgeBase(id)
      setKnowledgeBase(data)
    } catch (error) {
      console.error('Error loading knowledge base:', error)
      toast.error('Failed to load knowledge base')
      router.push('/rag')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <AuthGuard>
        <SidebarProvider
          style={
            {
              "--sidebar-width": "calc(var(--spacing) * 72)",
              "--header-height": "calc(var(--spacing) * 12)",
            } as React.CSSProperties
          }
        >
          <AppSidebar variant="inset" />
          <SidebarInset>
            <SiteHeader
              title="Edit Knowledge Base"
              showCreateButton={false}
            />
            <div className="flex flex-1 flex-col">
              <div className="container mx-auto p-6">
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-9 w-32" />
                  </div>
                  <div className="max-w-2xl mx-auto">
                    <div className="border rounded-lg p-6 space-y-6">
                      <div className="space-y-2">
                        <Skeleton className="h-6 w-1/4" />
                        <Skeleton className="h-4 w-3/4" />
                      </div>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-16" />
                          <Skeleton className="h-10 w-full" />
                        </div>
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-20" />
                          <Skeleton className="h-20 w-full" />
                        </div>
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-12" />
                          <Skeleton className="h-6 w-20" />
                        </div>
                      </div>
                      <div className="flex gap-3">
                        <Skeleton className="h-10 w-32" />
                        <Skeleton className="h-10 w-20" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </SidebarInset>
        </SidebarProvider>
      </AuthGuard>
    )
  }

  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader
            title="Edit Knowledge Base"
            showCreateButton={false}
          />
          <div className="flex flex-1 flex-col">
            <div className="container mx-auto p-6">
              <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                  <Button variant="ghost" size="sm" asChild>
                    <Link href={`/rag/${id}`}>
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back to Knowledge Base
                    </Link>
                  </Button>
                </div>

                {/* Form */}
                <KnowledgeBaseForm knowledgeBase={knowledgeBase} mode="edit" />
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}