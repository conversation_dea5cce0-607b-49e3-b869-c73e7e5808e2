'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { ArrowLeft, Edit, FileText, Upload, Settings } from 'lucide-react'
import Link from 'next/link'
import { toast } from 'sonner'

import { AppSidebar } from '@/components/app-sidebar'
import { AuthGuard } from '@/components/auth-guard'
import { SiteHeader } from '@/components/dashboard/site-header'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

import { knowledgeBaseService } from '@/services/knowledgeBaseService'
import type { KnowledgeBase, DataSource } from '@/types/knowledge-base'

export default function KnowledgeBaseDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const id = params.id as string
  
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeBase | null>(null)
  const [dataSources, setDataSources] = useState<DataSource[]>([])
  const [loading, setLoading] = useState(true)
  const [documentsLoading, setDocumentsLoading] = useState(false)

  useEffect(() => {
    if (id) {
      loadKnowledgeBase()
      loadDataSources()
    }
  }, [id])

  const loadKnowledgeBase = async () => {
    try {
      setLoading(true)
      const data = await knowledgeBaseService.getKnowledgeBase(id)
      setKnowledgeBase(data)
    } catch (error) {
      console.error('Error loading knowledge base:', error)
      toast.error('Failed to load knowledge base')
      router.push('/rag')
    } finally {
      setLoading(false)
    }
  }

  const loadDataSources = async () => {
    try {
      setDocumentsLoading(true)
      const data = await knowledgeBaseService.getDataSources(id)
      setDataSources(data)
    } catch (error) {
      console.error('Error loading documents:', error)
      toast.error('Failed to load documents')
    } finally {
      setDocumentsLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getDocumentStatusBadge = (status: string) => {
    switch (status) {
      case 'ready':
        return <Badge className="bg-green-100 text-green-800">Ready</Badge>
      case 'processing':
        return <Badge className="bg-blue-100 text-blue-800">Processing</Badge>
      case 'uploading':
        return <Badge className="bg-yellow-100 text-yellow-800">Uploading</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (loading || !knowledgeBase) {
    return (
      <AuthGuard>
        <SidebarProvider
          style={
            {
              "--sidebar-width": "calc(var(--spacing) * 72)",
              "--header-height": "calc(var(--spacing) * 12)",
            } as React.CSSProperties
          }
        >
          <AppSidebar variant="inset" />
          <SidebarInset>
            <SiteHeader
              title="Knowledge Base"
              showCreateButton={false}
            />
            <div className="flex flex-1 flex-col">
              <div className="container mx-auto p-6">
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-9 w-32" />
                  </div>
                  <Card>
                    <CardHeader>
                      <Skeleton className="h-8 w-1/3" />
                      <Skeleton className="h-4 w-2/3" />
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {[...Array(4)].map((_, i) => (
                          <Skeleton key={i} className="h-16" />
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </SidebarInset>
        </SidebarProvider>
      </AuthGuard>
    )
  }

  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader
            title={knowledgeBase.name}
            showCreateButton={true}
            createButtonText="Upload Documents"
            createButtonHref={`/rag/${id}/documents/upload`}
            createButtonPosition="left"
          />
          <div className="flex flex-1 flex-col">
            <div className="container mx-auto p-6">
              <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href="/rag">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Knowledge Bases
                      </Link>
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" asChild>
                      <Link href={`/rag/${id}/edit`}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </Link>
                    </Button>
                  </div>
                </div>

                {/* Knowledge Base Info */}
                <Card>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-2xl">{knowledgeBase.name}</CardTitle>
                        {knowledgeBase.description && (
                          <CardDescription className="mt-2">
                            {knowledgeBase.description}
                          </CardDescription>
                        )}
                      </div>
                      {getStatusBadge(knowledgeBase.status)}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          {knowledgeBase.document_count || 0}
                        </div>
                        <div className="text-sm text-gray-600">Documents</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {dataSources.filter(ds => ds.status === 'ready').length}
                        </div>
                        <div className="text-sm text-gray-600">Ready</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-yellow-600">
                          {dataSources.filter(ds => ds.status === 'processing').length}
                        </div>
                        <div className="text-sm text-gray-600">Processing</div>
                      </div>
                      <div className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-red-600">
                          {dataSources.filter(ds => ds.status === 'error').length}
                        </div>
                        <div className="text-sm text-gray-600">Errors</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Tabs */}
                <Tabs defaultValue="documents" className="space-y-4">
                  <TabsList>
                    <TabsTrigger value="documents">Documents</TabsTrigger>
                    <TabsTrigger value="settings">Settings</TabsTrigger>
                    <TabsTrigger value="agents">Connected Agents</TabsTrigger>
                  </TabsList>

                  <TabsContent value="documents" className="space-y-4">
                    {documentsLoading ? (
                      <div className="space-y-4">
                        {[...Array(3)].map((_, i) => (
                          <Card key={i}>
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between">
                                <div className="space-y-2 flex-1">
                                  <Skeleton className="h-4 w-1/3" />
                                  <Skeleton className="h-3 w-1/2" />
                                </div>
                                <Skeleton className="h-6 w-16" />
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : dataSources.length === 0 ? (
                      <Card>
                        <CardContent className="text-center py-12">
                          <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                          <h3 className="text-lg font-semibold mb-2">No documents yet</h3>
                          <p className="text-gray-600 mb-4">
                            Upload your first document to start building this knowledge base.
                          </p>
                          <Button asChild>
                            <Link href={`/rag/${id}/documents/upload`}>
                              <Upload className="mr-2 h-4 w-4" />
                              Upload Documents
                            </Link>
                          </Button>
                        </CardContent>
                      </Card>
                    ) : (
                      <div className="space-y-4">
                        {dataSources.map((dataSource) => (
                          <Card key={dataSource.id}>
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-1">
                                    <h4 className="font-medium">{dataSource.name}</h4>
                                    {getDocumentStatusBadge(dataSource.status)}
                                  </div>
                                  <div className="text-sm text-gray-600">
                                    {dataSource.file_type.toUpperCase()} • {Math.round(dataSource.file_size / 1024)} KB
                                    {dataSource.chunk_count && (
                                      <> • {dataSource.chunk_count} chunks</>
                                    )}
                                  </div>
                                  {dataSource.error_message && (
                                    <p className="text-sm text-red-600 mt-1">
                                      Error: {dataSource.error_message}
                                    </p>
                                  )}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {new Date(dataSource.created_at).toLocaleDateString()}
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="settings">
                    <Card>
                      <CardHeader>
                        <CardTitle>Knowledge Base Settings</CardTitle>
                        <CardDescription>
                          Manage your knowledge base configuration and preferences.
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between p-4 border rounded-lg">
                            <div>
                              <h4 className="font-medium">General Settings</h4>
                              <p className="text-sm text-gray-600">Name, description, and status</p>
                            </div>
                            <Button variant="outline" asChild>
                              <Link href={`/rag/${id}/edit`}>
                                <Settings className="mr-2 h-4 w-4" />
                                Configure
                              </Link>
                            </Button>
                          </div>
                          
                          <div className="flex items-center justify-between p-4 border rounded-lg opacity-50">
                            <div>
                              <h4 className="font-medium">RAG Configuration</h4>
                              <p className="text-sm text-gray-600">Chunk size, overlap, and embedding settings</p>
                            </div>
                            <Button variant="outline" disabled>
                              <Settings className="mr-2 h-4 w-4" />
                              Coming Soon
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="agents">
                    <Card>
                      <CardHeader>
                        <CardTitle>Connected Agents</CardTitle>
                        <CardDescription>
                          Agents that can access this knowledge base for RAG-powered responses.
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center py-8">
                          <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                          <h3 className="text-lg font-semibold mb-2">No connected agents</h3>
                          <p className="text-gray-600 mb-4">
                            Connect agents to this knowledge base to enable RAG-powered conversations.
                          </p>
                          <Button variant="outline" disabled>
                            Connect Agents (Coming Soon)
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}