'use client'

import { useState, useEffect } from 'react'
import { Search, Filter, Database, FileText, Clock, MoreHorizontal } from 'lucide-react'
import Link from 'next/link'
import { toast } from 'sonner'

import { AppSidebar } from '@/components/app-sidebar'
import { AuthGuard } from '@/components/auth-guard'
import { SiteHeader } from '@/components/dashboard/site-header'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { knowledgeBaseService } from '@/services/knowledgeBaseService'
import type { KnowledgeBase } from '@/types/knowledge-base'
import { DeleteKnowledgeBaseDialog } from '@/components/rag/delete-knowledge-base-dialog'

export default function RAGPage() {
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([])
  const [filteredKBs, setFilteredKBs] = useState<KnowledgeBase[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [kbToDelete, setKbToDelete] = useState<KnowledgeBase | null>(null)

  useEffect(() => {
    loadKnowledgeBases()
  }, [])

  useEffect(() => {
    filterKnowledgeBases()
  }, [knowledgeBases, searchQuery, statusFilter])

  const loadKnowledgeBases = async () => {
    try {
      setLoading(true)
      const data = await knowledgeBaseService.getKnowledgeBases()
      setKnowledgeBases(data)
    } catch (error) {
      console.error('Error loading knowledge bases:', error)
      toast.error('Failed to load knowledge bases')
    } finally {
      setLoading(false)
    }
  }

  const filterKnowledgeBases = () => {
    let filtered = knowledgeBases

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(kb => 
        kb.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        kb.description?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(kb => kb.status === statusFilter)
    }

    setFilteredKBs(filtered)
  }

  const handleDelete = (kb: KnowledgeBase) => {
    setKbToDelete(kb)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!kbToDelete) return

    try {
      await knowledgeBaseService.deleteKnowledgeBase(kbToDelete.id)
      toast.success('Knowledge base deleted successfully')
      loadKnowledgeBases()
    } catch (error) {
      console.error('Error deleting knowledge base:', error)
      toast.error('Failed to delete knowledge base')
    } finally {
      setDeleteDialogOpen(false)
      setKbToDelete(null)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const content = (
    <div className="space-y-6">
      {/* Description */}
      <div className="mb-6">
        <p className="text-muted-foreground">
          Manage your knowledge bases and documents for RAG-powered agents.
        </p>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search knowledge bases..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-32">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Knowledge Bases Grid */}
      {filteredKBs.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent className="space-y-4">
            <Database className="mx-auto h-12 w-12 text-gray-400" />
            <div>
              <h3 className="text-lg font-semibold">
                {knowledgeBases.length === 0 ? 'No knowledge bases yet' : 'No matching knowledge bases'}
              </h3>
              <p className="text-gray-600">
                {knowledgeBases.length === 0 
                  ? 'Create your first knowledge base to get started with RAG-powered agents.'
                  : 'Try adjusting your search or filters to find what you\'re looking for.'
                }
              </p>
            </div>
            {knowledgeBases.length === 0 && (
              <Button asChild>
                <Link href="/rag/new">
                  Create Knowledge Base
                </Link>
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredKBs.map((kb) => (
            <Card key={kb.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-3">
                <div className="space-y-1 flex-1">
                  <CardTitle className="text-lg line-clamp-1">
                    <Link 
                      href={`/rag/${kb.id}`}
                      className="hover:underline"
                    >
                      {kb.name}
                    </Link>
                  </CardTitle>
                  {kb.description && (
                    <CardDescription className="line-clamp-2">
                      {kb.description}
                    </CardDescription>
                  )}
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href={`/rag/${kb.id}/edit`}>
                        Edit
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href={`/rag/${kb.id}/documents`}>
                        Manage Documents
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      className="text-red-600"
                      onClick={() => handleDelete(kb)}
                    >
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  {getStatusBadge(kb.status)}
                  <div className="flex items-center text-sm text-gray-600">
                    <FileText className="mr-1 h-4 w-4" />
                    {kb.document_count || 0} docs
                  </div>
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <Clock className="mr-1 h-3 w-3" />
                  Created {new Date(kb.created_at).toLocaleDateString()}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Delete Dialog */}
      <DeleteKnowledgeBaseDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        knowledgeBase={kbToDelete}
        onConfirm={confirmDelete}
      />
    </div>
  )

  if (loading) {
    return (
      <AuthGuard>
        <SidebarProvider
          style={
            {
              "--sidebar-width": "calc(var(--spacing) * 72)",
              "--header-height": "calc(var(--spacing) * 12)",
            } as React.CSSProperties
          }
        >
          <AppSidebar variant="inset" />
          <SidebarInset>
            <SiteHeader
              title="Knowledge Bases"
              showCreateButton={true}
              createButtonText="New Knowledge Base"
              createButtonHref="/rag/new"
              createButtonPosition="left"
            />
            <div className="flex flex-1 flex-col">
              <div className="container mx-auto p-6">
                <div className="space-y-6">
                  <div className="flex gap-4 mb-6">
                    <Skeleton className="h-10 flex-1" />
                    <Skeleton className="h-10 w-32" />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {[...Array(6)].map((_, i) => (
                      <Card key={i}>
                        <CardHeader>
                          <Skeleton className="h-6 w-3/4 mb-2" />
                          <Skeleton className="h-4 w-full" />
                        </CardHeader>
                        <CardContent>
                          <div className="flex justify-between items-center">
                            <Skeleton className="h-6 w-20" />
                            <Skeleton className="h-6 w-16" />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </SidebarInset>
        </SidebarProvider>
      </AuthGuard>
    )
  }

  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader
            title="Knowledge Bases"
            showCreateButton={true}
            createButtonText="New Knowledge Base"
            createButtonHref="/rag/new"
            createButtonPosition="left"
          />
          <div className="flex flex-1 flex-col">
            <div className="container mx-auto p-6">
              {content}
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}