'use client'

import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

import { AppSidebar } from '@/components/app-sidebar'
import { AuthGuard } from '@/components/auth-guard'
import { SiteHeader } from '@/components/dashboard/site-header'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'
import { Button } from '@/components/ui/button'
import { KnowledgeBaseForm } from '@/components/rag/knowledge-base-form'

export default function NewKnowledgeBasePage() {
  return (
    <AuthGuard>
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader
            title="Create Knowledge Base"
            showCreateButton={false}
          />
          <div className="flex flex-1 flex-col">
            <div className="container mx-auto p-6">
              <div className="space-y-6">
                {/* Back Navigation */}
                <div className="flex items-center gap-4">
                  <Button variant="ghost" size="sm" asChild>
                    <Link href="/rag">
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back to Knowledge Bases
                    </Link>
                  </Button>
                </div>

                {/* Form */}
                <KnowledgeBaseForm mode="create" />
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}