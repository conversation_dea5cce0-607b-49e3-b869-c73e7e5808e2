# CRUSH.md

Repo: Next.js 15 + React 19 + TypeScript, Jest for unit/API tests, Playwright for e2e, ESLint flat config, strict TS.

Build/run
- Dev: npm run dev
- Build: npm run build
- Start: npm run start

Test
- All tests: npm test
- Watch: npm run test:watch
- CI w/ coverage: npm run test:ci
- API tests: npm run test:api
- UI/component tests: npm run test:ui
- Single test file: npx jest path/to/file.test.ts
- Single test name: npx jest path/to/file.test.ts -t "test name"

E2E
- Run: npm run e2e
- Headed: npm run e2e:headed
- Report: npm run e2e:report

Lint/typecheck/format
- ESLint: npx eslint .
- Typecheck: npx tsc --noEmit
- Formatting: use Prettier defaults if present; otherwise 2-space, single quotes, semicolons, trailing commas where valid.

Code style
- Imports: use path alias @/* per tsconfig.json; group std libs, third-party, internal; sort within groups; no default export unless required for Next routes.
- React/Next: client components with 'use client' as needed; hooks start with use; components PascalCase; files .tsx for JSX; no comments in code unless asked.
- Types: strict true; prefer explicit types on public APIs; use interfaces for object shapes, types for unions; avoid any; use unknown for untrusted.
- Naming: camelCase for vars/functions; PascalCase for components/types; UPPER_SNAKE_CASE for constants.
- Errors: never swallow; return typed Result or throw Error; in API routes respond with status and JSON { error, message }.
- Env/secrets: never log; read from process.env only on server; validate before use.
- Testing: colocate under tests/**, match *.test.ts(x); mock external IO; use runInBand for API tests if flakey.

Tools/config refs
- Jest: jest.config.ts (next/jest, jsdom, setup files)
- ESLint: eslint.config.mjs (next/core-web-vitals, next/typescript)
- TS paths: tsconfig.json paths { "@/*": ["./*"] }

Assistants
- No Cursor/Copilot rules found.
