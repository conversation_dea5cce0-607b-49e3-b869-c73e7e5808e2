'use client'

import { createClient } from '@supabase/supabase-js'

/**
 * Supabase Browser-Client
 * - liest die Public-Keys aus NEXT_PUBLIC_* Variablen
 * - sollte nur im Browser genutzt werden (Auth-Flow, Realtime etc.)
 */
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string | undefined
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string | undefined

if (!supabaseUrl || !supabaseAnonKey) {
  // Im Dev helfen wir mit klarer Fehlermeldung
  // Im Prod sollte die Build-/CI-Pipeline das bereits prüfen.
  console.warn(
    '[Supabase] Fehlende Env-Variablen: NEXT_PUBLIC_SUPABASE_URL oder NEXT_PUBLIC_SUPABASE_ANON_KEY. ' +
      'Bitte .env.local befüllen (siehe .env.local.example).'
  )
}

export const supabase = createClient(
  supabaseUrl ?? '',
  supabaseAnonKey ?? '',
  {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
  }
)