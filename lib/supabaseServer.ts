import 'server-only'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'

/**
 * Server-seitiger Supabase Client für Route Handler, Server Components, Actions.
 * Hin<PERSON>s zu Next.js 15: cookies() ist eine "Dynamic API" und muss in Routen/SSR awaited werden.
 * @supabase/ssr erwartet jedoch eine synchrone Cookie-Schnittstelle.
 *
 * Lösung:
 * - Wir geben die Cookies-Methoden als No-Op/lesende Brücke weiter:
 *   - get liest Cookie-Werte, ohne await zu benötigen (entwicklungsfreundlich).
 *   - set/remove sind No-Op, da <PERSON><PERSON> von <PERSON> in Route Handlern über die Response erfolgen sollte.
 * - Für Auth-Workflows, die Cookie-Setzen erfordern, nutze bitte Route Handler mit explizitem Setzen in der Response.
 */
export function createSupabaseServerClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error(
      '[Supabase] Fehlende Env-Variablen für Serverclient (NEXT_PUBLIC_SUPABASE_URL/NEXT_PUBLIC_SUPABASE_ANON_KEY)'
    )
  }

  const client = createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      get(name: string) {
        try {
          const jar = (cookies as unknown as () => { get: (n: string) => { value?: string } | undefined })()
          return jar.get(name)?.value
        } catch {
          return undefined
        }
      },
      set() {
        // No-Op: Cookies sollten in Next Route Handlern explizit über die Response gesetzt werden.
      },
      remove() {
        // No-Op
      },
    },
  })

  return client
}