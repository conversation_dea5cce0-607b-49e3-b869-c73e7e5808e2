import pdfParse from 'pdf-parse'
import mammoth from 'mammoth'
import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export interface ExtractedContent {
  text: string
  metadata: DocumentMetadata
}

export interface DocumentMetadata {
  title?: string
  author?: string
  created_date?: string
  page_count?: number
  word_count?: number
  language?: string
}

export interface DocumentChunk {
  content: string
  metadata: {
    chunk_index: number
    token_count: number
    page?: number
    section?: string
  }
}

export class DocumentProcessor {
  private maxChunkSize = 1000 // tokens
  private chunkOverlap = 200 // tokens

  async extractText(file: Buffer, fileType: string, originalFilename: string): Promise<ExtractedContent> {
    try {
      switch (fileType.toLowerCase()) {
        case 'pdf':
          return await this.extractFromPDF(file)
        case 'docx':
          return await this.extractFromDocx(file)
        case 'txt':
          return await this.extractFromTxt(file, originalFilename)
        case 'md':
          return await this.extractFromMarkdown(file, originalFilename)
        default:
          throw new Error(`Unsupported file type: ${fileType}`)
      }
    } catch (error) {
      console.error(`Error extracting text from ${fileType}:`, error)
      throw new Error(`Failed to extract text from ${fileType} file`)
    }
  }

  private async extractFromPDF(file: Buffer): Promise<ExtractedContent> {
    const pdfData = await pdfParse(file)
    
    return {
      text: pdfData.text,
      metadata: {
        page_count: pdfData.numpages,
        word_count: this.countWords(pdfData.text),
        title: pdfData.info?.Title,
        author: pdfData.info?.Author,
        created_date: pdfData.info?.CreationDate,
      }
    }
  }

  private async extractFromDocx(file: Buffer): Promise<ExtractedContent> {
    const result = await mammoth.extractRawText({ buffer: file })
    
    return {
      text: result.value,
      metadata: {
        word_count: this.countWords(result.value),
      }
    }
  }

  private async extractFromTxt(file: Buffer, filename: string): Promise<ExtractedContent> {
    const text = file.toString('utf-8')
    
    return {
      text,
      metadata: {
        word_count: this.countWords(text),
        title: filename.replace(/\.[^/.]+$/, ''), // Remove extension
      }
    }
  }

  private async extractFromMarkdown(file: Buffer, filename: string): Promise<ExtractedContent> {
    const text = file.toString('utf-8')
    const title = this.extractMarkdownTitle(text) || filename.replace(/\.[^/.]+$/, '')
    
    return {
      text,
      metadata: {
        word_count: this.countWords(text),
        title,
      }
    }
  }

  private extractMarkdownTitle(content: string): string | undefined {
    const match = content.match(/^#\s+(.+)/m)
    return match?.[1]?.trim()
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length
  }

  async generateChunks(content: string): Promise<DocumentChunk[]> {
    const sentences = this.splitIntoSentences(content)
    const chunks: DocumentChunk[] = []
    let currentChunk = ''
    let currentTokenCount = 0
    let chunkIndex = 0

    for (const sentence of sentences) {
      const sentenceTokenCount = this.estimateTokenCount(sentence)
      
      // If adding this sentence would exceed the max chunk size, save current chunk
      if (currentTokenCount + sentenceTokenCount > this.maxChunkSize && currentChunk.length > 0) {
        chunks.push({
          content: currentChunk.trim(),
          metadata: {
            chunk_index: chunkIndex,
            token_count: currentTokenCount,
          }
        })
        
        // Start new chunk with overlap from previous chunk
        const overlapText = this.getOverlapText(currentChunk, this.chunkOverlap)
        currentChunk = overlapText + sentence
        currentTokenCount = this.estimateTokenCount(currentChunk)
        chunkIndex++
      } else {
        currentChunk += sentence
        currentTokenCount += sentenceTokenCount
      }
    }

    // Add the last chunk if it has content
    if (currentChunk.trim().length > 0) {
      chunks.push({
        content: currentChunk.trim(),
        metadata: {
          chunk_index: chunkIndex,
          token_count: currentTokenCount,
        }
      })
    }

    return chunks
  }

  private splitIntoSentences(text: string): string[] {
    // Simple sentence splitting - could be improved with more sophisticated NLP
    return text
      .split(/(?<=[.!?])\s+/)
      .filter(sentence => sentence.trim().length > 0)
      .map(sentence => sentence + ' ')
  }

  private estimateTokenCount(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4)
  }

  private getOverlapText(text: string, maxTokens: number): string {
    const maxChars = maxTokens * 4 // Rough estimation
    if (text.length <= maxChars) return text
    
    // Find a good breaking point (end of sentence) within the overlap range
    const overlapText = text.slice(-maxChars)
    const lastSentenceEnd = Math.max(
      overlapText.lastIndexOf('. '),
      overlapText.lastIndexOf('! '),
      overlapText.lastIndexOf('? ')
    )
    
    if (lastSentenceEnd > 0) {
      return overlapText.slice(lastSentenceEnd + 2)
    }
    
    return overlapText
  }

  async generateEmbeddings(chunks: string[]): Promise<number[][]> {
    try {
      const embeddings: number[][] = []
      
      // Process chunks in batches to avoid rate limits
      const batchSize = 10
      for (let i = 0; i < chunks.length; i += batchSize) {
        const batch = chunks.slice(i, i + batchSize)
        
        const response = await openai.embeddings.create({
          model: 'text-embedding-ada-002',
          input: batch,
        })
        
        const batchEmbeddings = response.data.map(item => item.embedding)
        embeddings.push(...batchEmbeddings)
        
        // Small delay to avoid rate limiting
        if (i + batchSize < chunks.length) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }
      
      return embeddings
    } catch (error) {
      console.error('Error generating embeddings:', error)
      throw new Error('Failed to generate embeddings')
    }
  }

  async generateQueryEmbedding(query: string): Promise<number[]> {
    try {
      const response = await openai.embeddings.create({
        model: 'text-embedding-ada-002',
        input: query,
      })
      
      return response.data[0].embedding
    } catch (error) {
      console.error('Error generating query embedding:', error)
      throw new Error('Failed to generate query embedding')
    }
  }
}

export const documentProcessor = new DocumentProcessor()