'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { Loader2 } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'

import { knowledgeBaseService } from '@/services/knowledgeBaseService'
import type { KnowledgeBase, CreateKnowledgeBaseRequest, UpdateKnowledgeBaseRequest } from '@/types/knowledge-base'

interface KnowledgeBaseFormProps {
  knowledgeBase?: KnowledgeBase | null
  mode: 'create' | 'edit'
}

export function KnowledgeBaseForm({ knowledgeBase, mode }: KnowledgeBaseFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: knowledgeBase?.name || '',
    description: knowledgeBase?.description || '',
    status: knowledgeBase?.status || 'active'
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      toast.error('Name is required')
      return
    }

    if (formData.name.length > 100) {
      toast.error('Name must be less than 100 characters')
      return
    }

    try {
      setLoading(true)
      
      if (mode === 'create') {
        const data: CreateKnowledgeBaseRequest = {
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          status: formData.status as 'active' | 'inactive'
        }
        
        const newKB = await knowledgeBaseService.createKnowledgeBase(data)
        toast.success('Knowledge base created successfully')
        router.push(`/rag/${newKB.id}`)
      } else if (knowledgeBase) {
        const data: UpdateKnowledgeBaseRequest = {
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          status: formData.status as 'active' | 'inactive'
        }
        
        await knowledgeBaseService.updateKnowledgeBase(knowledgeBase.id, data)
        toast.success('Knowledge base updated successfully')
        router.push(`/rag/${knowledgeBase.id}`)
      }
    } catch (error) {
      console.error('Error saving knowledge base:', error)
      toast.error(mode === 'create' ? 'Failed to create knowledge base' : 'Failed to update knowledge base')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (knowledgeBase) {
      router.push(`/rag/${knowledgeBase.id}`)
    } else {
      router.push('/rag')
    }
  }

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>
            {mode === 'create' ? 'Create Knowledge Base' : 'Edit Knowledge Base'}
          </CardTitle>
          <CardDescription>
            {mode === 'create' 
              ? 'Create a new knowledge base to organize your documents and make them searchable for your agents.'
              : 'Update your knowledge base settings and configuration.'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Name */}
            <div className="space-y-2">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                placeholder="Enter knowledge base name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                disabled={loading}
                maxLength={100}
                required
              />
              <p className="text-xs text-muted-foreground">
                A descriptive name for your knowledge base (max 100 characters)
              </p>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe what this knowledge base contains..."
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                disabled={loading}
                rows={3}
                maxLength={500}
              />
              <p className="text-xs text-muted-foreground">
                Optional description to help you organize your knowledge bases
              </p>
            </div>

            {/* Status */}
            <div className="space-y-3">
              <Label>Status</Label>
              <div className="flex items-center space-x-3">
                <Switch
                  checked={formData.status === 'active'}
                  onCheckedChange={(checked) => 
                    setFormData(prev => ({ ...prev, status: checked ? 'active' : 'inactive' }))
                  }
                  disabled={loading}
                />
                <div className="space-y-1">
                  <p className="text-sm font-medium">
                    {formData.status === 'active' ? 'Active' : 'Inactive'}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formData.status === 'active' 
                      ? 'This knowledge base can be used by agents'
                      : 'This knowledge base is disabled and cannot be used by agents'
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-3 pt-4">
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {mode === 'create' ? 'Create Knowledge Base' : 'Save Changes'}
              </Button>
              <Button type="button" variant="outline" onClick={handleCancel} disabled={loading}>
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}