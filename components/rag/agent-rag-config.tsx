'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Database, 
  Plus, 
  Trash2, 
  <PERSON>tings, 
  Zap,
  Search,
  FileText,
  Loader2,
  AlertCircle
} from 'lucide-react'

interface KnowledgeBase {
  id: string
  name: string
  description?: string
  status: 'active' | 'inactive'
  document_count?: number
  created_at: string
}

interface LinkedKnowledgeBase {
  id: string
  priority: number
  created_at: string
  knowledge_bases: KnowledgeBase
}

interface RAGConfig {
  agent_id: string
  enabled: boolean
  similarity_threshold: number
  max_chunks: number
  chunk_overlap: boolean
  include_metadata: boolean
  source_attribution: boolean
}

interface RAGTestResult {
  query: string
  chunks: any[]
  context: string
  sources: any[]
  total_tokens: number
}

interface AgentRAGConfigProps {
  agentId: string
}

export function AgentRAGConfig({ agentId }: AgentRAGConfigProps) {
  const [linkedKBs, setLinkedKBs] = useState<LinkedKnowledgeBase[]>([])
  const [availableKBs, setAvailableKBs] = useState<KnowledgeBase[]>([])
  const [ragConfig, setRAGConfig] = useState<RAGConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [linkDialogOpen, setLinkDialogOpen] = useState(false)
  const [selectedKBId, setSelectedKBId] = useState<string>('')
  
  // Testing state
  const [testDialogOpen, setTestDialogOpen] = useState(false)
  const [testQuery, setTestQuery] = useState('')
  const [testResult, setTestResult] = useState<RAGTestResult | null>(null)
  const [testing, setTesting] = useState(false)

  useEffect(() => {
    fetchData()
  }, [agentId])

  const fetchData = async () => {
    try {
      setLoading(true)
      await Promise.all([
        fetchLinkedKnowledgeBases(),
        fetchAvailableKnowledgeBases(),
        fetchRAGConfig()
      ])
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchLinkedKnowledgeBases = async () => {
    try {
      const response = await fetch(`/api/agents/${agentId}/knowledge-bases`)
      if (response.ok) {
        const data = await response.json()
        setLinkedKBs(data)
      }
    } catch (error) {
      console.error('Error fetching linked knowledge bases:', error)
    }
  }

  const fetchAvailableKnowledgeBases = async () => {
    try {
      const response = await fetch('/api/knowledge-bases')
      if (response.ok) {
        const data = await response.json()
        setAvailableKBs(data.filter((kb: KnowledgeBase) => kb.status === 'active'))
      }
    } catch (error) {
      console.error('Error fetching available knowledge bases:', error)
    }
  }

  const fetchRAGConfig = async () => {
    try {
      const response = await fetch(`/api/agents/${agentId}/rag-config`)
      if (response.ok) {
        const data = await response.json()
        setRAGConfig(data)
      }
    } catch (error) {
      console.error('Error fetching RAG config:', error)
    }
  }

  const handleLinkKnowledgeBase = async () => {
    if (!selectedKBId) return

    try {
      const response = await fetch(`/api/agents/${agentId}/knowledge-bases`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          knowledge_base_id: selectedKBId,
          priority: linkedKBs.length + 1 
        })
      })

      if (response.ok) {
        await fetchLinkedKnowledgeBases()
        setLinkDialogOpen(false)
        setSelectedKBId('')
      } else {
        const errorData = await response.json()
        console.error('Error linking knowledge base:', errorData.error)
      }
    } catch (error) {
      console.error('Error linking knowledge base:', error)
    }
  }

  const handleUnlinkKnowledgeBase = async (kbId: string) => {
    try {
      const response = await fetch(`/api/agents/${agentId}/knowledge-bases/${kbId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        await fetchLinkedKnowledgeBases()
      }
    } catch (error) {
      console.error('Error unlinking knowledge base:', error)
    }
  }

  const handleUpdateRAGConfig = async (updates: Partial<RAGConfig>) => {
    if (!ragConfig) return

    try {
      setSaving(true)
      const response = await fetch(`/api/agents/${agentId}/rag-config`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      })

      if (response.ok) {
        const updatedConfig = await response.json()
        setRAGConfig({ ...ragConfig, ...updatedConfig })
      }
    } catch (error) {
      console.error('Error updating RAG config:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleTestRAG = async () => {
    if (!testQuery.trim()) return

    try {
      setTesting(true)
      const response = await fetch(`/api/agents/${agentId}/rag-query`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: testQuery })
      })

      if (response.ok) {
        const result = await response.json()
        setTestResult(result)
      } else {
        const errorData = await response.json()
        console.error('Error testing RAG:', errorData.error)
      }
    } catch (error) {
      console.error('Error testing RAG:', error)
    } finally {
      setTesting(false)
    }
  }

  const unlinkedKBs = availableKBs.filter(kb => 
    !linkedKBs.some(linked => linked.knowledge_bases.id === kb.id)
  )

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="py-8">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* RAG Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            RAG Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {ragConfig && (
            <>
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base font-medium">Enable RAG</Label>
                  <p className="text-sm text-gray-500">
                    Allow this agent to use knowledge bases for enhanced responses
                  </p>
                </div>
                <Switch
                  checked={ragConfig.enabled}
                  onCheckedChange={(enabled) => handleUpdateRAGConfig({ enabled })}
                  disabled={saving}
                />
              </div>

              {ragConfig.enabled && (
                <div className="space-y-6 pt-4 border-t">
                  <div className="space-y-3">
                    <Label>Similarity Threshold: {ragConfig.similarity_threshold}</Label>
                    <p className="text-sm text-gray-500">
                      Minimum relevance score for including document chunks
                    </p>
                    <Slider
                      value={[ragConfig.similarity_threshold]}
                      onValueChange={([value]) => 
                        handleUpdateRAGConfig({ similarity_threshold: value })
                      }
                      min={0}
                      max={1}
                      step={0.05}
                      disabled={saving}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-3">
                    <Label>Maximum Chunks: {ragConfig.max_chunks}</Label>
                    <p className="text-sm text-gray-500">
                      Maximum number of document chunks to include in responses
                    </p>
                    <Slider
                      value={[ragConfig.max_chunks]}
                      onValueChange={([value]) => 
                        handleUpdateRAGConfig({ max_chunks: value })
                      }
                      min={1}
                      max={20}
                      step={1}
                      disabled={saving}
                      className="w-full"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Include Metadata</Label>
                        <p className="text-xs text-gray-500">
                          Include document metadata in responses
                        </p>
                      </div>
                      <Switch
                        checked={ragConfig.include_metadata}
                        onCheckedChange={(include_metadata) => 
                          handleUpdateRAGConfig({ include_metadata })
                        }
                        disabled={saving}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Source Attribution</Label>
                        <p className="text-xs text-gray-500">
                          Show source information in responses
                        </p>
                      </div>
                      <Switch
                        checked={ragConfig.source_attribution}
                        onCheckedChange={(source_attribution) => 
                          handleUpdateRAGConfig({ source_attribution })
                        }
                        disabled={saving}
                      />
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Linked Knowledge Bases */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Linked Knowledge Bases ({linkedKBs.length})
            </div>
            <div className="flex gap-2">
              <Dialog open={testDialogOpen} onOpenChange={setTestDialogOpen}>
                <DialogTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm"
                    disabled={!ragConfig?.enabled || linkedKBs.length === 0}
                  >
                    <Search className="h-4 w-4 mr-2" />
                    Test RAG
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Test RAG Search</DialogTitle>
                    <DialogDescription>
                      Test how your agent will search and retrieve information from linked knowledge bases.
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="test-query">Test Query</Label>
                      <Textarea
                        id="test-query"
                        placeholder="Ask a question to test RAG search..."
                        value={testQuery}
                        onChange={(e) => setTestQuery(e.target.value)}
                        rows={3}
                      />
                    </div>
                    
                    <Button 
                      onClick={handleTestRAG}
                      disabled={testing || !testQuery.trim()}
                      className="w-full"
                    >
                      {testing ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Searching...
                        </>
                      ) : (
                        <>
                          <Search className="h-4 w-4 mr-2" />
                          Search
                        </>
                      )}
                    </Button>

                    {testResult && (
                      <div className="space-y-4 border-t pt-4">
                        <div>
                          <h4 className="font-medium mb-2">Results ({testResult.chunks.length} chunks found)</h4>
                          <div className="text-sm text-gray-600 mb-4">
                            Total tokens: {testResult.total_tokens} | Sources: {testResult.sources.length}
                          </div>
                        </div>

                        {testResult.chunks.length > 0 ? (
                          <div className="space-y-3">
                            {testResult.chunks.slice(0, 3).map((chunk, index) => (
                              <Card key={index} className="border-l-4 border-l-blue-500">
                                <CardContent className="pt-4">
                                  <div className="flex items-center justify-between mb-2">
                                    <Badge variant="outline">
                                      {chunk.metadata.source_name}
                                    </Badge>
                                    <span className="text-xs text-gray-500">
                                      {(chunk.similarity_score * 100).toFixed(1)}% match
                                    </span>
                                  </div>
                                  <p className="text-sm">
                                    {chunk.content.substring(0, 200)}...
                                  </p>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            <AlertCircle className="h-12 w-12 mx-auto mb-4" />
                            <p>No relevant chunks found for this query.</p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </DialogContent>
              </Dialog>

              <Dialog open={linkDialogOpen} onOpenChange={setLinkDialogOpen}>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Link Knowledge Base
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Link Knowledge Base</DialogTitle>
                    <DialogDescription>
                      Choose a knowledge base to link with this agent.
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="py-4">
                    <Label htmlFor="kb-select">Knowledge Base</Label>
                    <Select value={selectedKBId} onValueChange={setSelectedKBId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a knowledge base" />
                      </SelectTrigger>
                      <SelectContent>
                        {unlinkedKBs.map(kb => (
                          <SelectItem key={kb.id} value={kb.id}>
                            <div className="flex items-center gap-2">
                              <Database className="h-4 w-4" />
                              <div>
                                <div className="font-medium">{kb.name}</div>
                                {kb.description && (
                                  <div className="text-xs text-gray-500">{kb.description}</div>
                                )}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <DialogFooter>
                    <Button
                      onClick={handleLinkKnowledgeBase}
                      disabled={!selectedKBId}
                    >
                      Link Knowledge Base
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {linkedKBs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No knowledge bases linked yet.</p>
              <p className="text-sm">Link knowledge bases to enable RAG functionality.</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Knowledge Base</TableHead>
                  <TableHead>Documents</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {linkedKBs.map((linked) => (
                  <TableRow key={linked.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Database className="h-4 w-4 text-gray-500" />
                        <div>
                          <div className="font-medium">{linked.knowledge_bases.name}</div>
                          {linked.knowledge_bases.description && (
                            <div className="text-sm text-gray-500">
                              {linked.knowledge_bases.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        <FileText className="h-3 w-3 mr-1" />
                        {linked.knowledge_bases.document_count || 0}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={linked.knowledge_bases.status === 'active' ? 'default' : 'secondary'}>
                        {linked.knowledge_bases.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        #{linked.priority}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUnlinkKnowledgeBase(linked.knowledge_bases.id)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}