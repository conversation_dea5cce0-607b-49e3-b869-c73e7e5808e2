'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { 
  FileText, 
  Download, 
  ExternalLink,
  Loader2,
  AlertCircle,
  FileCode,
  Hash,
  Calendar,
  User,
  FileIcon,
  Layers
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface DataSource {
  id: string
  name: string
  original_filename: string
  file_type: 'pdf' | 'docx' | 'txt' | 'md'
  file_size: number
  file_url: string
  status: 'uploading' | 'processing' | 'ready' | 'error'
  error_message?: string
  metadata: any
  chunk_count: number
  created_at: string
  updated_at: string
}

interface DocumentChunk {
  id: string
  content: string
  metadata: {
    chunk_index: number
    token_count: number
    page?: number
    section?: string
  }
}

interface DocumentPreviewProps {
  document: DataSource
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function DocumentPreview({ document, open, onOpenChange }: DocumentPreviewProps) {
  const [chunks, setChunks] = useState<DocumentChunk[]>([])
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (open && document.status === 'ready') {
      fetchChunks()
    }
  }, [open, document.id, document.status])

  const fetchChunks = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/data-sources/${document.id}/chunks`)
      if (response.ok) {
        const data = await response.json()
        setChunks(data.slice(0, 10)) // First 10 chunks
      }
    } catch (error) {
      console.error('Error fetching chunks:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileTypeIcon = () => {
    switch (document.file_type) {
      case 'pdf':
        return <FileText className="h-5 w-5 text-red-500" />
      case 'docx':
        return <FileText className="h-5 w-5 text-blue-500" />
      case 'md':
        return <FileCode className="h-5 w-5 text-gray-600" />
      default:
        return <FileIcon className="h-5 w-5 text-gray-500" />
    }
  }

  const handleDownload = () => {
    const link = document.createElement('a')
    link.href = document.file_url
    link.download = document.original_filename
    link.click()
  }

  const handleViewOriginal = () => {
    window.open(document.file_url, '_blank')
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            {getFileTypeIcon()}
            <div>
              <div className="font-semibold">{document.name}</div>
              <div className="text-sm text-gray-500 font-normal">
                {document.original_filename}
              </div>
            </div>
          </DialogTitle>
          <DialogDescription>
            Document preview and details
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="chunks" disabled={document.status !== 'ready'}>
                Chunks ({document.chunk_count})
              </TabsTrigger>
              <TabsTrigger value="metadata">Metadata</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">File Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Type:</span>
                      <Badge variant="outline">
                        {document.file_type.toUpperCase()}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Size:</span>
                      <span className="text-sm font-medium">
                        {formatFileSize(document.file_size)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Status:</span>
                      <Badge variant={document.status === 'ready' ? 'default' : 'secondary'}>
                        {document.status}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Processing Results</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Chunks:</span>
                      <span className="text-sm font-medium">{document.chunk_count}</span>
                    </div>
                    {document.metadata?.word_count && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Words:</span>
                        <span className="text-sm font-medium">
                          {document.metadata.word_count.toLocaleString()}
                        </span>
                      </div>
                    )}
                    {document.metadata?.page_count && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Pages:</span>
                        <span className="text-sm font-medium">
                          {document.metadata.page_count}
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">Timestamps</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-500">Uploaded:</span>
                    <span className="text-sm font-medium">
                      {formatDistanceToNow(new Date(document.created_at), { addSuffix: true })}
                    </span>
                  </div>
                  {document.updated_at !== document.created_at && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-500">Updated:</span>
                      <span className="text-sm font-medium">
                        {formatDistanceToNow(new Date(document.updated_at), { addSuffix: true })}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {document.error_message && (
                <Card className="border-red-200">
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-2">
                      <AlertCircle className="h-4 w-4 text-red-500 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-red-800">Processing Error</p>
                        <p className="text-sm text-red-600 mt-1">
                          {document.error_message}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              <div className="flex gap-2">
                <Button onClick={handleDownload} variant="outline" className="flex-1">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
                <Button onClick={handleViewOriginal} variant="outline" className="flex-1">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Original
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="chunks" className="mt-4">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : chunks.length > 0 ? (
                <ScrollArea className="h-96">
                  <div className="space-y-4">
                    {chunks.map((chunk, index) => (
                      <Card key={chunk.id} className="border-l-4 border-l-blue-500">
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Hash className="h-4 w-4" />
                              Chunk {chunk.metadata.chunk_index + 1}
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {chunk.metadata.token_count} tokens
                            </Badge>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-gray-700 whitespace-pre-wrap">
                            {chunk.content.substring(0, 500)}
                            {chunk.content.length > 500 && '...'}
                          </p>
                          {chunk.metadata.page && (
                            <p className="text-xs text-gray-500 mt-2">
                              Page {chunk.metadata.page}
                            </p>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                    {chunks.length >= 10 && (
                      <p className="text-sm text-gray-500 text-center py-4">
                        Showing first 10 chunks of {document.chunk_count}
                      </p>
                    )}
                  </div>
                </ScrollArea>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Layers className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No chunks available</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="metadata" className="mt-4">
              <ScrollArea className="h-96">
                <Card>
                  <CardContent className="pt-6">
                    {document.metadata && Object.keys(document.metadata).length > 0 ? (
                      <div className="space-y-3">
                        {Object.entries(document.metadata).map(([key, value]) => (
                          <div key={key} className="flex justify-between items-start">
                            <span className="text-sm text-gray-500 capitalize">
                              {key.replace(/_/g, ' ')}:
                            </span>
                            <span className="text-sm font-medium text-right max-w-[60%] break-words">
                              {typeof value === 'object' 
                                ? JSON.stringify(value, null, 2)
                                : String(value)
                              }
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No metadata available</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  )
}