'use client'

import { useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Upload, File, X, AlertCircle, CheckCircle } from 'lucide-react'

interface DocumentUploadProps {
  knowledgeBaseId: string
  onUploadComplete?: () => void
}

interface UploadingFile {
  file: File
  name: string
  progress: number
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error'
  error?: string
  id?: string
}

const ALLOWED_TYPES = {
  'application/pdf': 'PDF',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'DOCX',
  'text/plain': 'TXT',
  'text/markdown': 'MD'
}

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

export function DocumentUpload({ knowledgeBaseId, onUploadComplete }: DocumentUploadProps) {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([])
  const [dragActive, setDragActive] = useState(false)
  const router = useRouter()

  const validateFile = (file: File): string | null => {
    if (!Object.keys(ALLOWED_TYPES).includes(file.type)) {
      return `Unsupported file type: ${file.type}. Only PDF, DOCX, TXT, and MD files are allowed.`
    }
    
    if (file.size > MAX_FILE_SIZE) {
      return `File too large: ${(file.size / 1024 / 1024).toFixed(1)}MB. Maximum size is 10MB.`
    }
    
    return null
  }

  const handleFiles = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files)
    const newUploadingFiles: UploadingFile[] = []
    
    for (const file of fileArray) {
      const error = validateFile(file)
      if (error) {
        newUploadingFiles.push({
          file,
          name: file.name,
          progress: 0,
          status: 'error',
          error
        })
      } else {
        newUploadingFiles.push({
          file,
          name: file.name,
          progress: 0,
          status: 'pending'
        })
      }
    }
    
    setUploadingFiles(prev => [...prev, ...newUploadingFiles])
    
    // Start uploading valid files
    newUploadingFiles
      .filter(uf => uf.status === 'pending')
      .forEach(uploadFile => startUpload(uploadFile))
  }, [knowledgeBaseId])

  const startUpload = async (uploadFile: UploadingFile) => {
    try {
      // Update status to uploading
      setUploadingFiles(prev => 
        prev.map(uf => 
          uf === uploadFile 
            ? { ...uf, status: 'uploading', progress: 10 }
            : uf
        )
      )

      const formData = new FormData()
      formData.append('file', uploadFile.file)
      formData.append('name', uploadFile.name)

      const response = await fetch(`/api/knowledge-bases/${knowledgeBaseId}/data-sources`, {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      const dataSource = await response.json()

      // Update status to processing
      setUploadingFiles(prev => 
        prev.map(uf => 
          uf === uploadFile 
            ? { 
                ...uf, 
                status: 'processing', 
                progress: 50,
                id: dataSource.id 
              }
            : uf
        )
      )

      // Poll for completion
      pollForCompletion(uploadFile, dataSource.id)

    } catch (error) {
      console.error('Upload error:', error)
      setUploadingFiles(prev => 
        prev.map(uf => 
          uf === uploadFile 
            ? { 
                ...uf, 
                status: 'error', 
                error: error instanceof Error ? error.message : 'Upload failed'
              }
            : uf
        )
      )
    }
  }

  const pollForCompletion = async (uploadFile: UploadingFile, dataSourceId: string) => {
    const maxAttempts = 60 // 5 minutes with 5-second intervals
    let attempts = 0

    const poll = async () => {
      try {
        const response = await fetch(`/api/data-sources/${dataSourceId}`)
        if (!response.ok) throw new Error('Failed to check status')
        
        const dataSource = await response.json()
        
        if (dataSource.status === 'ready') {
          setUploadingFiles(prev => 
            prev.map(uf => 
              uf === uploadFile 
                ? { ...uf, status: 'completed', progress: 100 }
                : uf
            )
          )
          onUploadComplete?.()
          return
        }
        
        if (dataSource.status === 'error') {
          setUploadingFiles(prev => 
            prev.map(uf => 
              uf === uploadFile 
                ? { 
                    ...uf, 
                    status: 'error', 
                    error: dataSource.error_message || 'Processing failed'
                  }
                : uf
            )
          )
          return
        }
        
        // Still processing
        attempts++
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000) // Check again in 5 seconds
        } else {
          throw new Error('Processing timeout')
        }
      } catch (error) {
        console.error('Error checking status:', error)
        setUploadingFiles(prev => 
          prev.map(uf => 
            uf === uploadFile 
              ? { 
                  ...uf, 
                  status: 'error', 
                  error: error instanceof Error ? error.message : 'Status check failed'
                }
              : uf
          )
        )
      }
    }

    poll()
  }

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files)
    }
  }, [handleFiles])

  const removeFile = (fileToRemove: UploadingFile) => {
    setUploadingFiles(prev => prev.filter(uf => uf !== fileToRemove))
  }

  const clearCompleted = () => {
    setUploadingFiles(prev => prev.filter(uf => uf.status !== 'completed'))
  }

  const getStatusIcon = (status: UploadingFile['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case 'processing':
      case 'uploading':
        return <div className="h-4 w-4 rounded-full border-2 border-blue-500 border-t-transparent animate-spin" />
      default:
        return <File className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusText = (status: UploadingFile['status']) => {
    switch (status) {
      case 'pending':
        return 'Pending'
      case 'uploading':
        return 'Uploading...'
      case 'processing':
        return 'Processing...'
      case 'completed':
        return 'Completed'
      case 'error':
        return 'Error'
      default:
        return 'Unknown'
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload Documents
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragActive 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-lg font-medium text-gray-700">
            Drop files here or click to browse
          </p>
          <p className="text-sm text-gray-500 mt-1">
            Supports PDF, DOCX, TXT, and MD files up to 10MB
          </p>
          
          <Input
            type="file"
            multiple
            accept=".pdf,.docx,.txt,.md"
            onChange={(e) => {
              if (e.target.files) {
                handleFiles(e.target.files)
              }
            }}
            className="mt-4 max-w-xs mx-auto"
          />
        </div>

        {uploadingFiles.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Upload Progress</Label>
              <Button 
                variant="outline" 
                size="sm"
                onClick={clearCompleted}
                disabled={!uploadingFiles.some(uf => uf.status === 'completed')}
              >
                Clear Completed
              </Button>
            </div>
            
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {uploadingFiles.map((uploadFile, index) => (
                <div key={index} className="border rounded p-3 space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(uploadFile.status)}
                      <span className="text-sm font-medium truncate">
                        {uploadFile.name}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-gray-500">
                        {getStatusText(uploadFile.status)}
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(uploadFile)}
                        className="h-6 w-6 p-0"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  
                  {(uploadFile.status === 'uploading' || uploadFile.status === 'processing') && (
                    <Progress value={uploadFile.progress} className="h-2" />
                  )}
                  
                  {uploadFile.error && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-xs">
                        {uploadFile.error}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}