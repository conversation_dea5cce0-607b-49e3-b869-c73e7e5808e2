// Zentraler HTTP-Client auf Basis von native fetch
// - <PERSON>t die <PERSON>sis-URL aus Environment (NEXT_PUBLIC_API_BASE_URL)
// - Vereinheitlichte Header, JSON-Parsing, Fehlerbehandlung
// - Throwt Errors mit status, code und message

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

export interface HttpError extends Error {
  status?: number;
  code?: string;
  details?: unknown;
}

export interface RequestOptions<TBody = unknown> extends RequestInit {
  json?: TBody;
  searchParams?: Record<string, string | number | boolean | undefined | null>;
  headers?: HeadersInit;
}

const getBaseUrl = (): string => {
  // Für Browser-seitige Aufrufe bevorzugen wir relative Pfade zu Next.js API-Routes.
  // Wenn NEXT_PUBLIC_API_BASE_URL gesetzt ist, wird diese genutzt (z. B. bei separatem Backend).
  const env = typeof window === 'undefined'
    ? process.env.NEXT_PUBLIC_API_BASE_URL
    : (window as any)?.__NEXT_PUBLIC_API_BASE_URL__ ?? process.env.NEXT_PUBLIC_API_BASE_URL;

  // Fallback auf leere Basis-URL (relative Aufrufe an /api/...)
  return (env && env.trim().length > 0) ? env : '';
};

const buildUrl = (path: string, searchParams?: RequestOptions['searchParams']): string => {
  const base = getBaseUrl();
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  const url = `${base}${normalizedPath}`;

  if (!searchParams) return url;

  const usp = new URLSearchParams();
  Object.entries(searchParams).forEach(([k, v]) => {
    if (v === undefined || v === null) return;
    usp.set(k, String(v));
  });

  const qs = usp.toString();
  return qs ? `${url}?${qs}` : url;
};

const defaultHeaders: HeadersInit = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

const parseJsonSafe = async (res: Response): Promise<any> => {
  const text = await res.text();
  try {
    return text ? JSON.parse(text) : null;
  } catch {
    return text; // Fallback: roher Text
  }
};

const ensureOk = async (res: Response): Promise<void> => {
  if (res.ok) return;

  const body = await parseJsonSafe(res);
  const err: HttpError = new Error(
    (body && (body.message || body.error || body.reason)) ||
    `HTTP ${res.status}`
  );
  err.status = res.status;
  if (body && typeof body === 'object') {
    err.code = (body as any).code;
    err.details = body;
  }
  throw err;
};

export async function http<TResponse = any, TBody = unknown>(
  path: string,
  method: HttpMethod,
  options: RequestOptions<TBody> = {}
): Promise<TResponse> {
  const { json, searchParams, headers, ...rest } = options;

  const url = buildUrl(path, searchParams);
  const init: RequestInit = {
    method,
    headers: { ...defaultHeaders, ...(headers || {}) },
    ...rest,
  };

  if (json !== undefined) {
    (init.headers as Record<string, string>)['Content-Type'] = 'application/json';
    init.body = JSON.stringify(json);
  }

  const res = await fetch(url, init);
  await ensureOk(res);

  // Versuche JSON zu parsen, fallback auf Text
  const data = await parseJsonSafe(res);
  return data as TResponse;
}

// Komfort-Methoden
export const httpGet = <TResponse = any>(path: string, options?: RequestOptions) =>
  http<TResponse>(path, 'GET', options);

export const httpPost = <TResponse = any, TBody = unknown>(path: string, json?: TBody, options?: Omit<RequestOptions<TBody>, 'json'>) =>
  http<TResponse, TBody>(path, 'POST', { ...(options || {}), json });

export const httpPut = <TResponse = any, TBody = unknown>(path: string, json?: TBody, options?: Omit<RequestOptions<TBody>, 'json'>) =>
  http<TResponse, TBody>(path, 'PUT', { ...(options || {}), json });

export const httpPatch = <TResponse = any, TBody = unknown>(path: string, json?: TBody, options?: Omit<RequestOptions<TBody>, 'json'>) =>
  http<TResponse, TBody>(path, 'PATCH', { ...(options || {}), json });

export const httpDelete = <TResponse = any>(path: string, options?: RequestOptions) =>
  http<TResponse>(path, 'DELETE', options);