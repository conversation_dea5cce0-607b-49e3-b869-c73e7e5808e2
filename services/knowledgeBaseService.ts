import { supabase } from '@/lib/supabase'
import { httpGet, httpPost, httpPut, httpDelete } from './http'
import type { 
  KnowledgeBase, 
  CreateKnowledgeBaseRequest, 
  UpdateKnowledgeBaseRequest,
  DataSource
} from '@/types/knowledge-base'

export class KnowledgeBaseService {
  async getKnowledgeBases(): Promise<KnowledgeBase[]> {
    return await httpGet<KnowledgeBase[]>('/api/knowledge-bases')
  }

  async getKnowledgeBase(id: string): Promise<KnowledgeBase> {
    return await httpGet<KnowledgeBase>(`/api/knowledge-bases/${id}`)
  }

  async createKnowledgeBase(data: CreateKnowledgeBaseRequest): Promise<KnowledgeBase> {
    return await httpPost<KnowledgeBase>('/api/knowledge-bases', data)
  }

  async updateKnowledgeBase(id: string, data: UpdateKnowledgeBaseRequest): Promise<KnowledgeBase> {
    return await httpPut<KnowledgeBase>(`/api/knowledge-bases/${id}`, data)
  }

  async deleteKnowledgeBase(id: string): Promise<void> {
    await httpDelete(`/api/knowledge-bases/${id}`)
  }

  async getDataSources(knowledgeBaseId: string): Promise<DataSource[]> {
    return await httpGet<DataSource[]>(`/api/knowledge-bases/${knowledgeBaseId}/data-sources`)
  }

  async uploadDocument(knowledgeBaseId: string, file: File): Promise<DataSource> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('knowledge_base_id', knowledgeBaseId)

    // For file uploads, we need to use fetch directly
    const response = await fetch(`/api/knowledge-bases/${knowledgeBaseId}/documents/upload`, {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Upload failed')
    }

    return await response.json()
  }

  async deleteDataSource(id: string): Promise<void> {
    await httpDelete(`/api/data-sources/${id}`)
  }
}

export const knowledgeBaseService = new KnowledgeBaseService()