// Call Handling Service
// Epic 6 Story 6.3: Anru<PERSON>-Handling und -protokollierung

import { createClient } from '@/lib/supabaseServer'
import type {
  CallLog,
  CallTranscriptEntry,
  CallSystemEvent,
  CallTakeoverSession,
  CreateCallLogRequest,
  UpdateCallLogRequest,
  AddTranscriptEntryRequest,
  AddSystemEventRequest,
  InitiateTakeoverRequest,
  UpdateTakeoverRequest,
  CallLogFilters,
  CallLogSearchResult,
  CallStatistics,
  CallHandlingError,
  SpeechToTextResult,
  TextToSpeechRequest,
  TextToSpeechResult
} from '@/types/call-handling'

export class CallHandlingService {
  private supabase = createClient()

  // Call Log Management
  async createCallLog(userId: string, request: CreateCallLogRequest): Promise<CallLog> {
    const { data, error } = await this.supabase
      .from('call_logs')
      .insert({
        user_id: userId,
        phone_number_id: request.phone_number_id,
        agent_id: request.agent_id,
        caller_number: request.caller_number,
        caller_name: request.caller_name,
        provider_call_sid: request.provider_call_sid,
        recording_enabled: request.recording_enabled || false,
        start_time: new Date().toISOString(),
        status: 'ringing',
        cost_breakdown: {
          duration_cost: 0,
          total_cost: 0,
          currency: 'USD'
        },
        quality_metrics: {
          average_response_time_ms: 0,
          technical_issues: []
        },
        metadata: request.metadata || {}
      })
      .select(`
        *,
        phone_number:phone_numbers(id, number, friendly_name),
        agent:agents(id, name, description)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to create call log: ${error.message}`)
    }

    // Create initial system event
    await this.addSystemEvent(data.id, {
      event_type: 'call_started',
      event_description: `Incoming call from ${request.caller_number}`,
      timestamp: new Date().toISOString(),
      event_data: {
        caller_number: request.caller_number,
        caller_name: request.caller_name,
        provider_call_sid: request.provider_call_sid
      },
      severity: 'info'
    })

    return data
  }

  async updateCallLog(callLogId: string, request: UpdateCallLogRequest): Promise<CallLog> {
    const updateData: any = {
      ...request,
      updated_at: new Date().toISOString()
    }

    // If ending the call, set end_time automatically
    if (request.status && ['completed', 'failed', 'missed'].includes(request.status)) {
      updateData.end_time = request.end_time || new Date().toISOString()
    }

    const { data, error } = await this.supabase
      .from('call_logs')
      .update(updateData)
      .eq('id', callLogId)
      .select(`
        *,
        phone_number:phone_numbers(id, number, friendly_name),
        agent:agents(id, name, description)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to update call log: ${error.message}`)
    }

    // Create system event for status change
    if (request.status) {
      await this.addSystemEvent(callLogId, {
        event_type: request.status === 'completed' ? 'call_started' : 'error', // Simplified for now
        event_description: `Call status changed to ${request.status}`,
        timestamp: new Date().toISOString(),
        event_data: {
          old_status: data.status,
          new_status: request.status,
          end_reason: request.end_reason
        },
        severity: request.status === 'failed' ? 'error' : 'info'
      })
    }

    return data
  }

  async getCallLog(callLogId: string): Promise<CallLog | null> {
    const { data, error } = await this.supabase
      .from('call_logs')
      .select(`
        *,
        phone_number:phone_numbers(id, number, friendly_name),
        agent:agents(id, name, description)
      `)
      .eq('id', callLogId)
      .single()

    if (error || !data) {
      return null
    }

    return data
  }

  async searchCallLogs(userId: string, filters: CallLogFilters = {}): Promise<CallLogSearchResult> {
    let query = this.supabase
      .from('call_logs')
      .select(`
        *,
        phone_number:phone_numbers(id, number, friendly_name),
        agent:agents(id, name, description)
      `, { count: 'exact' })
      .eq('user_id', userId)

    // Apply filters
    if (filters.status && filters.status.length > 0) {
      query = query.in('status', filters.status)
    }

    if (filters.agent_ids && filters.agent_ids.length > 0) {
      query = query.in('agent_id', filters.agent_ids)
    }

    if (filters.phone_number_ids && filters.phone_number_ids.length > 0) {
      query = query.in('phone_number_id', filters.phone_number_ids)
    }

    if (filters.date_from) {
      query = query.gte('start_time', filters.date_from)
    }

    if (filters.date_to) {
      query = query.lte('start_time', filters.date_to)
    }

    if (filters.duration_min) {
      query = query.gte('duration_seconds', filters.duration_min)
    }

    if (filters.duration_max) {
      query = query.lte('duration_seconds', filters.duration_max)
    }

    if (filters.caller_number) {
      query = query.ilike('caller_number', `%${filters.caller_number}%`)
    }

    if (filters.has_recording !== undefined) {
      if (filters.has_recording) {
        query = query.not('recording_url', 'is', null)
      } else {
        query = query.is('recording_url', null)
      }
    }

    if (filters.success_rating_min) {
      query = query.gte('success_rating', filters.success_rating_min)
    }

    // Sorting
    const sortBy = filters.sort_by || 'start_time'
    const sortOrder = filters.sort_order || 'desc'
    query = query.order(sortBy, { ascending: sortOrder === 'asc' })

    // Pagination
    const page = filters.page || 1
    const pageSize = filters.page_size || 50
    const from = (page - 1) * pageSize
    const to = from + pageSize - 1

    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      throw new Error(`Failed to search call logs: ${error.message}`)
    }

    const totalCount = count || 0
    const hasMore = totalCount > to + 1

    return {
      call_logs: data || [],
      total_count: totalCount,
      has_more: hasMore,
      next_page: hasMore ? page + 1 : undefined
    }
  }

  async deleteCallLog(callLogId: string): Promise<void> {
    const { error } = await this.supabase
      .from('call_logs')
      .delete()
      .eq('id', callLogId)

    if (error) {
      throw new Error(`Failed to delete call log: ${error.message}`)
    }
  }

  // Transcript Management
  async addTranscriptEntry(callLogId: string, request: AddTranscriptEntryRequest): Promise<CallTranscriptEntry> {
    // Get the next sequence number
    const { data: lastEntry } = await this.supabase
      .from('call_transcript_entries')
      .select('sequence_number')
      .eq('call_log_id', callLogId)
      .order('sequence_number', { ascending: false })
      .limit(1)
      .single()

    const sequenceNumber = (lastEntry?.sequence_number || 0) + 1

    const { data, error } = await this.supabase
      .from('call_transcript_entries')
      .insert({
        call_log_id: callLogId,
        timestamp: request.timestamp,
        sequence_number: sequenceNumber,
        speaker: request.speaker,
        text: request.text,
        confidence: request.confidence,
        processing_time_ms: request.processing_time_ms,
        tokens_used: request.tokens_used || 0,
        model_used: request.model_used,
        event_type: request.event_type || 'message',
        event_data: request.event_data || {}
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to add transcript entry: ${error.message}`)
    }

    return data
  }

  async getTranscript(callLogId: string): Promise<CallTranscriptEntry[]> {
    const { data, error } = await this.supabase
      .from('call_transcript_entries')
      .select('*')
      .eq('call_log_id', callLogId)
      .order('sequence_number', { ascending: true })

    if (error) {
      throw new Error(`Failed to get transcript: ${error.message}`)
    }

    return data || []
  }

  // System Events Management
  async addSystemEvent(callLogId: string, request: AddSystemEventRequest): Promise<CallSystemEvent> {
    const { data, error } = await this.supabase
      .from('call_system_events')
      .insert({
        call_log_id: callLogId,
        event_type: request.event_type,
        event_description: request.event_description,
        timestamp: request.timestamp,
        duration_ms: request.duration_ms,
        event_data: request.event_data || {},
        severity: request.severity || 'info'
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to add system event: ${error.message}`)
    }

    return data
  }

  async getSystemEvents(callLogId: string): Promise<CallSystemEvent[]> {
    const { data, error } = await this.supabase
      .from('call_system_events')
      .select('*')
      .eq('call_log_id', callLogId)
      .order('timestamp', { ascending: true })

    if (error) {
      throw new Error(`Failed to get system events: ${error.message}`)
    }

    return data || []
  }

  // Takeover Management
  async initiateTakeover(
    callLogId: string,
    supervisorUserId: string,
    request: InitiateTakeoverRequest
  ): Promise<CallTakeoverSession> {
    const { data, error } = await this.supabase
      .from('call_takeover_sessions')
      .insert({
        call_log_id: callLogId,
        supervisor_user_id: supervisorUserId,
        start_time: new Date().toISOString(),
        reason: request.reason,
        supervisor_notes: request.supervisor_notes,
        status: 'active'
      })
      .select(`
        *,
        supervisor:auth.users!supervisor_user_id(id, email)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to initiate takeover: ${error.message}`)
    }

    // Update call log status
    await this.updateCallLog(callLogId, {
      status: 'manual_takeover'
    })

    // Add system event
    await this.addSystemEvent(callLogId, {
      event_type: 'manual_takeover',
      event_description: `Manual takeover initiated by supervisor`,
      timestamp: new Date().toISOString(),
      event_data: {
        supervisor_user_id: supervisorUserId,
        reason: request.reason
      },
      severity: 'warning'
    })

    return data
  }

  async updateTakeover(sessionId: string, request: UpdateTakeoverRequest): Promise<CallTakeoverSession> {
    const updateData: any = {
      ...request,
      updated_at: new Date().toISOString()
    }

    if (request.status === 'completed' || request.status === 'aborted') {
      updateData.end_time = new Date().toISOString()
    }

    const { data, error } = await this.supabase
      .from('call_takeover_sessions')
      .update(updateData)
      .eq('id', sessionId)
      .select(`
        *,
        supervisor:auth.users!supervisor_user_id(id, email)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to update takeover: ${error.message}`)
    }

    // If ending takeover, restore call to agent
    if (request.status === 'completed') {
      await this.updateCallLog(data.call_log_id, {
        status: 'in_progress'
      })
    }

    return data
  }

  async getTakeoverSessions(callLogId: string): Promise<CallTakeoverSession[]> {
    const { data, error } = await this.supabase
      .from('call_takeover_sessions')
      .select(`
        *,
        supervisor:auth.users!supervisor_user_id(id, email)
      `)
      .eq('call_log_id', callLogId)
      .order('start_time', { ascending: true })

    if (error) {
      throw new Error(`Failed to get takeover sessions: ${error.message}`)
    }

    return data || []
  }

  // Statistics and Analytics
  async getCallStatistics(
    userId: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<CallStatistics> {
    const { data, error } = await this.supabase
      .rpc('get_call_statistics', {
        user_id_param: userId,
        date_from: dateFrom ? new Date(dateFrom).toISOString() : null,
        date_to: dateTo ? new Date(dateTo).toISOString() : null
      })
      .single()

    if (error) {
      throw new Error(`Failed to get call statistics: ${error.message}`)
    }

    return data
  }

  // Full-text search in transcripts
  async searchTranscripts(
    userId: string,
    searchTerm: string,
    options: {
      agentId?: string
      dateFrom?: string
      dateTo?: string
      limit?: number
    } = {}
  ): Promise<any[]> {
    const { data, error } = await this.supabase
      .rpc('search_call_transcripts', {
        search_term: searchTerm,
        user_id_param: userId,
        agent_id_param: options.agentId || null,
        date_from: options.dateFrom ? new Date(options.dateFrom).toISOString() : null,
        date_to: options.dateTo ? new Date(options.dateTo).toISOString() : null,
        limit_param: options.limit || 50
      })

    if (error) {
      throw new Error(`Failed to search transcripts: ${error.message}`)
    }

    return data || []
  }

  // Cost calculation utilities
  calculateCallCost(durationSeconds: number, perMinuteRate: number = 0.0125): number {
    // Basic cost calculation - in production this would be more complex
    const minutes = Math.ceil(durationSeconds / 60)
    return minutes * perMinuteRate
  }

  async updateCallCosts(callLogId: string, additionalCosts: Partial<typeof CallLog.prototype.cost_breakdown>): Promise<void> {
    const callLog = await this.getCallLog(callLogId)
    if (!callLog) {
      throw new Error('Call log not found')
    }

    const updatedCostBreakdown = {
      ...callLog.cost_breakdown,
      ...additionalCosts
    }

    // Recalculate total cost
    updatedCostBreakdown.total_cost = 
      (updatedCostBreakdown.duration_cost || 0) +
      (updatedCostBreakdown.recording_cost || 0) +
      (updatedCostBreakdown.transcription_cost || 0) +
      (updatedCostBreakdown.ai_processing_cost || 0) +
      (updatedCostBreakdown.speech_synthesis_cost || 0)

    await this.updateCallLog(callLogId, {
      cost_breakdown: updatedCostBreakdown,
      total_cost: updatedCostBreakdown.total_cost
    })
  }

  // Error handling
  async logError(error: CallHandlingError): Promise<void> {
    console.error('Call handling error:', error)
    
    // In production, you'd want to integrate with error tracking service
    // like Sentry, DataDog, etc.
    
    if (error.call_log_id) {
      await this.addSystemEvent(error.call_log_id, {
        event_type: 'error',
        event_description: error.message,
        timestamp: error.timestamp,
        event_data: {
          error_type: error.type,
          details: error.details
        },
        severity: 'error'
      })
    }
  }

  // Mock speech processing methods (to be replaced with actual implementations)
  async speechToText(audioData: Buffer, options: { language?: string } = {}): Promise<SpeechToTextResult> {
    // Mock implementation - in production this would integrate with OpenAI Whisper, Google Speech-to-Text, etc.
    return {
      text: "Mock transcription result",
      confidence: 0.95,
      processing_time_ms: 150,
      language: options.language || 'en-US'
    }
  }

  async textToSpeech(request: TextToSpeechRequest): Promise<TextToSpeechResult> {
    // Mock implementation - in production this would integrate with OpenAI TTS, ElevenLabs, etc.
    return {
      audio_url: `/api/audio/mock-${Date.now()}.mp3`,
      duration_ms: Math.floor(request.text.length * 50), // Rough estimate
      file_size_bytes: Math.floor(request.text.length * 100),
      processing_time_ms: 200
    }
  }

  // Utility methods
  isWithinBusinessHours(businessHours: any): boolean {
    const now = new Date()
    const dayOfWeek = now.toLocaleString('en-US', { weekday: 'long' }).toLowerCase()
    const currentTime = now.toTimeString().substring(0, 5)
    
    const dayHours = businessHours?.days?.[dayOfWeek]
    if (!dayHours) {
      return false
    }
    
    return currentTime >= dayHours.start && currentTime <= dayHours.end
  }

  formatPhoneNumber(number: string): string {
    if (number.startsWith('+1')) {
      const cleaned = number.substring(2)
      return `+1 (${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6)}`
    }
    return number
  }

  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    }
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }
}