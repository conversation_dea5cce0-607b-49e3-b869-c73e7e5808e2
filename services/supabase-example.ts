import { supabase } from '../lib/supabaseClient'

/**
 * Beispielservice: Lese ein Profil anhand der User-ID aus der Tabelle "profiles".
 * Passe Tabellennamen/Spalten nach deinem Schema an.
 */
export async function getProfileById(userId: string) {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single()

  if (error) throw error
  return data
}

/**
 * Beispielservice: Aktualisiere den "full_name" eines Profils.
 */
export async function updateProfileName(userId: string, fullName: string) {
  const { data, error } = await supabase
    .from('profiles')
    .update({ full_name: fullName })
    .eq('id', userId)
    .select()
    .single()

  if (error) throw error
  return data
}