import { supabase } from '@/lib/supabase'
import type { Agent as AppAgent } from '@/types/agent'

export type AgentStatus = 'active' | 'inactive'
export type AgentLanguage = 'de-DE' | 'en-US'

/**
 * Server-Agent-Typ (DB/API-Rückgabe)
 * description ist in der DB nullbar
 */
export interface DbAgent {
  id: string
  user_id: string
  created_at: string
  updated_at: string
  name: string
  description: string | null
  system_prompt: string
  voice: string
  language: AgentLanguage
  status: AgentStatus
}

/**
 * Normalisierung: DbAgent -> AppAgent (setzt description null -> undefined)
 */
function normalizeAgent(a: DbAgent): AppAgent {
  return {
    ...a,
    description: a.description ?? undefined,
  }
}

export interface PaginatedAgents {
  agents: AppAgent[]
  total: number
  page: number | null
  pageSize: number | null
}

export class AgentsService {
  static async list(params?: { page?: number; pageSize?: number }): Promise<PaginatedAgents> {
    const usp = new URLSearchParams()
    if (params?.page) usp.set('page', String(params.page))
    if (params?.pageSize) usp.set('pageSize', String(params.pageSize))
    const query = usp.toString()
    const res = await fetch(`/api/agents${query ? `?${query}` : ''}`, { method: 'GET' })
    if (!res.ok) {
      const body = await safeJson(res)
      throw buildError('LIST_AGENTS_FAILED', res.status, body)
    }
    const raw = await res.json()
    const agents = Array.isArray(raw?.agents) ? (raw.agents as DbAgent[]).map(normalizeAgent) : []
    return {
      agents,
      total: typeof raw?.total === 'number' ? raw.total : agents.length,
      page: raw?.page ?? null,
      pageSize: raw?.pageSize ?? null,
    }
  }

  static async get(id: string): Promise<{ agent: AppAgent }> {
    const res = await fetch(`/api/agents/${encodeURIComponent(id)}`, { method: 'GET' })
    if (!res.ok) {
      const body = await safeJson(res)
      throw buildError('GET_AGENT_FAILED', res.status, body)
    }
    const raw = await res.json()
    return { agent: normalizeAgent(raw.agent as DbAgent) }
  }

  static async create(input: {
    name: string
    description?: string | null
    system_prompt: string
    voice: string
    language: AgentLanguage
    status?: AgentStatus
  }): Promise<{ agent: AppAgent }> {
    const res = await fetch('/api/agents', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(input)
    })
    if (!res.ok) {
      const body = await safeJson(res)
      throw buildError('CREATE_AGENT_FAILED', res.status, body)
    }
    const raw = await res.json()
    return { agent: normalizeAgent(raw.agent as DbAgent) }
  }

  static async update(
    id: string,
    patch: Partial<Pick<AppAgent, 'name' | 'description' | 'system_prompt' | 'voice' | 'language' | 'status'>>
  ): Promise<{ agent: AppAgent }> {
    const res = await fetch(`/api/agents/${encodeURIComponent(id)}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(patch)
    })
    if (!res.ok) {
      const body = await safeJson(res)
      throw buildError('UPDATE_AGENT_FAILED', res.status, body)
    }
    const raw = await res.json()
    return { agent: normalizeAgent(raw.agent as DbAgent) }
  }

  static async remove(id: string): Promise<{ success: boolean }> {
    const res = await fetch(`/api/agents/${encodeURIComponent(id)}`, { method: 'DELETE' })
    if (!res.ok) {
      const body = await safeJson(res)
      throw buildError('DELETE_AGENT_FAILED', res.status, body)
    }
    return res.json()
  }
}

/**
 * Fehler-Helfer und JSON-Safe-Parser
 */
async function safeJson(res: Response): Promise<any> {
  try {
    return await res.json()
  } catch {
    return { error: 'Unknown', message: await res.text().catch(() => 'Unbekannter Fehler') }
  }
}

class AgentsServiceError extends Error {
  code: string
  status: number
  details?: any
  constructor(code: string, status: number, message: string, details?: any) {
    super(message)
    this.name = 'AgentsServiceError'
    this.code = code
    this.status = status
    this.details = details
  }
}

function buildError(code: string, status: number, body: any) {
  const message = typeof body?.message === 'string' ? body.message : 'Fehler bei der Agenten-Operation'
  return new AgentsServiceError(code, status, message, body)
}

// Optionale Re-Exports für Konsumenten
export default AgentsService