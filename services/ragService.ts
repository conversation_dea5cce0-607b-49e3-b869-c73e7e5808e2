import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { documentProcessor } from '@/lib/document-processor'

export interface RAGSearchResult {
  chunk_id: string
  content: string
  similarity_score: number
  metadata: {
    source_name: string
    source_id: string
    page?: number
    section?: string
    document_type: string
    chunk_index: number
    token_count: number
  }
}

export interface RAGQueryResponse {
  chunks: RAGSearchResult[]
  context: string
  sources: DocumentSource[]
  total_tokens: number
}

export interface DocumentSource {
  id: string
  name: string
  file_type: string
  chunks_used: number
}

export interface RAGSearchConfig {
  similarity_threshold: number
  max_chunks: number
  include_metadata: boolean
  source_attribution: boolean
}

export class RAGService {
  private async getSupabase() {
    const cookieStore = await cookies()
    return createRouteHandlerClient({ cookies: () => cookieStore })
  }

  async searchRelevantChunks(
    query: string,
    knowledgeBaseIds: string[],
    config: RAGSearchConfig
  ): Promise<RAGSearchResult[]> {
    try {
      // Generate query embedding
      const queryEmbedding = await documentProcessor.generateQueryEmbedding(query)
      
      // Perform vector similarity search
      const supabase = await this.getSupabase()
      const { data: chunks, error } = await supabase.rpc('search_document_chunks', {
        query_embedding: `[${queryEmbedding.join(',')}]`,
        knowledge_base_ids: knowledgeBaseIds,
        similarity_threshold: config.similarity_threshold,
        match_limit: config.max_chunks
      })

      if (error) {
        console.error('Error in vector search:', error)
        throw new Error('Vector search failed')
      }

      return chunks?.map((chunk: any) => ({
        chunk_id: chunk.id,
        content: chunk.content,
        similarity_score: chunk.similarity_score,
        metadata: {
          source_name: chunk.source_name,
          source_id: chunk.source_id,
          document_type: chunk.file_type,
          chunk_index: chunk.metadata?.chunk_index || 0,
          token_count: chunk.metadata?.token_count || 0,
          page: chunk.metadata?.page,
          section: chunk.metadata?.section
        }
      })) || []
    } catch (error) {
      console.error('Error searching chunks:', error)
      throw error
    }
  }

  async generateContext(
    chunks: RAGSearchResult[],
    includeMetadata: boolean,
    sourceAttribution: boolean
  ): Promise<string> {
    if (chunks.length === 0) return ''

    return chunks.map((chunk, index) => {
      let context = chunk.content

      if (includeMetadata && chunk.metadata.source_name) {
        context += `\n[Source: ${chunk.metadata.source_name}`
        if (chunk.metadata.page) {
          context += `, Page ${chunk.metadata.page}`
        }
        context += `]`
      }

      if (sourceAttribution) {
        context += `\n[Relevance: ${(chunk.similarity_score * 100).toFixed(1)}%]`
      }

      return context
    }).join('\n\n---\n\n')
  }

  async performRAGQuery(
    agentId: string,
    query: string
  ): Promise<RAGQueryResponse> {
    try {
      const supabase = await this.getSupabase()
      
      // Get agent's RAG configuration
      const { data: ragConfig, error: configError } = await supabase
        .from('agent_rag_configs')
        .select('*')
        .eq('agent_id', agentId)
        .single()

      if (configError || !ragConfig?.enabled) {
        return {
          chunks: [],
          context: '',
          sources: [],
          total_tokens: 0
        }
      }

      // Get linked knowledge bases
      const { data: linkedKBs, error: kbError } = await supabase
        .from('agent_knowledge_bases')
        .select('knowledge_base_id')
        .eq('agent_id', agentId)

      if (kbError || !linkedKBs || linkedKBs.length === 0) {
        return {
          chunks: [],
          context: '',
          sources: [],
          total_tokens: 0
        }
      }

      const knowledgeBaseIds = linkedKBs.map(kb => kb.knowledge_base_id)

      // Search for relevant chunks
      const searchConfig: RAGSearchConfig = {
        similarity_threshold: ragConfig.similarity_threshold,
        max_chunks: ragConfig.max_chunks,
        include_metadata: ragConfig.include_metadata,
        source_attribution: ragConfig.source_attribution
      }

      const chunks = await this.searchRelevantChunks(query, knowledgeBaseIds, searchConfig)

      // Generate context
      const context = await this.generateContext(
        chunks,
        searchConfig.include_metadata,
        searchConfig.source_attribution
      )

      // Calculate sources
      const sourceMap = new Map<string, DocumentSource>()
      chunks.forEach(chunk => {
        const sourceId = chunk.metadata.source_id
        if (sourceMap.has(sourceId)) {
          sourceMap.get(sourceId)!.chunks_used++
        } else {
          sourceMap.set(sourceId, {
            id: sourceId,
            name: chunk.metadata.source_name,
            file_type: chunk.metadata.document_type,
            chunks_used: 1
          })
        }
      })

      const sources = Array.from(sourceMap.values())
      
      // Estimate total tokens
      const totalTokens = chunks.reduce((sum, chunk) => 
        sum + (chunk.metadata.token_count || 0), 0
      )

      return {
        chunks,
        context,
        sources,
        total_tokens: totalTokens
      }
    } catch (error) {
      console.error('Error performing RAG query:', error)
      throw error
    }
  }

  async getKnowledgeBaseDocumentCount(knowledgeBaseIds: string[]): Promise<number> {
    try {
      const supabase = await this.getSupabase()
      const { count, error } = await supabase
        .from('data_sources')
        .select('*', { count: 'exact', head: true })
        .in('knowledge_base_id', knowledgeBaseIds)
        .eq('status', 'ready')

      if (error) {
        console.error('Error counting documents:', error)
        return 0
      }

      return count || 0
    } catch (error) {
      console.error('Error getting document count:', error)
      return 0
    }
  }
}

export const ragService = new RAGService()