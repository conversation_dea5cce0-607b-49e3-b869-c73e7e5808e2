-- Create function for vector similarity search
CREATE OR REPLACE FUNCTION search_document_chunks(
  query_embedding vector(1536),
  knowledge_base_ids uuid[],
  similarity_threshold float DEFAULT 0.75,
  match_limit int DEFAULT 5
)
RETURNS TABLE (
  id uuid,
  content text,
  metadata jsonb,
  similarity_score float,
  source_name text,
  source_id uuid,
  file_type text,
  created_at timestamp with time zone
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    dc.id,
    dc.content,
    dc.metadata,
    (1 - (dc.embedding <=> query_embedding)) as similarity_score,
    ds.name as source_name,
    ds.id as source_id,
    ds.file_type,
    dc.created_at
  FROM document_chunks dc
  JOIN data_sources ds ON dc.data_source_id = ds.id
  WHERE 
    ds.knowledge_base_id = ANY(knowledge_base_ids) AND
    ds.status = 'ready' AND
    (1 - (dc.embedding <=> query_embedding)) > similarity_threshold
  ORDER BY similarity_score DESC
  LIMIT match_limit;
END;
$$;

-- <PERSON> execute permission to authenticated users
GRANT EXECUTE ON FUNCTION search_document_chunks TO authenticated;

-- Create function to get available knowledge bases for an agent
CREATE OR REPLACE FUNCTION get_agent_available_knowledge_bases(agent_user_id uuid)
RETURNS TABLE (
  id uuid,
  name text,
  description text,
  status text,
  created_at timestamp with time zone,
  updated_at timestamp with time zone,
  document_count bigint,
  is_linked boolean
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    kb.id,
    kb.name,
    kb.description,
    kb.status,
    kb.created_at,
    kb.updated_at,
    COUNT(ds.id) FILTER (WHERE ds.status = 'ready') as document_count,
    EXISTS(
      SELECT 1 FROM agent_knowledge_bases akb 
      WHERE akb.knowledge_base_id = kb.id 
      AND akb.agent_id IN (
        SELECT a.id FROM agents a WHERE a.user_id = agent_user_id
      )
    ) as is_linked
  FROM knowledge_bases kb
  LEFT JOIN data_sources ds ON kb.id = ds.knowledge_base_id
  WHERE kb.user_id = agent_user_id
  GROUP BY kb.id, kb.name, kb.description, kb.status, kb.created_at, kb.updated_at
  ORDER BY kb.name;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_agent_available_knowledge_bases TO authenticated;

-- Create function to get knowledge base statistics
CREATE OR REPLACE FUNCTION get_knowledge_base_stats(kb_id uuid)
RETURNS TABLE (
  total_documents bigint,
  ready_documents bigint,
  processing_documents bigint,
  error_documents bigint,
  total_chunks bigint,
  total_size bigint,
  avg_chunks_per_doc float
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_documents,
    COUNT(*) FILTER (WHERE status = 'ready') as ready_documents,
    COUNT(*) FILTER (WHERE status IN ('uploading', 'processing')) as processing_documents,
    COUNT(*) FILTER (WHERE status = 'error') as error_documents,
    SUM(chunk_count) as total_chunks,
    SUM(file_size) as total_size,
    AVG(chunk_count) FILTER (WHERE chunk_count > 0) as avg_chunks_per_doc
  FROM data_sources
  WHERE knowledge_base_id = kb_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_knowledge_base_stats TO authenticated;