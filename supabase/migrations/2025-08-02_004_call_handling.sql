-- Call Handling and Logging Schema
-- Epic 6 Story 6.3: Anru<PERSON>-Handling und -protokollierung

-- Call logs table for comprehensive call tracking
CREATE TABLE call_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number_id UUID REFERENCES phone_numbers(id) ON DELETE CASCADE,
  agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Provider integration
  provider_call_sid TEXT, -- <PERSON><PERSON><PERSON> call SID
  
  -- Caller information
  caller_number TEXT NOT NULL,
  caller_name TEXT,
  caller_location TEXT,
  
  -- Call timing
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  duration_seconds INTEGER,
  
  -- Call status
  status TEXT NOT NULL DEFAULT 'ringing' CHECK (
    status IN ('ringing', 'in_progress', 'completed', 'failed', 'missed', 'manual_takeover')
  ),
  end_reason TEXT CHECK (
    end_reason IN ('caller_hangup', 'agent_hangup', 'system_error', 'timeout', 'manual_takeover', 'transferred')
  ),
  
  -- Quality and ratings
  success_rating INTEGER CHECK (success_rating >= 1 AND success_rating <= 5),
  caller_satisfaction INTEGER CHECK (caller_satisfaction >= 1 AND caller_satisfaction <= 5),
  
  -- Recording and transcription
  recording_enabled BOOLEAN DEFAULT false,
  recording_url TEXT,
  recording_duration_seconds INTEGER,
  
  -- Cost tracking
  cost_breakdown JSONB DEFAULT '{}',
  total_cost DECIMAL(10,4) DEFAULT 0.00,
  currency TEXT DEFAULT 'USD',
  
  -- Quality metrics
  quality_metrics JSONB DEFAULT '{}',
  
  -- Additional metadata
  metadata JSONB DEFAULT '{}'
);

-- Call transcript entries for detailed conversation logs
CREATE TABLE call_transcript_entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  call_log_id UUID REFERENCES call_logs(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Timing
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  sequence_number INTEGER NOT NULL, -- Order within the call
  
  -- Speaker identification
  speaker TEXT NOT NULL CHECK (speaker IN ('caller', 'agent', 'system')),
  speaker_name TEXT, -- Display name for the speaker
  
  -- Content
  text TEXT NOT NULL,
  original_text TEXT, -- Before any processing/correction
  
  -- Quality metrics
  confidence DECIMAL(3,2), -- 0.00 to 1.00
  processing_time_ms INTEGER,
  
  -- AI processing metadata
  tokens_used INTEGER DEFAULT 0,
  model_used TEXT,
  
  -- Event metadata for system entries
  event_type TEXT, -- 'message', 'tool_call', 'system_event', 'error'
  event_data JSONB DEFAULT '{}'
);

-- Call system events for detailed audit trail
CREATE TABLE call_system_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  call_log_id UUID REFERENCES call_logs(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Event details
  event_type TEXT NOT NULL, -- 'call_started', 'agent_joined', 'recording_started', 'tool_used', 'manual_takeover', 'transferred', 'error'
  event_description TEXT NOT NULL,
  
  -- Event timing
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  duration_ms INTEGER, -- For events that have duration
  
  -- Associated data
  event_data JSONB DEFAULT '{}',
  
  -- Severity for alerts
  severity TEXT DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical'))
);

-- Manual takeover sessions
CREATE TABLE call_takeover_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  call_log_id UUID REFERENCES call_logs(id) ON DELETE CASCADE,
  supervisor_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Session timing
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  duration_seconds INTEGER,
  
  -- Takeover details
  reason TEXT NOT NULL,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'aborted')),
  
  -- Notes and outcomes
  supervisor_notes TEXT,
  outcome TEXT,
  
  -- Metadata
  metadata JSONB DEFAULT '{}'
);

-- Indexes for performance optimization
CREATE INDEX idx_call_logs_user_id ON call_logs(user_id);
CREATE INDEX idx_call_logs_phone_number ON call_logs(phone_number_id);
CREATE INDEX idx_call_logs_agent ON call_logs(agent_id);
CREATE INDEX idx_call_logs_status ON call_logs(status);
CREATE INDEX idx_call_logs_start_time ON call_logs(start_time);
CREATE INDEX idx_call_logs_end_time ON call_logs(end_time);
CREATE INDEX idx_call_logs_caller_number ON call_logs(caller_number);
CREATE INDEX idx_call_logs_provider_sid ON call_logs(provider_call_sid);

-- Transcript entries indexes
CREATE INDEX idx_transcript_entries_call ON call_transcript_entries(call_log_id);
CREATE INDEX idx_transcript_entries_timestamp ON call_transcript_entries(timestamp);
CREATE INDEX idx_transcript_entries_sequence ON call_transcript_entries(call_log_id, sequence_number);
CREATE INDEX idx_transcript_entries_speaker ON call_transcript_entries(speaker);

-- Full-text search for transcripts (German and English)
CREATE INDEX idx_transcript_entries_text_german ON call_transcript_entries USING gin(to_tsvector('german', text));
CREATE INDEX idx_transcript_entries_text_english ON call_transcript_entries USING gin(to_tsvector('english', text));

-- System events indexes
CREATE INDEX idx_system_events_call ON call_system_events(call_log_id);
CREATE INDEX idx_system_events_timestamp ON call_system_events(timestamp);
CREATE INDEX idx_system_events_type ON call_system_events(event_type);
CREATE INDEX idx_system_events_severity ON call_system_events(severity);

-- Takeover sessions indexes
CREATE INDEX idx_takeover_sessions_call ON call_takeover_sessions(call_log_id);
CREATE INDEX idx_takeover_sessions_supervisor ON call_takeover_sessions(supervisor_user_id);
CREATE INDEX idx_takeover_sessions_status ON call_takeover_sessions(status);

-- Row Level Security (RLS) policies
ALTER TABLE call_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_transcript_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_system_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_takeover_sessions ENABLE ROW LEVEL SECURITY;

-- Call logs policies
CREATE POLICY "Users can view their own call logs" ON call_logs
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own call logs" ON call_logs
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own call logs" ON call_logs
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own call logs" ON call_logs
  FOR DELETE USING (user_id = auth.uid());

-- Transcript entries policies (inherit from call logs)
CREATE POLICY "Users can view transcript entries for their calls" ON call_transcript_entries
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM call_logs
      WHERE call_logs.id = call_transcript_entries.call_log_id
      AND call_logs.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert transcript entries for their calls" ON call_transcript_entries
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM call_logs
      WHERE call_logs.id = call_transcript_entries.call_log_id
      AND call_logs.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update transcript entries for their calls" ON call_transcript_entries
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM call_logs
      WHERE call_logs.id = call_transcript_entries.call_log_id
      AND call_logs.user_id = auth.uid()
    )
  );

-- System events policies
CREATE POLICY "Users can view system events for their calls" ON call_system_events
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM call_logs
      WHERE call_logs.id = call_system_events.call_log_id
      AND call_logs.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert system events for their calls" ON call_system_events
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM call_logs
      WHERE call_logs.id = call_system_events.call_log_id
      AND call_logs.user_id = auth.uid()
    )
  );

-- Takeover sessions policies
CREATE POLICY "Users can view takeover sessions for their calls" ON call_takeover_sessions
  FOR SELECT USING (
    supervisor_user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM call_logs
      WHERE call_logs.id = call_takeover_sessions.call_log_id
      AND call_logs.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create takeover sessions for their calls" ON call_takeover_sessions
  FOR INSERT WITH CHECK (
    supervisor_user_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM call_logs
      WHERE call_logs.id = call_takeover_sessions.call_log_id
      AND call_logs.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own takeover sessions" ON call_takeover_sessions
  FOR UPDATE USING (supervisor_user_id = auth.uid());

-- Database functions for call management

-- Function to update call duration automatically
CREATE OR REPLACE FUNCTION update_call_duration()
RETURNS TRIGGER AS $$
BEGIN
  -- Calculate duration when end_time is set
  IF NEW.end_time IS NOT NULL AND OLD.end_time IS NULL THEN
    NEW.duration_seconds = EXTRACT(EPOCH FROM (NEW.end_time - NEW.start_time))::INTEGER;
  END IF;
  
  -- Update timestamp
  NEW.updated_at = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for automatic duration calculation
CREATE TRIGGER trigger_update_call_duration
  BEFORE UPDATE ON call_logs
  FOR EACH ROW
  EXECUTE FUNCTION update_call_duration();

-- Function to search call transcripts
CREATE OR REPLACE FUNCTION search_call_transcripts(
  search_term TEXT,
  user_id_param UUID DEFAULT NULL,
  agent_id_param UUID DEFAULT NULL,
  date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  limit_param INTEGER DEFAULT 50
)
RETURNS TABLE (
  call_log_id UUID,
  start_time TIMESTAMP WITH TIME ZONE,
  agent_name TEXT,
  caller_number TEXT,
  transcript_excerpt TEXT,
  match_count INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cl.id,
    cl.start_time,
    a.name as agent_name,
    cl.caller_number,
    string_agg(cte.text, ' ' ORDER BY cte.sequence_number) as transcript_excerpt,
    COUNT(cte.id)::INTEGER as match_count
  FROM call_logs cl
  JOIN agents a ON a.id = cl.agent_id
  JOIN call_transcript_entries cte ON cte.call_log_id = cl.id
  WHERE 
    (user_id_param IS NULL OR cl.user_id = user_id_param)
    AND (agent_id_param IS NULL OR cl.agent_id = agent_id_param)
    AND (date_from IS NULL OR cl.start_time >= date_from)
    AND (date_to IS NULL OR cl.start_time <= date_to)
    AND (
      cte.text ILIKE '%' || search_term || '%'
      OR to_tsvector('german', cte.text) @@ plainto_tsquery('german', search_term)
      OR to_tsvector('english', cte.text) @@ plainto_tsquery('english', search_term)
    )
  GROUP BY cl.id, cl.start_time, a.name, cl.caller_number
  ORDER BY cl.start_time DESC
  LIMIT limit_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get call statistics
CREATE OR REPLACE FUNCTION get_call_statistics(
  user_id_param UUID,
  date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TABLE (
  total_calls INTEGER,
  completed_calls INTEGER,
  missed_calls INTEGER,
  failed_calls INTEGER,
  total_duration_seconds INTEGER,
  average_duration_seconds INTEGER,
  total_cost DECIMAL,
  success_rate DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_calls,
    COUNT(*) FILTER (WHERE status = 'completed')::INTEGER as completed_calls,
    COUNT(*) FILTER (WHERE status = 'missed')::INTEGER as missed_calls,
    COUNT(*) FILTER (WHERE status = 'failed')::INTEGER as failed_calls,
    COALESCE(SUM(duration_seconds), 0)::INTEGER as total_duration_seconds,
    COALESCE(AVG(duration_seconds), 0)::INTEGER as average_duration_seconds,
    COALESCE(SUM(total_cost), 0) as total_cost,
    CASE 
      WHEN COUNT(*) > 0 THEN 
        ROUND(COUNT(*) FILTER (WHERE status = 'completed')::DECIMAL / COUNT(*) * 100, 2)
      ELSE 0 
    END as success_rate
  FROM call_logs
  WHERE 
    user_id = user_id_param
    AND (date_from IS NULL OR start_time >= date_from)
    AND (date_to IS NULL OR start_time <= date_to);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup old call data (for GDPR compliance)
CREATE OR REPLACE FUNCTION cleanup_old_call_data(
  retention_days INTEGER DEFAULT 365
)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete old call logs and related data
  WITH deleted_calls AS (
    DELETE FROM call_logs
    WHERE created_at < NOW() - INTERVAL '1 day' * retention_days
    RETURNING id
  )
  SELECT COUNT(*) INTO deleted_count FROM deleted_calls;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;