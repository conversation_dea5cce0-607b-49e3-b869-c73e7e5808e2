-- Enable pgvector extension for vector storage
CREATE EXTENSION IF NOT EXISTS vector;

-- Create knowledge_bases table
CREATE TABLE knowledge_bases (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  name TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive'))
);

-- Create data_sources table for documents
CREATE TABLE data_sources (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  knowledge_base_id UUID REFERENCES knowledge_bases(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  name TEXT NOT NULL,
  original_filename TEXT NOT NULL,
  file_type TEXT NOT NULL CHECK (file_type IN ('pdf', 'docx', 'txt', 'md')),
  file_size INTEGER NOT NULL,
  file_url TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'uploading' CHECK (status IN ('uploading', 'processing', 'ready', 'error')),
  error_message TEXT,
  metadata JSONB DEFAULT '{}',
  chunk_count INTEGER DEFAULT 0
);

-- Create document_chunks table for vector storage
CREATE TABLE document_chunks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  data_source_id UUID REFERENCES data_sources(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  embedding vector(1536), -- OpenAI embedding dimension
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create agent_knowledge_bases junction table
CREATE TABLE agent_knowledge_bases (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
  knowledge_base_id UUID REFERENCES knowledge_bases(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  priority INTEGER DEFAULT 1,
  UNIQUE(agent_id, knowledge_base_id)
);

-- Create agent_rag_configs table
CREATE TABLE agent_rag_configs (
  agent_id UUID PRIMARY KEY REFERENCES agents(id) ON DELETE CASCADE,
  enabled BOOLEAN DEFAULT true,
  similarity_threshold DECIMAL(3,2) DEFAULT 0.75 CHECK (similarity_threshold >= 0 AND similarity_threshold <= 1),
  max_chunks INTEGER DEFAULT 5 CHECK (max_chunks > 0 AND max_chunks <= 20),
  chunk_overlap BOOLEAN DEFAULT true,
  include_metadata BOOLEAN DEFAULT true,
  source_attribution BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add rag_enabled column to agents table
ALTER TABLE agents ADD COLUMN rag_enabled BOOLEAN DEFAULT false;

-- Row Level Security Policies
ALTER TABLE knowledge_bases ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_chunks ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_knowledge_bases ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_rag_configs ENABLE ROW LEVEL SECURITY;

-- Knowledge Bases Policies
CREATE POLICY "Users can view their own knowledge bases" ON knowledge_bases
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own knowledge bases" ON knowledge_bases
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own knowledge bases" ON knowledge_bases
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own knowledge bases" ON knowledge_bases
  FOR DELETE USING (auth.uid() = user_id);

-- Data Sources Policies
CREATE POLICY "Users can view data sources from their knowledge bases" ON data_sources
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM knowledge_bases kb 
      WHERE kb.id = data_sources.knowledge_base_id 
      AND kb.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create data sources in their knowledge bases" ON data_sources
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM knowledge_bases kb 
      WHERE kb.id = data_sources.knowledge_base_id 
      AND kb.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update data sources in their knowledge bases" ON data_sources
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM knowledge_bases kb 
      WHERE kb.id = data_sources.knowledge_base_id 
      AND kb.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete data sources from their knowledge bases" ON data_sources
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM knowledge_bases kb 
      WHERE kb.id = data_sources.knowledge_base_id 
      AND kb.user_id = auth.uid()
    )
  );

-- Document Chunks Policies
CREATE POLICY "Users can view chunks from their data sources" ON document_chunks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM data_sources ds
      JOIN knowledge_bases kb ON ds.knowledge_base_id = kb.id
      WHERE ds.id = document_chunks.data_source_id 
      AND kb.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create chunks in their data sources" ON document_chunks
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM data_sources ds
      JOIN knowledge_bases kb ON ds.knowledge_base_id = kb.id
      WHERE ds.id = document_chunks.data_source_id 
      AND kb.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update chunks in their data sources" ON document_chunks
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM data_sources ds
      JOIN knowledge_bases kb ON ds.knowledge_base_id = kb.id
      WHERE ds.id = document_chunks.data_source_id 
      AND kb.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete chunks from their data sources" ON document_chunks
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM data_sources ds
      JOIN knowledge_bases kb ON ds.knowledge_base_id = kb.id
      WHERE ds.id = document_chunks.data_source_id 
      AND kb.user_id = auth.uid()
    )
  );

-- Agent Knowledge Bases Policies
CREATE POLICY "Users can view their agent knowledge base connections" ON agent_knowledge_bases
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM agents a 
      WHERE a.id = agent_knowledge_bases.agent_id 
      AND a.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create connections for their agents" ON agent_knowledge_bases
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM agents a 
      WHERE a.id = agent_knowledge_bases.agent_id 
      AND a.user_id = auth.uid()
    ) AND
    EXISTS (
      SELECT 1 FROM knowledge_bases kb 
      WHERE kb.id = agent_knowledge_bases.knowledge_base_id 
      AND kb.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update connections for their agents" ON agent_knowledge_bases
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM agents a 
      WHERE a.id = agent_knowledge_bases.agent_id 
      AND a.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete connections for their agents" ON agent_knowledge_bases
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM agents a 
      WHERE a.id = agent_knowledge_bases.agent_id 
      AND a.user_id = auth.uid()
    )
  );

-- Agent RAG Configs Policies
CREATE POLICY "Users can view RAG configs for their agents" ON agent_rag_configs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM agents a 
      WHERE a.id = agent_rag_configs.agent_id 
      AND a.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create RAG configs for their agents" ON agent_rag_configs
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM agents a 
      WHERE a.id = agent_rag_configs.agent_id 
      AND a.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update RAG configs for their agents" ON agent_rag_configs
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM agents a 
      WHERE a.id = agent_rag_configs.agent_id 
      AND a.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete RAG configs for their agents" ON agent_rag_configs
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM agents a 
      WHERE a.id = agent_rag_configs.agent_id 
      AND a.user_id = auth.uid()
    )
  );

-- Indexes for better performance
CREATE INDEX idx_knowledge_bases_user ON knowledge_bases(user_id);
CREATE INDEX idx_knowledge_bases_status ON knowledge_bases(status);

CREATE INDEX idx_data_sources_knowledge_base ON data_sources(knowledge_base_id);
CREATE INDEX idx_data_sources_status ON data_sources(status);
CREATE INDEX idx_data_sources_file_type ON data_sources(file_type);

CREATE INDEX idx_document_chunks_data_source ON document_chunks(data_source_id);
CREATE INDEX idx_document_chunks_embedding ON document_chunks USING ivfflat (embedding vector_cosine_ops);

CREATE INDEX idx_agent_knowledge_bases_agent ON agent_knowledge_bases(agent_id);
CREATE INDEX idx_agent_knowledge_bases_kb ON agent_knowledge_bases(knowledge_base_id);

-- Functions for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
CREATE TRIGGER update_knowledge_bases_updated_at BEFORE UPDATE ON knowledge_bases
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_sources_updated_at BEFORE UPDATE ON data_sources
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_rag_configs_updated_at BEFORE UPDATE ON agent_rag_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to count documents in knowledge base
CREATE OR REPLACE FUNCTION count_knowledge_base_documents(kb_id UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)::INTEGER
        FROM data_sources
        WHERE knowledge_base_id = kb_id
        AND status = 'ready'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;