-- Migration: Create agents table with RLS and owner-only policies (Hard-Delete)
-- Timestamp: 2025-08-01_001

-- Enable required extensions (safe if already enabled)
create extension if not exists "uuid-ossp";
create extension if not exists pgcrypto;

-- Table: agents
create table if not exists public.agents (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  name text not null,
  description text,
  system_prompt text not null,
  voice text not null,
  language text not null check (language in ('de-DE', 'en-US')),
  status text not null default 'inactive' check (status in ('active', 'inactive'))
);

-- Helpful indexes
create index if not exists idx_agents_user_id on public.agents (user_id);
create index if not exists idx_agents_created_at on public.agents (created_at desc);
create index if not exists idx_agents_status on public.agents (status);

-- RLS
alter table public.agents enable row level security;

-- Policies (owner-only)
drop policy if exists "Agents select own" on public.agents;
create policy "Agents select own"
  on public.agents
  for select
  using (auth.uid() = user_id);

drop policy if exists "Agents insert own" on public.agents;
create policy "Agents insert own"
  on public.agents
  for insert
  with check (auth.uid() = user_id);

drop policy if exists "Agents update own" on public.agents;
create policy "Agents update own"
  on public.agents
  for update
  using (auth.uid() = user_id);

drop policy if exists "Agents delete own" on public.agents;
create policy "Agents delete own"
  on public.agents
  for delete
  using (auth.uid() = user_id);

-- Updated_at trigger
create or replace function public.set_updated_at()
returns trigger
language plpgsql
as $$
begin
  new.updated_at = now();
  return new;
end;
$$;

drop trigger if exists trg_agents_set_updated_at on public.agents;
create trigger trg_agents_set_updated_at
before update on public.agents
for each row
execute function public.set_updated_at();

-- Comment metadata
comment on table public.agents is 'KI-Agenten der Benutzer; RLS auf Besitzer. Hard-Delete erlaubt.';
comment on column public.agents.system_prompt is 'Kernpersönlichkeit/Instruktion des Agenten (>= 50 Zeichen empfohlen).';