-- Create phone_numbers table
CREATE TABLE phone_numbers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  number TEXT NOT NULL UNIQUE,
  friendly_name TEXT,
  country_code TEXT NOT NULL,
  area_code TEXT,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
  assigned_agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
  monthly_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  provider_id TEXT NOT NULL DEFAULT 'twilio',
  provider_phone_number_sid TEXT,
  capabilities JSONB DEFAULT '{"voice": true, "sms": false}',
  forwarding_settings JSONB DEFAULT '{}',
  business_hours JSONB DEFAULT '{}',
  webhook_url TEXT
);

-- Create call_logs table
CREATE TABLE call_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number_id UUID REFERENCES phone_numbers(id) ON DELETE CASCADE,
  agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  duration_seconds INTEGER,
  caller_number TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('active', 'completed', 'failed', 'missed', 'no_answer')),
  end_reason TEXT CHECK (end_reason IN ('caller_hangup', 'agent_hangup', 'system_error', 'timeout', 'busy')),
  transcript_url TEXT,
  recording_url TEXT,
  cost DECIMAL(10,4),
  metadata JSONB DEFAULT '{}',
  provider_call_sid TEXT
);

-- Create call_metrics table for aggregated statistics
CREATE TABLE call_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number_id UUID REFERENCES phone_numbers(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  total_calls INTEGER DEFAULT 0,
  answered_calls INTEGER DEFAULT 0,
  missed_calls INTEGER DEFAULT 0,
  failed_calls INTEGER DEFAULT 0,
  average_duration DECIMAL(10,2) DEFAULT 0,
  total_duration INTEGER DEFAULT 0,
  total_cost DECIMAL(10,4) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(phone_number_id, date)
);

-- Create billing_usage table for cost tracking
CREATE TABLE billing_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  phone_number_id UUID REFERENCES phone_numbers(id) ON DELETE CASCADE,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  usage_type TEXT NOT NULL CHECK (usage_type IN ('monthly_fee', 'call_minutes', 'sms')),
  quantity DECIMAL(10,4) NOT NULL DEFAULT 0,
  unit_cost DECIMAL(10,4) NOT NULL DEFAULT 0,
  total_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
  currency TEXT NOT NULL DEFAULT 'USD',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'
);

-- Row Level Security Policies
ALTER TABLE phone_numbers ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing_usage ENABLE ROW LEVEL SECURITY;

-- Phone Numbers Policies
CREATE POLICY "Users can view their own phone numbers" ON phone_numbers
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own phone numbers" ON phone_numbers
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own phone numbers" ON phone_numbers
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own phone numbers" ON phone_numbers
  FOR DELETE USING (auth.uid() = user_id);

-- Call Logs Policies
CREATE POLICY "Users can view call logs for their phone numbers" ON call_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM phone_numbers pn 
      WHERE pn.id = call_logs.phone_number_id 
      AND pn.user_id = auth.uid()
    )
  );

CREATE POLICY "System can create call logs" ON call_logs
  FOR INSERT WITH CHECK (true); -- Allow system to create logs via webhooks

CREATE POLICY "Users can update call logs for their phone numbers" ON call_logs
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM phone_numbers pn 
      WHERE pn.id = call_logs.phone_number_id 
      AND pn.user_id = auth.uid()
    )
  );

-- Call Metrics Policies
CREATE POLICY "Users can view call metrics for their phone numbers" ON call_metrics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM phone_numbers pn 
      WHERE pn.id = call_metrics.phone_number_id 
      AND pn.user_id = auth.uid()
    )
  );

CREATE POLICY "System can manage call metrics" ON call_metrics
  FOR ALL USING (true); -- Allow system to manage metrics

-- Billing Usage Policies
CREATE POLICY "Users can view their own billing usage" ON billing_usage
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can create billing usage" ON billing_usage
  FOR INSERT WITH CHECK (true); -- Allow system to create billing records

CREATE POLICY "System can update billing usage" ON billing_usage
  FOR UPDATE USING (true); -- Allow system to update billing records

-- Indexes for better performance
CREATE INDEX idx_phone_numbers_user ON phone_numbers(user_id);
CREATE INDEX idx_phone_numbers_status ON phone_numbers(status);
CREATE INDEX idx_phone_numbers_number ON phone_numbers(number);

CREATE INDEX idx_call_logs_phone_number ON call_logs(phone_number_id);
CREATE INDEX idx_call_logs_start_time ON call_logs(start_time);
CREATE INDEX idx_call_logs_status ON call_logs(status);
CREATE INDEX idx_call_logs_caller_number ON call_logs(caller_number);

CREATE INDEX idx_call_metrics_phone_number ON call_metrics(phone_number_id);
CREATE INDEX idx_call_metrics_date ON call_metrics(date);

CREATE INDEX idx_billing_usage_user ON billing_usage(user_id);
CREATE INDEX idx_billing_usage_phone_number ON billing_usage(phone_number_id);
CREATE INDEX idx_billing_usage_period ON billing_usage(period_start, period_end);

-- Functions for updating timestamps
CREATE TRIGGER update_phone_numbers_updated_at BEFORE UPDATE ON phone_numbers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_call_metrics_updated_at BEFORE UPDATE ON call_metrics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to get phone number statistics
CREATE OR REPLACE FUNCTION get_phone_number_stats(phone_number_uuid UUID)
RETURNS TABLE (
  total_calls bigint,
  answered_calls bigint,
  missed_calls bigint,
  average_duration decimal,
  total_cost decimal,
  last_call_date timestamp with time zone
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::bigint as total_calls,
    COUNT(*) FILTER (WHERE status = 'completed')::bigint as answered_calls,
    COUNT(*) FILTER (WHERE status IN ('missed', 'no_answer'))::bigint as missed_calls,
    AVG(duration_seconds) as average_duration,
    SUM(cost) as total_cost,
    MAX(start_time) as last_call_date
  FROM call_logs
  WHERE phone_number_id = phone_number_uuid;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_phone_number_stats TO authenticated;

-- Function to update daily call metrics
CREATE OR REPLACE FUNCTION update_call_metrics(phone_number_uuid UUID, call_date DATE)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO call_metrics (
    phone_number_id,
    date,
    total_calls,
    answered_calls,
    missed_calls,
    failed_calls,
    average_duration,
    total_duration,
    total_cost
  )
  SELECT 
    phone_number_uuid,
    call_date,
    COUNT(*)::integer,
    COUNT(*) FILTER (WHERE status = 'completed')::integer,
    COUNT(*) FILTER (WHERE status IN ('missed', 'no_answer'))::integer,
    COUNT(*) FILTER (WHERE status = 'failed')::integer,
    AVG(duration_seconds),
    SUM(duration_seconds)::integer,
    SUM(cost)
  FROM call_logs
  WHERE phone_number_id = phone_number_uuid
    AND DATE(start_time) = call_date
  ON CONFLICT (phone_number_id, date)
  DO UPDATE SET
    total_calls = EXCLUDED.total_calls,
    answered_calls = EXCLUDED.answered_calls,
    missed_calls = EXCLUDED.missed_calls,
    failed_calls = EXCLUDED.failed_calls,
    average_duration = EXCLUDED.average_duration,
    total_duration = EXCLUDED.total_duration,
    total_cost = EXCLUDED.total_cost,
    updated_at = NOW();
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION update_call_metrics TO authenticated;