import { render, screen, fireEvent, act } from '@testing-library/react'
import '@testing-library/jest-dom'
import TestingPage from '@/app/testing/page'

jest.mock('next/navigation', () => ({ useRouter: () => ({ push: jest.fn(), refresh: jest.fn() }) }))
jest.mock('sonner', () => ({ toast: { success: jest.fn(), error: jest.fn() } }))

jest.mock('@/services/agents', () => ({
  __esModule: true,
  default: {
    list: jest.fn(async () => ({ agents: [
      { id: 'a1', user_id: 'u1', created_at: '2025-01-01', updated_at: '2025-01-02', name: 'Agent One', description: 'd', system_prompt: 'p', voice: 'voice1', language: 'de-DE', status: 'active' },
    ] }))
  }
}))

jest.useFakeTimers()

describe.skip('TestingPage', () => {
  it('streams mock lines on start and stops streaming', async () => {
    render(<TestingPage />)
    expect(await screen.findByText('Test-Session')).toBeInTheDocument()

    const selectTrigger = await screen.findByText('Agent auswählen')
    fireEvent.mouseDown(selectTrigger)
    const option = await screen.findByText('Agent One')
    fireEvent.click(option)

    const startBtn = screen.getByRole('button', { name: 'Start' })
    fireEvent.click(startBtn)

    await act(async () => { jest.advanceTimersByTime(1500) })
    const anyLine = screen.getByText(/Voice-Agent|Zusammenfassung|unterstützen/)
    expect(anyLine).toBeInTheDocument()

    const stopBtn = screen.getByRole('button', { name: 'Stop' })
    fireEvent.click(stopBtn)
    await act(async () => { jest.advanceTimersByTime(1500) })

    const allLinesBefore = screen.getAllByText(/Hallo|prüfe|Verstanden|Zusammenfassung|unterstützen/)
    await act(async () => { jest.advanceTimersByTime(1500) })
    const allLinesAfter = screen.getAllByText(/Hallo|prüfe|Verstanden|Zusammenfassung|unterstützen/)
    expect(allLinesAfter.length).toBeLessThanOrEqual(allLinesBefore.length + 1)
  })
})
