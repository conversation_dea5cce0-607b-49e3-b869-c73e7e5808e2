/**
 * Tests für /api/agents (GET/POST)
 * - 401 Unauthenticated
 * - GET: 200 mit Pagination & Owner-Filter
 * - POST: 400 Zod-Validierung
 * - POST: 201 Success
 * - DB-Fehler: 500
 *
 * Wir mocken Supabase-Client via jest.mock('@supabase/auth-helpers-nextjs')
 * und rufen die Route-Handler-Funktionen direkt auf.
 */

import * as authHelpers from '@supabase/auth-helpers-nextjs'

import * as AgentsRoute from '../../../app/api/agents/route'

type SessionUser = { id: string; email?: string }
type Session = { user: SessionUser } | null

// Utility: Fake NextRequest mit URL und optionalem Body
function makeRequest(method: 'GET' | 'POST', url: string, body?: any): Request {
  const init: RequestInit = { method, headers: new Headers() }
  if (body !== undefined) {
    init.body = JSON.stringify(body)
    ;(init.headers as any).set?.('Content-Type', 'application/json')
  }
  return new Request(new URL(url, 'http://localhost').toString(), init)
}

// Jest Mocks
jest.mock('@supabase/auth-helpers-nextjs', () => {
  const actual = jest.requireActual('@supabase/auth-helpers-nextjs')
  const cookiesMock = jest.fn()
  const selectMock = jest.fn()
  const eqMock = jest.fn()
  const orderMock = jest.fn()
  const rangeMock = jest.fn()
  const singleMock = jest.fn()
  const insertMock = jest.fn()
  const fromMock = jest.fn(() => ({
    select: selectMock,
    insert: insertMock,
    eq: eqMock,
    order: orderMock,
    range: rangeMock,
  }))

  const authGetSessionMock = jest.fn()

  const supabaseClientMock = {
    auth: {
      getSession: authGetSessionMock,
    },
    from: fromMock,
  }

  return {
    ...actual,
    createRouteHandlerClient: jest.fn(() => supabaseClientMock),
    // Export der inneren Mocks für gezieltes Verhalten je Test
    __mocks: {
      supabaseClientMock,
      fromMock,
      selectMock,
      eqMock,
      orderMock,
      rangeMock,
      singleMock,
      insertMock,
      authGetSessionMock,
      cookiesMock,
    },
  }
})

const {
  __mocks: {
    supabaseClientMock,
    fromMock,
    selectMock,
    eqMock,
    orderMock,
    rangeMock,
    insertMock,
    authGetSessionMock,
  },
} = authHelpers as any

describe.skip('/api/agents', () => {
  beforeEach(() => {
    jest.resetAllMocks()
  })

  describe('GET /api/agents', () => {
    test('401 wenn keine Session vorhanden', async () => {
      authGetSessionMock.mockResolvedValueOnce({ data: { session: null }, error: null })

      const req = makeRequest('GET', '/api/agents')
      const res = await AgentsRoute.GET(req)
      const body = await res.json()

      expect(res.status).toBe(401)
      expect(body.error).toBe('Unauthorized')
    })

    test('200, gibt Liste und Count zurück, Owner-Filter gesetzt', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      // Query-Chain mocks
      selectMock.mockReturnValue({ eq: eqMock, order: orderMock })
      eqMock.mockReturnValue({ select: selectMock, order: orderMock })
      orderMock.mockReturnValue({ select: selectMock, eq: eqMock, range: rangeMock })
      // Finaler Aufruf ohne Pagination
      // Wir simulieren, dass select() am Ende data/count zurückgibt:
      ;(async () => {}) // no-op

      // Überschreiben der "await query" Stelle:
      // Wir erreichen das, indem orderMock() das finale Await-Objekt zurückgibt:
      const finalReturn = { data: [{ id: 'a1', user_id: 'user-1' }], error: null, count: 1 }
      orderMock.mockResolvedValueOnce(finalReturn)

      const req = makeRequest('GET', '/api/agents')
      const res = await AgentsRoute.GET(req)
      const json = await res.json()

      expect(res.status).toBe(200)
      expect(json.agents).toEqual([{ id: 'a1', user_id: 'user-1' }])
      expect(json.total).toBe(1)

      // Sicherstellen, dass Owner-Filter aufgerufen wurde
      expect(eqMock).toHaveBeenCalledWith('user_id', 'user-1')
    })

    test('200 mit Pagination page/pageSize', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      // Kette inkl. range
      selectMock.mockReturnValue({ eq: eqMock, order: orderMock })
      eqMock.mockReturnValue({ select: selectMock, order: orderMock })
      orderMock.mockReturnValue({ select: selectMock, eq: eqMock, range: rangeMock })
      rangeMock.mockResolvedValueOnce({ data: [], error: null, count: 0 })

      const req = makeRequest('GET', '/api/agents?page=2&pageSize=10')
      const res = await AgentsRoute.GET(req)
      const json = await res.json()

      expect(res.status).toBe(200)
      expect(json.page).toBe(2)
      expect(json.pageSize).toBe(10)
    })

    test('500 bei DB-Fehler', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      selectMock.mockReturnValue({ eq: eqMock, order: orderMock })
      eqMock.mockReturnValue({ select: selectMock, order: orderMock })
      orderMock.mockResolvedValueOnce({ data: null, error: { message: 'db down' }, count: null })

      const req = makeRequest('GET', '/api/agents')
      const res = await AgentsRoute.GET(req)
      const body = await res.json()

      expect(res.status).toBe(500)
      expect(body.error).toBe('Database Error')
    })
  })

  describe('POST /api/agents', () => {
    test('401 wenn keine Session vorhanden', async () => {
      authGetSessionMock.mockResolvedValueOnce({ data: { session: null }, error: null })

      const req = makeRequest('POST', '/api/agents', {
        name: 'x',
        system_prompt: 'y'.repeat(60),
        voice: 'z',
        language: 'de-DE',
      })
      const res = await AgentsRoute.POST(req)
      const body = await res.json()

      expect(res.status).toBe(401)
      expect(body.error).toBe('Unauthorized')
    })

    test('400 bei Zod-Validierung (z. B. zu kurzer system_prompt)', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      const req = makeRequest('POST', '/api/agents', {
        name: 'Ein Agent',
        system_prompt: 'zu kurz',
        voice: 'default',
        language: 'de-DE',
      })
      const res = await AgentsRoute.POST(req)
      const body = await res.json()

      expect(res.status).toBe(400)
      expect(body.error).toBe('Validation Error')
      expect(Array.isArray(body.issues)).toBe(true)
    })

    test('201 bei erfolgreichem Insert', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      // Insert-Kette
      insertMock.mockReturnValue({ select: () => ({ single: () => Promise.resolve({ data: { id: 'a1' }, error: null }) }) })
      fromMock.mockReturnValue({ insert: insertMock, select: selectMock })

      const req = makeRequest('POST', '/api/agents', {
        name: 'Test Agent',
        system_prompt: 'x'.repeat(60),
        voice: 'default',
        language: 'de-DE',
        user_id: 'böse', // wird serverseitig gelöscht
      })

      const res = await AgentsRoute.POST(req)
      const body = await res.json()

      expect(res.status).toBe(201)
      expect(body.agent).toEqual({ id: 'a1' })
    })

    test('500 bei DB-Fehler beim Insert', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      insertMock.mockReturnValue({
        select: () => ({
          single: () => Promise.resolve({ data: null, error: { message: 'db error' } }),
        }),
      })
      fromMock.mockReturnValue({ insert: insertMock, select: selectMock })

      const req = makeRequest('POST', '/api/agents', {
        name: 'Test Agent',
        system_prompt: 'x'.repeat(60),
        voice: 'default',
        language: 'de-DE',
      })

      const res = await AgentsRoute.POST(req)
      const body = await res.json()

      expect(res.status).toBe(500)
      expect(body.error).toBe('Database Error')
    })
  })
})