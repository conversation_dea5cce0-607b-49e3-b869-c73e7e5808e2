/**
 * Tests für /api/agents/[id] (GET/PUT/DELETE)
 * - 401 Unauthenticated
 * - 400 Bad Request bei ungültiger UUID
 * - 404 Not Found wenn Agent nicht existiert (ensureOwnership)
 * - 403 Forbidden wenn Agent nicht dem Nutzer gehört (ensureOwnership)
 * - PUT: 400 Zod-Validierungsfehler
 * - PUT: 200 Erfolg mit Update
 * - DELETE: 200 Erfolg
 * - DB-Fehler: 500
 */

import * as authHelpers from '@supabase/auth-helpers-nextjs'
import * as AgentIdRoute from '../../../app/api/agents/[id]/route'

function makeRequest(method: 'GET' | 'PUT' | 'DELETE', url: string, body?: any): Request {
  const init: RequestInit = { method, headers: new Headers() }
  if (body !== undefined) {
    init.body = JSON.stringify(body)
    ;(init.headers as any).set?.('Content-Type', 'application/json')
  }
  return new Request(new URL(url, 'http://localhost').toString(), init)
}

jest.mock('@supabase/auth-helpers-nextjs', () => {
  const actual = jest.requireActual('@supabase/auth-helpers-nextjs')

  const selectMock = jest.fn()
  const eqMock = jest.fn()
  const updateMock = jest.fn()
  const deleteMock = jest.fn()
  const singleMock = jest.fn()

  const fromMock = jest.fn(() => ({
    select: selectMock,
    eq: eqMock,
    update: updateMock,
    delete: deleteMock,
    single: singleMock,
  }))

  const authGetSessionMock = jest.fn()

  const supabaseClientMock = {
    auth: {
      getSession: authGetSessionMock,
    },
    from: fromMock,
  }

  return {
    ...actual,
    createRouteHandlerClient: jest.fn(() => supabaseClientMock),
    __mocks: {
      supabaseClientMock,
      fromMock,
      selectMock,
      eqMock,
      updateMock,
      deleteMock,
      singleMock,
      authGetSessionMock,
    },
  }
})

const {
  __mocks: {
    fromMock,
    selectMock,
    eqMock,
    updateMock,
    deleteMock,
    singleMock,
    authGetSessionMock,
  },
} = authHelpers as any

describe.skip('/api/agents/[id]', () => {
  beforeEach(() => {
    jest.resetAllMocks()
  })

  describe('GET /api/agents/[id]', () => {
    test('401 wenn keine Session vorhanden', async () => {
      authGetSessionMock.mockResolvedValueOnce({ data: { session: null }, error: null })

      const req = makeRequest('GET', '/api/agents/00000000-0000-0000-0000-000000000000')
      const res = await AgentIdRoute.GET(req, { params: { id: '00000000-0000-0000-0000-000000000000' } })
      const body = await res.json()

      expect(res.status).toBe(401)
      expect(body.error).toBe('Unauthorized')
    })

    test('400 bei ungültiger UUID', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      const req = makeRequest('GET', '/api/agents/not-a-uuid')
      const res = await AgentIdRoute.GET(req, { params: { id: 'not-a-uuid' } })
      const body = await res.json()

      expect(res.status).toBe(400)
      expect(body.error).toBe('Bad Request')
    })

    test('404 wenn Agent nicht existiert (ensureOwnership)', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      // ensureOwnership macht: select('id, user_id').eq('id', agentId).single()
      // Wir simulieren error | kein data
      fromMock.mockReturnValueOnce({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: null, error: { message: 'not found' } }),
          }),
        }),
      })

      const req = makeRequest('GET', '/api/agents/00000000-0000-0000-0000-000000000000')
      const res = await AgentIdRoute.GET(req, { params: { id: '00000000-0000-0000-0000-000000000000' } })
      const body = await res.json()

      expect(res.status).toBe(404)
      expect(body.error).toBe('Not Found')
    })

    test('403 wenn Agent nicht dem Nutzer gehört', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      // ensureOwnership fetch
      fromMock.mockReturnValueOnce({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: { id: 'a1', user_id: 'other' }, error: null }),
          }),
        }),
      })

      const req = makeRequest('GET', '/api/agents/00000000-0000-0000-0000-000000000000')
      const res = await AgentIdRoute.GET(req, { params: { id: '00000000-0000-0000-0000-000000000000' } })
      const body = await res.json()

      expect(res.status).toBe(403)
      expect(body.error).toBe('Forbidden')
    })

    test('200 bei Erfolg', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      // ensureOwnership ok
      fromMock.mockReturnValueOnce({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: { id: 'a1', user_id: 'user-1' }, error: null }),
          }),
        }),
      })

      // eigentliche Datenabfrage
      fromMock.mockReturnValueOnce({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: { id: 'a1', user_id: 'user-1' }, error: null }),
          }),
        }),
      })

      const req = makeRequest('GET', '/api/agents/00000000-0000-0000-0000-000000000000')
      const res = await AgentIdRoute.GET(req, { params: { id: '00000000-0000-0000-0000-000000000000' } })
      const body = await res.json()

      expect(res.status).toBe(200)
      expect(body.agent).toEqual({ id: 'a1', user_id: 'user-1' })
    })
  })

  describe('PUT /api/agents/[id]', () => {
    test('400 bei ungültiger UUID', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      const req = makeRequest('PUT', '/api/agents/not-a-uuid', { name: 'X' })
      const res = await AgentIdRoute.PUT(req, { params: { id: 'not-a-uuid' } })
      const body = await res.json()

      expect(res.status).toBe(400)
      expect(body.error).toBe('Bad Request')
    })

    test('400 Zod, wenn kein Feld gesetzt', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      // ensureOwnership ok
      fromMock.mockReturnValueOnce({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: { id: 'a1', user_id: 'user-1' }, error: null }),
          }),
        }),
      })

      const req = makeRequest('PUT', '/api/agents/00000000-0000-0000-0000-000000000000', {})
      const res = await AgentIdRoute.PUT(req, { params: { id: '00000000-0000-0000-0000-000000000000' } })
      const body = await res.json()

      expect(res.status).toBe(400)
      expect(body.error).toBe('Validation Error')
    })

    test('200 bei Erfolg (Update)', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      // ensureOwnership ok
      fromMock.mockReturnValueOnce({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: { id: 'a1', user_id: 'user-1' }, error: null }),
          }),
        }),
      })

      // Update-Kette
      fromMock.mockReturnValueOnce({
        update: () => ({
          eq: () => ({
            select: () => ({
              single: () => Promise.resolve({ data: { id: 'a1', name: 'Neu' }, error: null }),
            }),
          }),
        }),
      })

      const req = makeRequest('PUT', '/api/agents/00000000-0000-0000-0000-000000000000', { name: 'Neu' })
      const res = await AgentIdRoute.PUT(req, { params: { id: '00000000-0000-0000-0000-000000000000' } })
      const body = await res.json()

      expect(res.status).toBe(200)
      expect(body.agent).toEqual({ id: 'a1', name: 'Neu' })
    })

    test('500 bei DB-Fehler im Update', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      // ensureOwnership ok
      fromMock.mockReturnValueOnce({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: { id: 'a1', user_id: 'user-1' }, error: null }),
          }),
        }),
      })

      // Update-Kette mit Fehler
      fromMock.mockReturnValueOnce({
        update: () => ({
          eq: () => ({
            select: () => ({
              single: () => Promise.resolve({ data: null, error: { message: 'db error' } }),
            }),
          }),
        }),
      })

      const req = makeRequest('PUT', '/api/agents/00000000-0000-0000-0000-000000000000', { name: 'Neu' })
      const res = await AgentIdRoute.PUT(req, { params: { id: '00000000-0000-0000-0000-000000000000' } })
      const body = await res.json()

      expect(res.status).toBe(500)
      expect(body.error).toBe('Database Error')
    })
  })

  describe('DELETE /api/agents/[id]', () => {
    test('400 bei ungültiger UUID', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      const req = makeRequest('DELETE', '/api/agents/not-a-uuid')
      const res = await AgentIdRoute.DELETE(req, { params: { id: 'not-a-uuid' } })
      const body = await res.json()

      expect(res.status).toBe(400)
      expect(body.error).toBe('Bad Request')
    })

    test('200 bei Erfolg (Delete)', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      // ensureOwnership ok
      fromMock.mockReturnValueOnce({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: { id: 'a1', user_id: 'user-1' }, error: null }),
          }),
        }),
      })

      // Delete-Kette
      fromMock.mockReturnValueOnce({
        delete: () => ({
          eq: () => Promise.resolve({ error: null }),
        }),
      })

      const req = makeRequest('DELETE', '/api/agents/00000000-0000-0000-0000-000000000000')
      const res = await AgentIdRoute.DELETE(req, { params: { id: '00000000-0000-0000-0000-000000000000' } })
      const body = await res.json()

      expect(res.status).toBe(200)
      expect(body.success).toBe(true)
    })

    test('500 bei DB-Fehler (Delete)', async () => {
      authGetSessionMock.mockResolvedValueOnce({
        data: { session: { user: { id: 'user-1' } } },
        error: null,
      })

      // ensureOwnership ok
      fromMock.mockReturnValueOnce({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: { id: 'a1', user_id: 'user-1' }, error: null }),
          }),
        }),
      })

      // Delete-Kette mit Fehler
      fromMock.mockReturnValueOnce({
        delete: () => ({
          eq: () => Promise.resolve({ error: { message: 'db error' } }),
        }),
      })

      const req = makeRequest('DELETE', '/api/agents/00000000-0000-0000-0000-000000000000')
      const res = await AgentIdRoute.DELETE(req, { params: { id: '00000000-0000-0000-0000-000000000000' } })
      const body = await res.json()

      expect(res.status).toBe(500)
      expect(body.error).toBe('Database Error')
    })
  })
})