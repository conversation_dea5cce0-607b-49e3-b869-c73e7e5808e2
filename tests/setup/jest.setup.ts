import '@testing-library/jest-dom'
import 'whatwg-url'

;(global as any).URL = (global as any).URL ?? (require('whatwg-url') as any).URL
;(global as any).URLSearchParams =
  (global as any).URLSearchParams ?? (require('whatwg-url') as any).URLSearchParams

if (!(global as any).Headers) (global as any).Headers = require('undici').Headers
if (!(global as any).Request) (global as any).Request = require('undici').Request
if (!(global as any).Response) (global as any).Response = require('undici').Response
if (!(global as any).FormData) (global as any).FormData = require('undici').FormData

jest.mock('next/navigation', () => {
  const actual = jest.requireActual('next/navigation')
  return {
    ...actual,
    useRouter: () => ({
      push: jest.fn(),
      replace: jest.fn(),
      refresh: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn(),
    }),
    usePathname: () => '/test-path',
    useSearchParams: () => new URLSearchParams(''),
  }
})

jest.mock('@supabase/supabase-js', () => {
  const createClient = () => ({
    auth: {
      signInWithPassword: jest.fn(async () => ({ data: {}, error: null })),
      signOut: jest.fn(async () => ({ error: null })),
    },
    from: jest.fn(() => ({ select: jest.fn(), insert: jest.fn(), update: jest.fn(), delete: jest.fn() })),
  })
  return { createClient }
})

jest.mock('@supabase/auth-helpers-nextjs', () => {
  const authGetSessionMock = jest.fn()
  const fromMock = jest.fn(() => ({
    select: jest.fn(), insert: jest.fn(), update: jest.fn(), delete: jest.fn(), eq: jest.fn(), order: jest.fn(), range: jest.fn(), single: jest.fn(),
  }))
  const client = { auth: { getSession: authGetSessionMock }, from: fromMock }
  return {
    createServerClient: jest.fn(() => client),
    createClientComponentClient: jest.fn(() => client),
    createRouteHandlerClient: jest.fn(() => client),
    __mocks: { authGetSessionMock, fromMock },
  }
})

jest.mock('jose', () => ({
  jwtVerify: jest.fn(async () => ({ payload: {}, protectedHeader: {} })),
}))

jest.mock('next/server', () => {
  return {
    NextResponse: {
      json: (body: any, init?: ResponseInit) => new Response(JSON.stringify(body), {
        status: init?.status,
        headers: { 'content-type': 'application/json', ...(init?.headers || {}) },
      }),
    },
  }
})

export {}
