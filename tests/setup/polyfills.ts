/**
 * Node/Jest Polyfills für Web/APIs, die in Next.js/whatwg-url erwartet werden.
 */

// TextEncoder/TextDecoder (benötigt von whatwg-url)
import { TextEncoder, TextDecoder } from 'util'
;(global as any).TextEncoder = TextEncoder
;(global as any).TextDecoder = TextDecoder as any

// Minimales fetch/Request/Response/Headers Polyfill über node:module (Node 18+ hat global fetch)
// Da Jest-Umgebungen variieren, setzen wir nur, wenn nicht vorhanden.
if (!(global as any).fetch) {
  // Einfaches Fallback auf eine no-op Fetch-Implementierung, da unsere API-Tests NextRequest direkt nutzen.
  ;(global as any).fetch = async () => {
    throw new Error('fetch not available in test environment')
  }
  ;(global as any).Request = class {}
  ;(global as any).Response = class {}
  ;(global as any).Headers = class {}
}