Architektur-Empfehlung für 3 Repos (Landing, Docs, Dashboard) und Backend-Positionierung

Kurzantwort
Ja, das lokale Next.js-API-Backend in diesem Dashboard-Repo ist für Prototyping/Edge-Fälle ok, aber für drei getrennte Repos ist ein zentralisiertes, wiederverwendbares Backend empfehlenswert. Nutze Supabase als zentrale Daten-/Auth-Schicht und exponiere eine dedizierte BFF/API-Schicht (z. B. eine eigenständige Next.js-/Node-API oder Supabase Edge Functions). Das Dashboard greift dann auf diese zentrale API zu; Landing und Docs bleiben statisch und nutzen nur Auth/Tracking.

Empfohlene Zielstruktur
- Repo A: landing-web (Static/SSG, kein Server)
  - Nur Marketing/Signup-Flow; bei Auth-Callouts an Supabase (Hosted Auth) oder zentrale Auth-UI-Route (z. B. auth.domain.com)
- Repo B: docs-web (Static/SSG mit MDX)
  - Keine Business-APIs, nur Lesedocs. Optional minimaler Telemetry-Endpunkt der zentralen API
- Repo C: dashboard-web (dieses Repo)
  - Reines Frontend + BFF-Client. Keine persistente Businesslogik in app/api/* auf Dauer
  - Zugriff auf zentrale API über HTTP (z. B. https://api.domain.com)
- Repo D: api-backend (neues, zentrales Backend)
  - Schnittstellen: REST (und/oder GraphQL)
  - Auth: Supabase JWT Verifikation, RLS in DB erzwungen
  - Ort: Vercel Serverless, Supabase Edge Functions oder eigenständiger Node Service
  - Verantwortlich für: Agents CRUD, Monitoring, History, Analytics, RAG, Telefonie-Proxys
- Daten-/Auth-Layer: Supabase Projekt (ein zentraler Mandant)
  - DB, RLS, Storage, Realtime
  - Edge Functions für spezielle, latenzkritische Endpunkte und Webhooks
  - Optional: Multitenancy/Org falls nötig

Warum so?
- Single Source of Truth: Ein Backend für alle drei Frontends reduziert Duplikate und Inkonsistenzen
- Sicherheit: RLS in Supabase + signierte JWTs; zentrale API kann Rate Limits, Audit Logs, Policies erzwingen
- Deploy/Scaling: Unabhängige Skalierung der API von Frontends; Edge Functions für Hot Paths
- Wartbarkeit: Klare Verantwortlichkeiten; Frontends bleiben „dünn“

Konkreter Migrationspfad von aktuellem Stand
1) Beibehalten für jetzt
- Die bereits implementierten Next.js API Routes im Dashboard (z. B. [app/api/agents/route.ts](app/api/agents/route.ts:1)) dienen als temporärer BFF, um Features zügig Ende-zu-Ende fertigzustellen.

2) Extraktion in zentrales Backend
- Erzeuge Repo D (api-backend) mit identischen Endpunkten wie aktuell im Dashboard BFF:
  - /agents (GET/POST), /agents/:id (GET/PUT/DELETE)
  - Später: /monitoring, /history, /analytics, /rag, /telephony
- Teile aus den Dashboard-API-Routen in Services extrahieren und in das API-Repo verschieben:
  - Zod-Schemas, Validierungen, Datenzugriff
  - Session-/JWT-Check (Supabase Admin Client + JWT Verify)
- Supabase: Migrationen bleiben zentral. Dieses Repo kann die SQL-Migrationen behalten, aber idealerweise wandern sie ins API-Repo (oder in ein dediziertes infra Repo), um Source of Truth zu zentralisieren.

3) Dashboard an zentrale API anbinden
- Ersetze interne fetches zu /api/* durch https://api.domain.com/*
- Verwende Supabase Session JWT im Authorization Header (Bearer) gegenüber der zentralen API
- Entferne nach stabiler Migration die lokalen API-Routen aus dem Dashboard

4) Domänen/CI/CD
- Vercel:
  - landing.domain.com -> Repo A
  - docs.domain.com -> Repo B
  - app.domain.com -> Repo C (Dashboard)
  - api.domain.com -> Repo D (API)
- Supabase:
  - Ein Projekt; .env Files in allen Repos mit denselben Kern-Variablen
- CI:
  - Jede Pipeline deployt unabhängig; API hat eigene Tests, Load-/Contract-Tests

5) Edge-Fälle
- Realtime/WS: Für Monitoring/Live-Funktionen direkt gegen Supabase Realtime Channels oder über API-Gateway (abhängig von Autorisierungsanforderungen)
- RAG: Upload/Indexing über API (Edge Functions geeignet) + Storage in Supabase
- Telefonie: Externe Provider-Callbacks (Webhooks) an die zentrale API/Edge Functions, nicht an Frontends

Antwort auf deine Frage „Ist es richtig so?“
- Kurzfristig: Ja, für schnelles Weiterbauen ist die aktuelle API im Dashboard-Repo praktikabel.
- Langfristig: Besser ein zentrales Backend (Repo D) und das Dashboard nur als Client. Dadurch sind Landing/Docs/Dashboard sauber entkoppelt, die Geschäftslogik zentralisiert und Security/Operations verbessert.

Empfohlene nächsten Schritte
- Definiere das API-Backendrepo (Repo D) mit Basisgerüst: Auth/JWT Middleware, Agents CRUD, Fehler-Handling, Zod
- Hebe die vorhandene Agents-Route aus dem Dashboard in Repo D (1:1), stelle die Supabase-Migrationen dorthin um
- Stelle das Dashboard um auf api.domain.com (Feature Flag/Env, um Übergang zu erleichtern)
- Belasse temporär die Dashboard-API-Routen bis die Umschaltung bestätigt ist; danach entfernen

Damit ist eine klare, skalierbare Ausrichtung festgelegt, die eure Dreiteilung (Landing, Docs, Dashboard) optimal unterstützt und das Backend als gemeinsame Plattform positioniert.